# 批量智能一键成片API参数修复

## 🚨 问题分析

### 错误信息
```
批量智能一键成片任务提交失败: 参数无效，请检查输入配置、剪辑配置和输出配置的JSON格式是否正确
```

### 根本原因
1. **OutputConfig中MediaURL格式错误** - 使用了占位符而不是真实的OSS URL
2. **MediaGroupArray可能为空** - 没有验证媒体组是否包含有效素材
3. **配置参数缺失** - 某些必需的API参数没有提供默认值
4. **JSON格式问题** - 参数序列化可能产生无效JSON

## 🔧 修复内容

### 1. OutputConfig修复 ✅

**问题**: MediaURL使用了无效的占位符格式
```typescript
// 修复前 - 错误的URL格式
MediaURL: `oss://your-bucket/output/batch_video_{index}.mp4`

// 修复后 - 正确的OSS HTTP URL格式
MediaURL: `http://matrix-video-output.oss-cn-shanghai.aliyuncs.com/batch-videos/${timestamp}/video_{index}.mp4`
```

**完整修复**:
```typescript
const buildApiOutputConfig = (config: typeof props.modelValue) => {
  const [width, height] = config.resolution.split('x').map(Number);
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  
  return {
    Count: config.count,
    MaxDuration: config.maxDuration,
    Width: width,
    Height: height,
    Video: {
      Crf: config.quality
    },
    GeneratePreviewOnly: false,
    // 使用正确的OSS URL格式，包含{index}占位符
    MediaURL: `http://matrix-video-output.oss-cn-shanghai.aliyuncs.com/batch-videos/${timestamp}/video_{index}.mp4`,
    FileName: `batch_video_${timestamp}_{index}.mp4`
  };
};
```

### 2. InputConfig验证修复 ✅

**问题**: 没有验证MediaGroupArray的有效性
```typescript
// 修复前 - 直接使用可能为空的数组
const config: any = {
  MediaGroupArray: apiConfigs.value.mediaGroups
};

// 修复后 - 完整验证
const mediaGroupArray = apiConfigs.value.mediaGroups || [];
if (mediaGroupArray.length === 0) {
  throw new Error('至少需要一个媒体组');
}

const validMediaGroups = mediaGroupArray.filter(group => 
  group.MediaArray && group.MediaArray.length > 0
);

if (validMediaGroups.length === 0) {
  throw new Error('每个媒体组至少需要一个素材文件');
}

const config: any = {
  MediaGroupArray: validMediaGroups
};
```

### 3. EditingConfig默认值修复 ✅

**问题**: 缺少必需的配置参数
```typescript
// 修复前 - 直接返回可能不完整的配置
const buildEditingConfig = () => {
  return apiConfigs.value.editingConfig;
};

// 修复后 - 提供完整的默认配置
const buildEditingConfig = () => {
  const config = apiConfigs.value.editingConfig || {};
  
  const editingConfig = {
    MediaConfig: config.MediaConfig || {
      Volume: 0.5
    },
    SpeechConfig: config.SpeechConfig || {
      Volume: 0.8,
      SpeechRate: 1.0
    },
    BackgroundMusicConfig: config.BackgroundMusicConfig || {
      Volume: 0.3
    },
    ProcessConfig: config.ProcessConfig || {
      SingleShotDuration: 3,
      AllowVfxEffect: false,
      AllowTransition: false,
      AlignmentMode: "AutoSpeed",
      ImageDuration: 2
    },
    ...config
  };
  
  return editingConfig;
};
```

### 4. 提交流程优化 ✅

**增强错误处理和参数验证**:
```typescript
const submitTask = async () => {
  submitting.value = true;
  try {
    // 基础验证
    if (!taskInfo.value.taskName?.trim()) {
      ElMessage.warning('请输入任务名称');
      return;
    }
    
    // 构建配置参数（带错误处理）
    let inputConfigObj, editingConfigObj, outputConfigObj;
    
    try {
      inputConfigObj = buildInputConfig();
      editingConfigObj = buildEditingConfig();
      outputConfigObj = buildOutputConfig();
    } catch (buildError: any) {
      ElMessage.error(`配置参数错误: ${buildError?.message || '未知错误'}`);
      return;
    }

    // JSON格式验证
    const inputConfig = JSON.stringify(inputConfigObj);
    const editingConfig = JSON.stringify(editingConfigObj);
    const outputConfig = JSON.stringify(outputConfigObj);

    try {
      JSON.parse(inputConfig);
      JSON.parse(editingConfig);
      JSON.parse(outputConfig);
    } catch (jsonError) {
      ElMessage.error('配置参数JSON格式错误');
      return;
    }

    // 详细日志输出
    console.log('📤 提交任务数据:');
    console.log('InputConfig:', inputConfigObj);
    console.log('EditingConfig:', editingConfigObj);
    console.log('OutputConfig:', outputConfigObj);

    // API调用
    const response = await submitBatchMediaProducingJob(
      inputConfig, editingConfig, outputConfig, userData, templateConfig
    );
    
    ElMessage.success(`批量成片任务提交成功！任务ID: ${response.JobId}`);
    
  } catch (error: any) {
    // 详细错误处理
    let errorMessage = '任务提交失败，请重试';
    if (error?.message) {
      if (error.message.includes('参数无效')) {
        errorMessage = '参数配置无效，请检查媒体文件、输出配置等参数';
      } else if (error.message.includes('JSON格式')) {
        errorMessage = 'JSON格式错误，请检查配置参数';
      } else {
        errorMessage = `提交失败: ${error.message}`;
      }
    }
    ElMessage.error(errorMessage);
  } finally {
    submitting.value = false;
  }
};
```

## 📋 API参数格式要求

### InputConfig 必需格式
```json
{
  "MediaGroupArray": [
    {
      "GroupName": "媒体组1",
      "MediaArray": [
        "****9d46c886b45481030f6e****",
        "****c886810b4549d4630f6e****"
      ],
      "SplitMode": "NoSplit",
      "Volume": 1.0
    }
  ],
  "TitleArray": ["标题1", "标题2"],
  "SpeechTextArray": ["旁白内容1", "旁白内容2"]
}
```

### EditingConfig 必需格式
```json
{
  "MediaConfig": {
    "Volume": 0.5
  },
  "SpeechConfig": {
    "Volume": 0.8,
    "SpeechRate": 1.0
  },
  "BackgroundMusicConfig": {
    "Volume": 0.3
  },
  "ProcessConfig": {
    "SingleShotDuration": 3,
    "AllowVfxEffect": false,
    "AllowTransition": false,
    "AlignmentMode": "AutoSpeed",
    "ImageDuration": 2
  }
}
```

### OutputConfig 必需格式
```json
{
  "Count": 10,
  "MaxDuration": 15,
  "Width": 1080,
  "Height": 1920,
  "Video": {
    "Crf": 27
  },
  "GeneratePreviewOnly": false,
  "MediaURL": "http://matrix-video-output.oss-cn-shanghai.aliyuncs.com/batch-videos/2025-07-15T10-30-00-000Z/video_{index}.mp4",
  "FileName": "batch_video_2025-07-15T10-30-00-000Z_{index}.mp4"
}
```

## 🧪 测试验证

### 测试步骤:
1. **添加媒体素材** - 确保至少有一个媒体组包含素材
2. **配置参数** - 设置标题、旁白、剪辑参数等
3. **选择模板** - 选择一个可用的模板
4. **提交任务** - 点击提交按钮
5. **查看日志** - 检查控制台输出的详细参数

### 预期结果:
- ✅ 参数验证通过
- ✅ JSON格式正确
- ✅ API调用成功
- ✅ 返回JobId

### 调试信息:
修复后会输出详细的调试信息:
```
📤 提交任务数据:
InputConfig: { MediaGroupArray: [...], TitleArray: [...] }
EditingConfig: { MediaConfig: {...}, SpeechConfig: {...} }
OutputConfig: { Count: 10, Width: 1080, Height: 1920, ... }
✅ 任务提交成功: { RequestId: "...", JobId: "..." }
```

## 🔍 故障排除

### 如果仍然出现"参数无效"错误:

1. **检查媒体文件** - 确保MediaArray中的媒体ID有效
2. **检查OSS配置** - 确认MediaURL中的OSS bucket存在且有权限
3. **检查分辨率** - 确认Width和Height是有效的数值
4. **检查时长** - 确认MaxDuration在合理范围内(5-300秒)

### 常见错误及解决方案:

| 错误信息 | 可能原因 | 解决方案 |
|---------|---------|---------|
| MediaGroupArray为空 | 没有添加媒体素材 | 添加至少一个媒体文件 |
| MediaURL格式错误 | OSS URL格式不正确 | 使用http://开头的完整URL |
| JSON格式错误 | 参数序列化失败 | 检查参数中是否有特殊字符 |
| 模板不存在 | 选择了无效的模板 | 重新选择可用模板 |

## 📈 预期效果

修复完成后，批量智能一键成片功能应该能够:

1. **正确验证参数** - 在提交前验证所有必需参数
2. **生成有效JSON** - 确保所有配置参数格式正确
3. **成功提交任务** - API调用返回JobId
4. **提供详细反馈** - 清晰的错误信息和成功提示
5. **支持调试** - 详细的控制台日志输出

## 🚀 部署建议

1. **测试环境验证** - 先在测试环境完整测试流程
2. **参数模板** - 准备标准的参数配置模板
3. **错误监控** - 监控API调用的成功率和错误类型
4. **用户指导** - 提供清晰的使用说明和错误处理指导

修复完成后，用户应该能够顺利提交批量智能一键成片任务，并获得清晰的反馈信息。
