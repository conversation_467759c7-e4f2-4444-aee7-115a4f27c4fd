# 数据绑定测试指南

## 测试目的
验证批量智能一键成片功能中的数据绑定是否正常工作，确保所有配置都能动态调整并正确传递给API。

## 测试步骤

### 1. 基础数据绑定测试

#### 1.1 任务信息测试
- [ ] 在"任务名称"输入框中输入文字，检查是否实时更新
- [ ] 在"任务描述"输入框中输入文字，检查是否实时更新
- [ ] 点击"预览API参数"，检查TaskInfoForm中的数据是否正确传递到API参数中

#### 1.2 剪辑配置测试
- [ ] 调整"视频音量"滑块，观察配置状态监控中的数值变化
- [ ] 调整"旁白音量"滑块，观察配置状态监控中的数值变化
- [ ] 切换"背景音乐"开关，观察监控中的状态变化
- [ ] 调整"背景音乐音量"，观察数值变化
- [ ] 切换各种开关（AI字幕、镜头切换特效等），观察状态变化

#### 1.3 输出配置测试
- [ ] 调整"生成数量"，观察配置状态监控中的数值变化
- [ ] 调整"视频时长"，观察配置状态监控中的数值变化
- [ ] 切换"分辨率"选项，观察配置状态监控中的数值变化
- [ ] 调整"视频质量(CRF)"滑块，观察配置状态监控中的数值变化

### 2. 配置预设测试

#### 2.1 预设应用测试
- [ ] 点击"短视频模式"按钮，检查所有配置是否按预期更新
- [ ] 点击"长视频模式"按钮，检查所有配置是否按预期更新
- [ ] 点击"高质量模式"按钮，检查所有配置是否按预期更新
- [ ] 点击"快速模式"按钮，检查所有配置是否按预期更新

#### 2.2 预设配置验证
**短视频模式预期配置：**
- 视频音量: 60%
- 旁白音量: 85%
- 背景音乐: 开启，25%
- 生成数量: 20个
- 视频时长: 15秒
- 分辨率: 1080x1920
- 视频质量: CRF 25

**长视频模式预期配置：**
- 视频音量: 55%
- 旁白音量: 80%
- 背景音乐: 开启，35%
- 生成数量: 5个
- 视频时长: 120秒
- 分辨率: 1920x1080
- 视频质量: CRF 20

### 3. API参数生成测试

#### 3.1 预览功能测试
- [ ] 点击"配置管理" → "预览API参数"
- [ ] 检查控制台输出的API参数是否包含所有配置项
- [ ] 验证参数格式是否符合阿里云ICE官方文档要求

#### 3.2 参数格式验证
检查生成的API参数是否包含以下必要字段：

**InputConfig:**
```json
{
  "MediaGroupArray": [...],
  "TitleArray": [...],
  "SpeechTextArray": [...]
}
```

**EditingConfig:**
```json
{
  "MediaConfig": {"Volume": ...},
  "SpeechConfig": {"Volume": ..., "AsrConfig": ...},
  "BackgroundMusicConfig": {"Volume": ...},
  "ProcessConfig": {...}
}
```

**OutputConfig:**
```json
{
  "MediaURL": "...",
  "Count": ...,
  "MaxDuration": ...,
  "Width": ...,
  "Height": ...,
  "Video": {"Crf": ...}
}
```

### 4. 配置管理测试

#### 4.1 保存配置测试
- [ ] 设置一组自定义配置
- [ ] 点击"配置管理" → "保存当前配置"
- [ ] 检查是否显示保存成功消息

#### 4.2 加载配置测试
- [ ] 修改当前配置
- [ ] 点击"配置管理" → 选择之前保存的配置
- [ ] 检查配置是否正确恢复

#### 4.3 重置功能测试
- [ ] 修改各种配置
- [ ] 点击"重置表单"按钮
- [ ] 检查所有配置是否恢复到合理的默认值

### 5. 实时监控测试

#### 5.1 监控显示测试
- [ ] 修改任意配置项
- [ ] 观察"配置状态监控"区域是否实时更新
- [ ] 检查显示的数值是否与实际配置一致

#### 5.2 监控准确性测试
- [ ] 设置视频音量为75%，检查监控显示是否为"75%"
- [ ] 关闭背景音乐，检查监控显示是否为"关闭"
- [ ] 设置生成数量为50，检查监控显示是否为"50个"

## 预期结果

### 正常工作的标志：
1. **实时响应**: 所有配置项的修改都能立即反映在界面上
2. **数据一致性**: 配置状态监控显示的数值与实际设置一致
3. **API参数正确**: 预览的API参数包含所有配置项且格式正确
4. **预设功能**: 配置预设能正确应用到所有相关配置项
5. **持久化**: 保存和加载配置功能正常工作

### 问题排查：
如果发现数据绑定不工作，请检查：
1. Vue组件的v-model绑定是否正确
2. 父子组件间的props和emit是否正确传递
3. 响应式数据的ref/reactive是否正确使用
4. 配置构建方法是否正确读取响应式数据

## 测试报告模板

```
测试日期: ____
测试人员: ____

基础数据绑定: ✅/❌
配置预设功能: ✅/❌
API参数生成: ✅/❌
配置管理功能: ✅/❌
实时监控功能: ✅/❌

发现问题:
1. ________________
2. ________________
3. ________________

总体评价: ✅通过 / ❌需要修复
```

## 调试技巧

1. **使用浏览器开发者工具**:
   - 按F12打开开发者工具
   - 在Console标签页查看API参数输出
   - 在Vue DevTools中查看组件状态

2. **检查网络请求**:
   - 在Network标签页查看API请求
   - 验证请求参数是否正确

3. **Vue响应式调试**:
   - 使用Vue DevTools查看组件数据
   - 检查响应式数据的变化

通过以上测试，可以确保数据绑定功能完全正常工作，所有配置都能动态调整并正确传递给后端API。
