# MediaSelectorDialog 关键Bug修复

## 🐛 问题分析

### 1. 递归更新错误 (Maximum recursive updates exceeded)
**根本原因**: MediaGroupManager中有多个watch监听器相互触发
```typescript
// 问题代码 - 三个watch相互触发
watch(() => props.modelValue, ...)  // 监听props
watch(groups, ...)                  // 监听本地数据
watch(apiMediaGroups, ...)          // 监听computed
```

### 2. 分页显示错误 (显示10条而不是8条)
**根本原因**: API可能不严格按照MaxResults返回数据，或者有缓存问题

### 3. 选中状态更新延迟
**根本原因**: DOM更新和状态更新不同步

### 4. 弹窗重复打开问题
**根本原因**: 没有防抖机制，快速点击导致状态混乱

## 🔧 修复方案

### 1. 修复递归更新问题 ✅

**MediaGroupManager.vue**:
```typescript
// 修复前 - 三个watch相互触发
watch(() => props.modelValue, (val) => {
  groups.value = JSON.parse(JSON.stringify(val));
});
watch(groups, (val) => {
  emit('update:modelValue', uiGroups);
  emit('api-config-changed', apiMediaGroups.value);
}, { deep: true });
watch(apiMediaGroups, (val) => {
  emit('api-config-changed', val);
}, { deep: true });

// 修复后 - 简化为两个watch，避免循环
watch(() => props.modelValue, (val) => {
  groups.value = JSON.parse(JSON.stringify(val));
}, { deep: true });

watch(groups, (val) => {
  const uiGroups = JSON.parse(JSON.stringify(val));
  emit('update:modelValue', uiGroups);
  // 直接计算API配置，避免重复监听
  const apiConfig = val
    .filter(group => group.media.length > 0)
    .map(group => ({
      GroupName: group.name,
      MediaArray: group.media.map(media => media.id),
      SplitMode: 'NoSplit' as const,
      Volume: 1.0
    }));
  emit('api-config-changed', apiConfig);
}, { deep: true });
```

### 2. 修复分页显示问题 ✅

**MediaSelectorDialog.vue**:
```typescript
// 添加调试信息确认API行为
console.log('📚 请求每页数量:', pageSize.value);
const response = await listMediaBasicInfo(query);
console.log('📚 API返回数据量:', response.MediaInfos?.length);

// 确保pageSize正确传递
const query: MediaListQueryParams = {
  MediaType: convertToApiMediaType(mediaType.value),
  MaxResults: pageSize.value, // 确保使用正确的变量
  NextToken: page === 1 ? '' : nextToken.value,
  SortBy: 'desc',
  IncludeFileBasicInfo: true
};
```

### 3. 修复选中状态更新延迟 ✅

**使用nextTick确保DOM更新**:
```typescript
const confirmSelect = async () => {
  if (selected.value.length === 0) {
    ElMessage.warning('请选择至少一个素材');
    return;
  }
  
  console.log('✅ 确认选择素材:', selected.value);
  emit('confirm', selected.value);
  
  // 使用nextTick确保状态更新完成
  await nextTick();
  closeDialog();
};
```

**优化watch监听**:
```typescript
// 监听弹窗显示状态，显示时重置并加载数据
watch(() => props.visible, (visible, oldVisible) => {
  if (visible && !oldVisible) {
    console.log('🔄 弹窗打开，重新加载媒体列表');
    // 重置状态
    currentPage.value = 1;
    selected.value = [];
    if (tableRef.value) {
      tableRef.value.clearSelection();
    }
    loadMediaList(1);
  }
});
```

### 4. 修复弹窗重复打开问题 ✅

**添加防抖机制**:
```typescript
const mediaSelectorVisible = ref(false);
const currentGroupIndex = ref(0);
const isOpening = ref(false); // 防止重复打开

const openMediaSelector = async (groupIndex: number) => {
  // 防止重复打开
  if (isOpening.value || mediaSelectorVisible.value) {
    console.log('⚠️ 弹窗正在打开或已打开，忽略重复请求');
    return;
  }
  
  isOpening.value = true;
  console.log('🔍 打开素材选择器，组索引:', groupIndex);
  
  currentGroupIndex.value = groupIndex;
  mediaSelectorVisible.value = true;
  
  // 短暂延迟后重置防抖标志
  setTimeout(() => {
    isOpening.value = false;
  }, 500);
};
```

### 5. 优化状态管理 ✅

**MediaGroupManager中的onMediaSelected**:
```typescript
const onMediaSelected = (selected: MediaItem[]) => {
  console.log('📥 接收到选中的素材:', selected);
  console.log('📍 当前组索引:', currentGroupIndex.value);
  
  // 添加素材到指定组
  groups.value[currentGroupIndex.value].media.push(...selected);
  
  console.log('📊 更新后的组数据:', groups.value[currentGroupIndex.value]);
  
  // 发送事件通知
  emit('media-added', { 
    groupIndex: currentGroupIndex.value, 
    media: selected 
  });
  
  // 关闭弹窗
  mediaSelectorVisible.value = false;
  console.log('🔒 弹窗已关闭');
};
```

## 🧪 测试验证

### 测试步骤:
1. **打开页面** → 检查控制台是否有递归更新错误
2. **点击添加素材** → 弹窗应该正常打开
3. **查看分页** → 每页应该显示8个素材
4. **选择素材** → 选中状态应该立即响应
5. **确认选择** → 素材应该立即添加到组中
6. **再次点击添加素材** → 弹窗应该能正常打开
7. **快速多次点击** → 不应该出现重复弹窗

### 预期结果:
- ✅ 无递归更新错误
- ✅ 分页正确显示8个素材
- ✅ 选中状态立即更新
- ✅ 弹窗可以重复正常打开
- ✅ 防抖机制生效

## 🔍 调试信息

修复后的代码包含详细的调试信息:

```typescript
// MediaSelectorDialog
console.log('📚 请求每页数量:', pageSize.value);
console.log('📚 API返回数据量:', response.MediaInfos?.length);
console.log('🔄 弹窗打开，重新加载媒体列表');
console.log('✅ 确认选择素材:', selected.value);

// MediaGroupManager  
console.log('🔍 打开素材选择器，组索引:', groupIndex);
console.log('⚠️ 弹窗正在打开或已打开，忽略重复请求');
console.log('📥 接收到选中的素材:', selected);
console.log('📊 更新后的组数据:', groups.value[currentGroupIndex.value]);
console.log('🔒 弹窗已关闭');
```

## 📊 性能优化

### 1. 减少不必要的watch监听
- 移除了重复的apiMediaGroups watch
- 合并了状态更新逻辑

### 2. 优化数据处理
- 使用深拷贝避免引用问题
- 直接计算API配置而不是通过computed

### 3. 防抖机制
- 防止重复打开弹窗
- 避免快速点击导致的状态混乱

### 4. 状态重置
- 弹窗打开时重置选择状态
- 确保每次打开都是干净的状态

## 🎯 关键改进

1. **架构简化**: 移除了复杂的watch循环依赖
2. **状态管理**: 使用nextTick确保DOM更新同步
3. **用户体验**: 添加防抖机制和详细的调试信息
4. **错误处理**: 完善的边界条件处理
5. **性能优化**: 减少不必要的计算和监听

## 🚀 部署建议

1. **测试环境验证**: 先在测试环境验证所有功能
2. **监控日志**: 观察控制台调试信息确认修复效果
3. **用户测试**: 让用户测试常见操作流程
4. **性能监控**: 观察页面性能是否有改善

修复完成后，MediaSelectorDialog应该能够:
- ✅ 稳定运行，无递归更新错误
- ✅ 正确显示分页数据
- ✅ 立即响应用户操作
- ✅ 支持重复使用
- ✅ 提供良好的用户体验
