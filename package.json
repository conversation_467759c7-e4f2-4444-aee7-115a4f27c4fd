{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "3.8.4", "description": "数智宝", "author": "数智宝", "license": "MIT", "type": "module", "scripts": {"dev": "vite", "prod": "vite --mode production", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview"}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "repository": {"type": "git", "url": "https://gitee.com/geek-xd/ruo-yi-vue3-geek.git"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@vueuse/core": "13.4.0", "axios": "1.10.0", "bpmn-js": "^18.6.2", "bpmn-js-bpmnlint": "^0.23.0", "bpmnlint": "^11.4.4", "bpmnlint-loader": "^0.1.4", "diagram-js": "^15.3.0", "echarts": "5.6.0", "element-plus": "2.10.2", "fast-glob": "^3.3.3", "file-saver": "2.0.5", "fuse.js": "7.1.0", "highlight.js": "11.11.1", "inherits": "^2.0.4", "js-cookie": "3.0.5", "jsbarcode": "^3.12.1", "jsencrypt": "3.3.2", "min-dash": "^4.2.3", "nprogress": "0.2.0", "pinia": "3.0.3", "qrcode": "^1.5.4", "quill": "^2.0.2", "quill-better-table": "^1.2.10", "three": "0.177.0", "vform3-builds": "^3.0.10", "vkbeautify": "^0.99.3", "vue": "3.5.17", "vue-cropper": "1.1.1", "vue-router": "4.5.1"}, "devDependencies": {"@types/nprogress": "^0.2.3", "@types/three": "0.177.0", "@vitejs/plugin-vue": "5.2.4", "@vue/compiler-sfc": "3.5.17", "sass": "1.89.2", "tslib": "^2.7.0", "unplugin-auto-import": "19.3.0", "vite": "6.3.5", "vite-plugin-compression": "0.5.1", "vite-plugin-glsl": "^1.4.2", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-setup-extend": "0.4.0"}}