import request from '@/utils/request'

/**
 * 根据模版编号获取模版信息
 * @param {string} templateId - 模版ID
 * @returns {Promise}
 */
export function getTemplateById(templateId) {
  return request({
    url: '/videoEdit/getTemplateById',
    method: 'get',
    params: {
      templateId
    }
  })
}

/**
 * 根据模版合成视频
 * @param {string} templateId - 模版ID
 * @param {Object} videoMap - 视频参数映射
 * @returns {Promise}
 */
export function produceVideo(templateId, videoMap) {
  return request({
    url: '/videoEdit/produceVideo',
    method: 'post',
    params: {
      templateId
    },
    data: videoMap
  })
}

/**
 * 获取合成结果
 * @param {string} jobId - 任务ID
 * @returns {Promise}
 */
export function getJobResultByJobId(jobId) {
  return request({
    url: '/videoEdit/getJobResultByJobId',
    method: 'get',
    params: {
      jobId
    }
  })
}

/**
 * 获取模版列表
 * @param {Object} params - 查询参数
 * @param {number} params.pageNo - 页码，默认1
 * @param {number} params.pageSize - 每页大小，默认20
 * @returns {Promise}
 */
export function listTemplates(params = {}) {
  return request({
    url: '/videoEdit/listTemplates',
    method: 'get',
    params: {
      pageNo: params.pageNo || 1,
      pageSize: params.pageSize || 20
    }
  })
}

/**
 * 轮询获取合成结果
 * @param {string} jobId - 任务ID
 * @param {number} interval - 轮询间隔（毫秒），默认3秒
 * @param {number} maxRetries - 最大重试次数，默认100次
 * @returns {Promise}
 */
export function pollJobResult(jobId, interval = 3000, maxRetries = 100) {
  return new Promise((resolve, reject) => {
    let retryCount = 0;
    
    const poll = async () => {
      try {
        const response = await getJobResultByJobId(jobId);
        const result = response.data;
        
        // 根据实际业务状态判断，这里假设有status字段
        if (result && (result.status === 'completed' || result.status === 'success')) {
          resolve(response);
          return;
        }
        
        if (result && (result.status === 'failed' || result.status === 'error')) {
          reject(new Error('视频合成失败'));
          return;
        }
        
        // 如果还在处理中且未超过最大重试次数，继续轮询
        if (retryCount < maxRetries) {
          retryCount++;
          setTimeout(poll, interval);
        } else {
          reject(new Error('轮询超时，请稍后手动查询结果'));
        }
      } catch (error) {
        if (retryCount < maxRetries) {
          retryCount++;
          setTimeout(poll, interval);
        } else {
          reject(error);
        }
      }
    };
    
    poll();
  });
}

