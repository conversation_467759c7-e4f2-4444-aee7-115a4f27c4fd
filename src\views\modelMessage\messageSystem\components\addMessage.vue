<template>
  <el-dialog v-model="dialogVisible" :title="title" width="700px" destroy-on-close :close-on-click-modal="false" @closed="handleClosed" class="message-dialog">
    <div class="form-container">
      <el-form ref="messageSystemFormRef" :model="form" :rules="rules" label-width="80px" class="message-form" v-loading="loading" element-loading-text="消息发送中...">
        <!-- 基本信息区域 -->
        <div class="form-section">
          <div class="section-title"><el-icon><Document /></el-icon> 基本信息</div>
          
          <el-row :gutter="16">
            <el-col :span="24">
              <el-form-item label="标题" prop="messageTitle">
                <el-input v-model="form.messageTitle" placeholder="请输入消息标题" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <br>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="发送方式" prop="sendMode">
                <el-select v-model="form.sendMode" placeholder="选择发送方式" clearable style="width: 100%" @change="fetchRecipients">
                  <el-option v-for="dict in send_mode" :key="dict.value" :label="dict.label" :value="dict.value">
                    <div class="option-with-icon">
                      <el-icon v-if="dict.value === '0'"><MessageBox /></el-icon>
                      <el-icon v-else-if="dict.value === '1'"><PhoneFilled /></el-icon>
                      <el-icon v-else-if="dict.value === '2'"><Message /></el-icon>
                      {{ dict.label }}
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="消息类型" prop="messageType">
                <el-select v-model="form.messageType" placeholder="选择消息类型" clearable style="width: 100%">
                  <el-option v-for="dict in message_type" :key="dict.value" :label="dict.label" :value="dict.value" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="16" v-if="isCodeFieldVisible">
            <el-col :span="24">
              <el-form-item label="接收号码" prop="code">
                <el-input v-model="form.code" placeholder="接收人联系方式" disabled />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        
        <!-- 消息内容区域 -->
        <div class="form-section">
          <div class="section-title"><el-icon><ChatDotRound /></el-icon> 消息内容</div>
          
          <el-row>
            <el-col :span="24">
              <div class="content-type-selector">
                <div class="type-label">内容类型:</div>
                <el-radio-group v-model="form.contentType" @change="handleContentTypeChange" size="default">
                  <el-radio label="template">
                    <el-icon><Files /></el-icon> 模板签名
                  </el-radio>
                  <el-radio label="content">
                    <el-icon><EditPen /></el-icon> 消息内容
                  </el-radio>
                </el-radio-group>
              </div>
            </el-col>
          </el-row>
          
          <el-row>
            <el-col :span="24">
              <div class="content-input-container">
                <template v-if="form.contentType === 'template'">
                  <el-form-item label="模板选择" prop="messageContent">
                    <el-select v-model="form.messageContent" placeholder="请选择模板签名" style="width: 100%" @change="handleTemplateChange">
                      <el-option v-for="temp in templates" :key="temp.templateId" :label="temp.templateCode" :value="temp.templateCode" />
                    </el-select>
                  </el-form-item>
                </template>
                <template v-else>
                  <el-form-item label="消息内容" prop="messageContent">
                    <el-input v-model="form.messageContent" type="textarea" :rows="3" placeholder="短信格式须为: 模板名?模板参数=值" maxlength="100" show-word-limit @input="handleContentInput" />
                  </el-form-item>
                </template>
              </div>
            </el-col>
          </el-row>
        </div>
        
        <!-- 收件人区域 -->
        <div class="form-section">
          <div class="section-title"><el-icon><User /></el-icon> 收件人信息</div>
          
          <el-row>
            <el-col :span="24">
              <div class="recipient-type-selector">
                <div class="type-label">收件人类型:</div>
                <el-radio-group v-model="form.recipientType" @change="fetchRecipients">
                  <el-radio v-for="type in recipientTypes" :key="type.value" :label="type.value">
                    <el-icon v-if="type.value === 'user'"><User /></el-icon>
                    <el-icon v-else-if="type.value === 'role'"><Avatar /></el-icon>
                    <el-icon v-else-if="type.value === 'dept'"><OfficeBuilding /></el-icon>
                    {{ type.label }}
                  </el-radio>
                </el-radio-group>
              </div>
            </el-col>
          </el-row>
          
          <el-row v-if="hasSelectedOptions" class="recipient-row">
            <el-col :span="24">
              <el-form-item :label="selectedLabel" prop="messageRecipient">
                <div v-if="form.recipientType === 'dept'">
                  <el-tree-select v-model="form.messageRecipient" :data="depts" placeholder="请选择部门" clearable :props="{ value: 'deptId', label: 'deptName', children: 'children' }" @change="handleDeptChange" multiple style="width: 500px" />
                </div>
                <div v-else>
                  <el-select v-model="form.messageRecipient" placeholder="请选择收件人" @change="handleSelectionChange" multiple collapse-tags style="width: 500px">
                    <el-option v-for="item in selectedOptions" :key="item.id" :label="item.name" :value="item.id" />
                  </el-select>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <br>
          <el-row>
            <el-col :span="24">
              <el-form-item label="备注">
                <el-input v-model="form.remark" type="textarea" :rows="2" placeholder="可选：添加备注信息" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="toggleDialog">取 消</el-button>
        <el-button type="primary" @click="submitForm" :loading="loading" class="send-button">
          <el-icon><Promotion /></el-icon> 发送消息
        </el-button>
      </div>
    </div>
    
    <template #footer>
      
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, getCurrentInstance, defineProps, defineEmits, defineExpose } from 'vue';
import { getUserNamesByDeptId, getUsersByRoleId, selectUser, selectRole, selectDept, selecTemplates, batchAddMessage } from '@/api/modelMessage/messageSystem';
import { ElMessage } from 'element-plus';
import { Message, Document, ChatDotRound, MessageBox, PhoneFilled, Files, EditPen, User, Avatar, OfficeBuilding, Promotion } from '@element-plus/icons-vue';

const props = defineProps({ title: { type: String, default: "发送消息" } });
const { proxy } = getCurrentInstance();
const { send_mode, message_type} = proxy.useDict("send_mode", "message_type");
const emit = defineEmits(['success', 'close']);
const users = ref([]);
const roles = ref([]);
const depts = ref([]);
const templates = ref([]);
const messageSystemFormRef = ref(null);
const dialogVisible = ref(false);
const form = ref({
  messageTitle: null, messageContent: null, messageTemplate: null, messageRecipient: [], 
  remark: null, sendMode: null, recipientType: null, code: null, contentType: "template" 
});

const loading = ref(false);

const isCodeFieldVisible = computed(() => ['1', '2'].includes(form.value.sendMode)); //0 平台 1 短信 2 邮箱
const recipientTypes=computed(() =>[{ value: 'user', label: '用户' },{ value: 'role', label: '角色' },{ value: 'dept', label: '部门' }]);
const selectedLabel = computed(() => recipientTypes.value.find(type => type.value === form.value.recipientType)?.label || '');
const hasSelectedOptions = computed(() => form.value.recipientType && (users.value.length || roles.value.length || depts.value.length));

const rules = ref({ //验证
  messageTitle: [{ required: true, message: '请输入标题', trigger: 'blur' }],
  sendMode: [{ required: true, message: '请选择发送方式', trigger: 'change' }],
  messageRecipient: [{ required: true, message: '请选择收件人', trigger: 'change' }],
  messageType: [{ required: true, message: '请选择消息类型', trigger: 'change' }],
  recipientType: [{ required: true, message: '请选择收件人方式', trigger: 'change' }],
  contentType: [{ required: true, message: '请选择内容类型', trigger: 'change' }],
  messageContent: [
    { required: true, message: '模版签名或消息内容不能为空', trigger: 'blur' },
    { validator: validateMessageContent, trigger: 'blur' }
  ],
  ...(isCodeFieldVisible.value ? { code: [{ required: true, message: '手机号或者邮箱', trigger: 'blur' }] } : {})
});

const selectedOptions = computed(() => {
  if (!form.value.recipientType) {
    console.log('selectedOptions: no recipientType selected');
    return [];
  }

  const data = form.value.recipientType === 'user' ? users.value :
               form.value.recipientType === 'role' ? roles.value :
               depts.value;

  console.log('selectedOptions - recipientType:', form.value.recipientType);
  console.log('selectedOptions - raw data:', data);

  const options = data.map(item => ({
    id: item.userId || item.roleId || item.deptId,
    name: item.userName || item.roleName || item.deptName,
    phonenumber: item.phonenumber,
    email: item.email
  }));

  console.log('selectedOptions - mapped options:', options);
  return options;
});

//获取部门 角色 平台信息
async function fetchRecipients() {
  console.log('fetchRecipients called, recipientType:', form.value.recipientType, 'sendMode:', form.value.sendMode);

  // 如果已经有选中的收件人，先保存起来
  const previousSelection = form.value.messageRecipient;
  form.value.messageRecipient = [];

  switch (form.value.recipientType) {
    case 'user': await getUsers(); break;
    case 'role': await getRoles(); break;
    case 'dept': await getDepts(); break;
  }

  // 如果之前有选择，并且发送方式改变了，重新计算联系方式
  if (previousSelection && previousSelection.length > 0 && form.value.sendMode) {
    form.value.messageRecipient = previousSelection;
    // 延迟执行，等待 selectedOptions 更新
    setTimeout(() => {
      handleSelectionChange(previousSelection);
    }, 100);
  }
}

//选择对应的平台 角色 部门 填充对应的号码信息
async function handleSelectionChange(selectedIds) {
  console.log('handleSelectionChange called with:', selectedIds);
  console.log('current recipientType:', form.value.recipientType);
  console.log('current sendMode:', form.value.sendMode);

  // 如果没有选择任何收件人，清空联系方式
  if (!selectedIds || selectedIds.length === 0) {
    form.value.code = '';
    return;
  }

  // 如果还没有选择发送方式或收件人类型，暂时不处理
  if (!form.value.sendMode || !form.value.recipientType) {
    console.log('sendMode or recipientType not selected yet, skipping...');
    return;
  }

  try {
    let users = [];

    if (form.value.recipientType === 'role') {
      // 对于角色，我们需要获取完整的用户信息
      for (const id of selectedIds) {
        const response = await getUsersByRoleId(id);
        const roleUsers = response.data || [];
        console.log(`Role ${id} users:`, roleUsers);

        // 检查数据是否完整，如果联系方式字段为空，尝试获取完整信息
        const hasValidContacts = roleUsers.some(user => {
          if (form.value.sendMode === '0') return user.userName || user.nickName;
          if (form.value.sendMode === '1') return user.phonenumber;
          if (form.value.sendMode === '2') return user.email;
          return false;
        });

        if (!hasValidContacts && roleUsers.length > 0) {
          console.log('Role users data incomplete, fetching complete user data...');
          console.log('Sample role user:', roleUsers[0]);

          // 获取所有用户数据
          const allUsersResponse = await selectUser(form.value.sendMode);
          const allUsers = allUsersResponse.data || [];
          console.log('All users from selectUser:', allUsers.length > 0 ? allUsers[0] : 'No users');

          if (allUsers.length > 0) {
            // 根据用户名匹配完整信息（因为userId可能为null）
            const completeUsers = roleUsers.map(roleUser => {
              const completeUser = allUsers.find(u =>
                u.userName === roleUser.userName ||
                u.userId === roleUser.userId
              );
              if (completeUser) {
                console.log(`Matched user ${roleUser.userName}:`, completeUser);
                return completeUser;
              }
              return roleUser;
            });
            users.push(...completeUsers);
          } else {
            console.log('No complete user data available, using original role users');
            users.push(...roleUsers);
          }
        } else {
          users.push(...roleUsers);
        }
      }
    } else if (form.value.recipientType === 'dept') {
      // 对于部门，同样处理
      for (const id of selectedIds) {
        const response = await getUserNamesByDeptId(id);
        const deptUsers = response.data || [];
        console.log(`Dept ${id} users:`, deptUsers);

        // 检查数据是否完整
        const hasValidContacts = deptUsers.some(user => {
          if (form.value.sendMode === '0') return user.userName || user.nickName;
          if (form.value.sendMode === '1') return user.phonenumber;
          if (form.value.sendMode === '2') return user.email;
          return false;
        });

        if (!hasValidContacts && deptUsers.length > 0) {
          console.log('Dept users data incomplete, fetching complete user data...');
          console.log('Sample dept user:', deptUsers[0]);

          const allUsersResponse = await selectUser(form.value.sendMode);
          const allUsers = allUsersResponse.data || [];
          console.log('All users from selectUser:', allUsers.length > 0 ? allUsers[0] : 'No users');

          if (allUsers.length > 0) {
            const completeUsers = deptUsers.map(deptUser => {
              const completeUser = allUsers.find(u =>
                u.userName === deptUser.userName ||
                u.userId === deptUser.userId
              );
              if (completeUser) {
                console.log(`Matched user ${deptUser.userName}:`, completeUser);
                return completeUser;
              }
              return deptUser;
            });
            users.push(...completeUsers);
          } else {
            console.log('No complete user data available, using original dept users');
            users.push(...deptUsers);
          }
        } else {
          users.push(...deptUsers);
        }
      }
    } else if (form.value.recipientType === 'user') {
      users = selectedOptions.value.filter(option => selectedIds.includes(option.id));
    }

    console.log('final users for updateCodeField:', users);
    updateCodeField(users);
  } catch (error) {
    console.error('未获取到用户信息', error);
    ElMessage.error('获取用户信息失败');
  }
}

//部门信息
async function handleDeptChange(deptIds) {
  console.log('handleDeptChange called with:', deptIds);
  console.log('current sendMode:', form.value.sendMode);

  if (!form.value.sendMode) {
    console.log('sendMode not selected yet, skipping...');
    return;
  }

  try {
    const users = [];
    for (const deptId of deptIds) {
      const response = await getUserNamesByDeptId(deptId);
      const deptUsers = response.data || [];
      console.log(`Dept ${deptId} users:`, deptUsers);

      // 检查数据是否完整
      const hasValidContacts = deptUsers.some(user => {
        if (form.value.sendMode === '0') return user.userName || user.nickName;
        if (form.value.sendMode === '1') return user.phonenumber;
        if (form.value.sendMode === '2') return user.email;
        return false;
      });

      if (!hasValidContacts && deptUsers.length > 0) {
        console.log('Dept users data incomplete, fetching complete user data...');
        const allUsersResponse = await selectUser(form.value.sendMode);
        const allUsers = allUsersResponse.data || [];

        if (allUsers.length > 0) {
          const completeUsers = deptUsers.map(deptUser => {
            const completeUser = allUsers.find(u =>
              u.userName === deptUser.userName ||
              u.userId === deptUser.userId
            );
            return completeUser || deptUser;
          });
          users.push(...completeUsers);
        } else {
          users.push(...deptUsers);
        }
      } else {
        users.push(...deptUsers);
      }
    }

    console.log('final dept users for updateCodeField:', users);
    updateCodeField(users);
  } catch (error) {
    console.error('未获取到部门成员信息', error);
    ElMessage.error('获取部门成员信息失败');
  }
}

function updateCodeField(users) {
  console.log('updateCodeField called with users:', users);
  console.log('current sendMode:', form.value.sendMode);

  // 过滤出有效的联系方式
  const validContacts = users.map(user => {
    // 打印第一个用户的所有字段，帮助调试
    if (users.indexOf(user) === 0) {
      console.log('First user fields:', Object.keys(user));
      console.log('First user data sample:', user);
    }

    let contact = '';
    if (form.value.sendMode === '0') {
      // 平台消息：显示用户名 - 尝试多种可能的字段名
      contact = user.userName || user.name || user.nickName || user.realName || user.fullName || '';
    } else if (form.value.sendMode === '1') {
      // 短信：显示手机号 - 尝试多种可能的字段名
      contact = user.phonenumber || user.phone || user.mobile || user.phoneNumber || user.mobilePhone || '';
    } else if (form.value.sendMode === '2') {
      // 邮箱：显示邮箱地址 - 尝试多种可能的字段名
      contact = user.email || user.emailAddress || user.userEmail || user.mailAddress || user.emailAddr || '';
    }

    if (users.indexOf(user) < 3) { // 只打印前3个用户的详细信息
      console.log(`User ${user.userName || user.name}: sendMode=${form.value.sendMode}, contact="${contact}"`);
    }

    return contact;
  }).filter(contact => contact && contact.trim() !== ''); // 过滤掉空值

  // 去重并连接
  const uniqueContacts = [...new Set(validContacts)];
  form.value.code = uniqueContacts.join(', ');

  console.log('valid contacts:', validContacts);
  console.log('unique contacts:', uniqueContacts);
  console.log('final code field:', form.value.code);

  if (!form.value.code || form.value.code.trim() === '') {
    const contactType = form.value.sendMode === '0' ? '用户名' :
                       form.value.sendMode === '1' ? '手机号' : '邮箱地址';

    // 提供详细的错误信息和解决建议
    if (form.value.recipientType === 'role' || form.value.recipientType === 'dept') {
      console.error(`数据问题分析：所选${form.value.recipientType === 'role' ? '角色' : '部门'}下的用户联系方式字段为空`);
      console.error('建议检查：1. 用户资料是否完整 2. 数据库中用户表的联系方式字段是否有数据 3. API是否返回了正确的字段');

      ElMessage({
        message: `所选${form.value.recipientType === 'role' ? '角色' : '部门'}下的用户没有有效的${contactType}信息！\n请检查：\n1. 用户资料是否完整\n2. 系统中是否录入了用户的${contactType}`,
        type: 'warning',
        duration: 5000,
        dangerouslyUseHTMLString: true
      });
    } else {
      ElMessage.warning(`所选收件人中没有有效的${contactType}信息！`);
    }
  } else {
    // 成功获取到联系方式时的提示
    const count = uniqueContacts.length;
    const contactType = form.value.sendMode === '0' ? '用户' :
                       form.value.sendMode === '1' ? '手机号' : '邮箱';
    console.log(`✅ 成功获取到 ${count} 个有效的${contactType}信息`);

    // 在开发环境下显示成功信息
    if (process.env.NODE_ENV === 'development') {
      ElMessage.success(`成功获取到 ${count} 个有效的${contactType}信息`);
    }
  }
}

//发送信息提交
async function submitForm() {
  loading.value = true;
  await messageSystemFormRef.value.validate(async valid => {
    if (valid) {
      try {
        const recipients = await getRecipients();
        const messages = recipients.map(recipient => ({
          ...form.value, messageRecipient: recipient.userName || recipient.name,
          code: form.value.sendMode === '1' ? recipient.phonenumber : form.value.sendMode === '2' ? recipient.email : recipient.name,
          sendMode: form.value.sendMode
        }));
        await batchAddMessage(messages);
        ElMessage.success("消息发送成功！");
        emit('success');
        toggleDialog();
      } catch (error) {
        ElMessage.error('发送信息失败:', error);
        loading.value = false; 
      }
    } else {
      ElMessage.warning("请将信息输入完整！");
      loading.value = false; 
    }
  });
  loading.value = false;
}

//过滤掉不符合的用户
async function getRecipients() {
  if (form.value.recipientType === 'dept') {
    const users = [];
    for (const deptId of form.value.messageRecipient) {
      users.push(...(await getUserNamesByDeptId(deptId)).data.filter(user => user.deptId));
    }
    return users;
  } else if (form.value.recipientType === 'role') {
    return form.value.messageRecipient.flatMap(async id => (await getUsersByRoleId(id)).data);
  } else if (form.value.recipientType === 'user') {
    return selectedOptions.value.filter(option => form.value.messageRecipient.includes(option.id));
  }
  return [];
}

//查询平台 角色 部门 模版信息 部门为树状
async function getUsers() {
  try {
    console.log('getUsers called with sendMode:', form.value.sendMode);
    const response = await selectUser(form.value.sendMode);
    users.value = response.data || [];
    console.log('users data:', users.value);
  } catch (error) {
    console.error('未获取到用户信息', error);
    users.value = [];
  }
}

async function getRoles() {
  try {
    console.log('getRoles called');
    const response = await selectRole();
    roles.value = response.data || [];
    console.log('roles data:', roles.value);
  } catch (error) {
    console.error('未获取到角色信息', error);
    roles.value = [];
  }
}

async function getDepts() {
  try {
    console.log('getDepts called');
    const response = await selectDept();
    depts.value = buildTree(response.data || []);
    console.log('depts data:', depts.value);
  } catch (error) {
    console.error('未获取到部门信息', error);
    depts.value = [];
  }
}

function buildTree(depts) {
  const map = {}, root = [];
  depts.forEach(dept => {
    map[dept.deptId] = dept;
    if (dept.parentId === 0) root.push(dept);
    else {
      map[dept.parentId].children = map[dept.parentId].children || [];
      map[dept.parentId].children.push(dept);
    }
  });
  return root;
}

async function getTemplate() { try { templates.value = (await selecTemplates()).data; } catch (error) { console.error('未获取到模版信息', error); } }

function reset() {
  form.value = { messageTitle: null, messageContent: null, messageTemplate: null, messageRecipient: null, remark: null, sendMode: null, recipientType: null };
  proxy.resetForm("messageSystemFormRef");
}

function toggleDialog() {
  dialogVisible.value = !dialogVisible.value;
  if (!dialogVisible.value) { reset(); emit('close'); loading.value = false; }
}

function handleClosed() { reset(); emit('close'); loading.value = false; }
function handleContentTypeChange() { form.value.messageContent = null; }
function handleTemplateChange(value) { form.value.messageContent = value; }
function handleContentInput(value) { form.value.messageContent = value; }

function validateMessageContent(rule, value, callback) {
  const pattern = /^[^?]+?\?[^=]+?=.*$/;
  if (form.value.sendMode === '1' && form.value.contentType === 'content' && !pattern.test(value)) {
    callback(new Error('短信输入内容格式必须为：模板名?模板参数=值'));
  } else { callback(); }
}

defineExpose({
  open: () => {
    dialogVisible.value = true;
    // 不在这里调用 fetchRecipients，因为此时 recipientType 还没有设置
    console.log('Dialog opened');
  },
  getTemplate
});

getTemplate();
</script>

<style scoped>
/* 对话框基础样式 */
.message-dialog :deep(.el-dialog) { 
  width: 750px !important;
  border-radius: 8px; 
  overflow: visible; 
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.12);
  display: flex;
  flex-direction: column;
  max-height: 90vh;
}

/* 对话框头部 */
.message-dialog :deep(.el-dialog__header) { 
  padding: 15px 20px;  /* 减小头部内边距 */
  margin: 0; 
  border-bottom: 1px solid #f0f2f5; 
  background-color: #f9fafc;
  flex-shrink: 0;
}

/* 对话框主体 */
.message-dialog :deep(.el-dialog__body) { 
  padding: 0;
  flex-grow: 1;
  overflow: visible;
  display: flex;
  flex-direction: column;
}

/* 对话框底部 */
.message-dialog :deep(.el-dialog__footer) { 
  padding: 12px 20px;  /* 减小底部内边距 */
  border-top: 1px solid #f0f2f5; 
  background-color: #f9fafc;
  flex-shrink: 0;
  z-index: 10;
}

/* 消息头部区域 */
.message-header { 
  display: flex; 
  align-items: center; 
  padding: 16px 20px;  /* 减小头部区域内边距 */
  background-image: linear-gradient(120deg, #409EFF, #79bbff); 
  color: #fff;
  flex-shrink: 0;
  margin-bottom: 3px;  /* 减小下方间距 */
}

.message-icon { 
  font-size: 20px;  /* 稍微减小图标大小 */
  background-color: rgba(255, 255, 255, 0.2); 
  padding: 6px;  /* 减小内边距 */
  border-radius: 50%; 
  margin-right: 15px;
}

.message-intro { 
  font-size: 14px; 
  font-weight: 500; 
  opacity: 0.9; 
}

/* 表单容器 */
.form-container {
  flex-grow: 1;
  overflow: visible;
  padding: 3px 0;  /* 减小上下内边距 */
}

/* 表单基础样式 */
.message-form { 
  padding: 20px 24px;  /* 减小左右内边距 */
  background-color: #f8f9fa;
}

/* 表单区块 */
.form-section { 
  background-color: #fff; 
  margin-bottom: 16px;  /* 减小区块之间的间距 */
  border-radius: 10px;  /* 稍减圆角 */
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);  /* 优化阴影 */
  padding: 20px;  /* 减小区块内边距 */
  transition: all 0.3s ease; 
}

.form-section:last-child {
  margin-bottom: 8px;  /* 减小最后一个区块底部间距 */
}

/* 区块标题 */
.section-title { 
  display: flex; 
  align-items: center; 
  border-bottom: 1px solid #f0f2f5; 
  padding-bottom: 6px;  /* 减小标题下方内边距 */
  margin-bottom: 16px;  /* 减小标题与内容间距 */
  font-size: 15px;  /* 稍减字体大小 */
  font-weight: 600; 
  color: #303133; 
}

.section-title .el-icon { 
  margin-right: 8px;  /* 减小图标右侧间距 */
  color: #409EFF; 
  font-size: 16px;  /* 减小图标大小 */
}

/* 类型选择器 */
.content-type-selector, .recipient-type-selector { 
  display: flex; 
  align-items: center; 
  margin-bottom: 10px;  /* 减小底部间距 */
}

.type-label { 
  margin-right: 10px;  /* 减小右侧间距 */
  font-weight: 500; 
  color: #606266; 
  min-width: 70px; 
}

.content-type-selector .el-radio, .recipient-type-selector .el-radio { 
  margin-right: 14px;  /* 减小右侧间距 */
  margin-bottom: 0;
}

.content-type-selector .el-icon, .recipient-type-selector .el-icon { 
  margin-right: 4px;  /* 减小图标右侧间距 */
  font-size: 15px;  /* 减小图标大小 */
}

/* 内容和收件人行 */
.content-input-container, .recipient-row { 
  margin-top: 10px;  /* 减小上方间距 */
  margin-bottom: 0;
}

/* 表单项优化 */
:deep(.el-form-item) {
  margin-bottom: 18px;  /* 减小表单项之间间距 */
}

:deep(.el-form-item:last-child) {
  margin-bottom: 0;
}

:deep(.el-form-item__label) {
  line-height: 32px;  /* 减小行高 */
}

:deep(.el-form-item__content) {
  line-height: 32px;  /* 减小行高 */
}

:deep(.el-form-item__error) {
  position: static;
  margin-top: 2px;  /* 减小上方间距 */
  padding-left: 0;
}

/* 输入框样式 */
:deep(.el-input__wrapper), :deep(.el-textarea__wrapper) {
  padding: 4px 10px;  /* 减小内边距 */
}

/* 底部按钮 */
.dialog-footer { 
  display: flex; 
  justify-content: flex-end; 
  padding: 0 10px;  /* 添加左右内边距 */
}

.send-button { 
  display: flex; 
  align-items: center; 
  padding: 8px 18px;  /* 减小内边距 */
}

.send-button .el-icon { 
  margin-right: 4px; 
  animation: pulse 1.5s infinite; 
}

@keyframes pulse { 
  0% { opacity: 0.7; } 
  50% { opacity: 1; } 
  100% { opacity: 0.7; } 
}

/* 消除多余空白间距 - 调整为只隐藏不需要的br标签 */
br {
  display: block;  /* 恢复br标签的显示 */
  content: "";
 
}

/* 表单区域间距调整 */
.form-section .el-row {
  margin-bottom: 2px;  /* 为行添加底部间距 */
}

.form-section .el-row:last-child {
  margin-bottom: 0;  /* 最后一行不需要底部间距 */
}

/* 增加表单标题和内容间的垂直间距 */
.el-form-item {
  margin-bottom: 22px !important;  /* 增加表单项之间的间距 */
}

/* 发送方式和消息类型之间的间距 */
.el-row:has(.el-col-12) {
  margin-top: 12px;
  margin-bottom: 12px;
}

/* 收件人区域与备注之间的间距 */
.recipient-row {
  margin-bottom: 12px !important;
}

/* 底部按钮区域的间距 */
.dialog-footer {
  margin-top: 12px;
  padding: 5px 12px;
}

/* 响应式调整 */
@media (max-width: 768px) { 
  .message-dialog :deep(.el-dialog) { 
    width: 95% !important; 
  } 
  
  .content-type-selector, .recipient-type-selector { 
    flex-direction: row; 
    align-items: center; 
  }
  
  .type-label { 
    margin-bottom: 0; 
  }
}
</style>