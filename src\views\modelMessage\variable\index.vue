<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="变量名称" prop="variableName">
        <el-input v-model="queryParams.variableName" placeholder="请输入变量名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <br>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd">创建变量</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete">删除变量</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 卡片视图 -->
    <div class="card-view">
      <el-row :gutter="16">
        <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="(item, index) in variableList" :key="index" class="card-col">
          <el-card shadow="hover" class="variable-card" :class="{'variable-card-built-in': item.variableType === '内置变量'}">
            <template #header>
              <div class="card-header">
                <div class="card-header-left">
                  <el-checkbox v-model="item.isSelected" @change="handleSelectChange(item)" class="card-checkbox"></el-checkbox>
                  <span class="card-title">{{ item.variableName }}</span>
                </div>
                <el-tag size="small" effect="plain" :type="getVariableTypeTag(item.variableType)">{{ item.variableType }}</el-tag>
              </div>
            </template>
            <div class="card-content">
              <div class="card-item">
                <span class="card-label">变量内容</span>
                <div class="content-display" :class="{'content-built-in': item.variableType === '内置变量'}">
                  <i v-if="item.variableType === '内置变量'" 
                     :class="getBuiltInVariableIcon(item.variableContent)" 
                     class="built-in-icon"></i>
                  <span class="card-value">{{ formatVariableContent(item) }}</span>
                </div>
              </div>
              <div class="card-actions">
                <el-button type="primary" size="small" icon="Edit" @click="handleUpdate(item)">编辑</el-button>
                <el-button type="danger" size="small" icon="Delete" @click="handleDelete(item)">删除</el-button>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <pagination v-show="total>0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改变量管理对话框 -->
    <el-dialog :title="title" v-model="open" width="650px" append-to-body custom-class="variable-dialog">
      <div class="dialog-container">
        <div class="dialog-header-info">
          <div class="header-icon">
            <i class="el-icon-magic-stick" v-if="form.variableId == null"></i>
            <i class="el-icon-edit" v-else></i>
          </div>
          <div class="header-text">
            {{ form.variableId == null ? '创建新的变量，可用于个性化消息内容' : '编辑已有变量，修改后将影响使用该变量的消息模板' }}
          </div>
        </div>
        <div class="dialog-form-container">
          <el-form ref="variableRef" :model="form" :rules="rules" label-width="100px" class="enhanced-form">
            <el-form-item label="变量名称" prop="variableName">
              <el-input v-model="form.variableName" placeholder="请输入变量名称" clearable>
                <template #prefix><i class="el-icon-price-tag variable-icon"></i></template>
              </el-input>
              <span class="form-tip"><i class="el-icon-info-filled tip-icon"></i> 变量名称用于在消息模板中识别，请确保其唯一性</span>
            </el-form-item>
            
            <el-form-item label="变量类型" prop="variableType" class="variable-type-section">
              <div class="type-selection-wrapper">
                <el-radio-group v-model="form.variableType" @change="handleVariableTypeChange" class="type-radio-group">
                  <el-radio-button label="指定文本">
                    <i class="el-icon-document"></i> 指定文本
                  </el-radio-button>
                  <el-radio-button label="内置变量">
                    <i class="el-icon-cpu"></i> 内置变量
                  </el-radio-button>
                </el-radio-group>
                <div class="type-description">
                  {{ form.variableType === '指定文本' ? 
                      '指定文本变量用于设置固定的文本内容' : 
                      '内置变量会在发送消息时自动替换为系统值' }}
                </div>
              </div>
            </el-form-item>

            <div class="variable-content">
              <transition name="fade" mode="out-in"> <!-- 添加mode="out-in"确保更平滑过渡 -->
                <el-form-item v-if="form.variableType === '内置变量'" label="内置变量" prop="variableContent" class="built-in-form-item content-transition-item">
                  <div class="built-in-variables-grid">
                    <div
                      v-for="(item, key) in builtInVariableOptions"
                      :key="key"
                      :class="['variable-option', {'selected': form.variableContent === key}]"
                      :data-type="key"
                      @click="selectBuiltInVariable(key)"
                    >
                      <div class="variable-option-icon" :style="getVariableIconStyle(key, item.color)">
                        <i :class="item.icon"></i>
                      </div>
                      <div class="variable-option-content">
                        <div class="variable-option-title">{{ item.label }}</div>
                        <div class="variable-option-desc">{{ item.description }}</div>
                      </div>
                    </div>
                  </div>
                  
                  <div class="variable-preview" v-if="form.variableContent">
                    <div class="preview-header">
                      <i class="el-icon-view"></i> 预览示例：{{ getVariablePreview(form.variableContent) }}
                    </div>
                  </div>
                </el-form-item>
                
                <el-form-item v-else-if="form.variableType === '指定文本'" label="变量内容" prop="variableContent" class="content-transition-item">
                  <el-input 
                    v-model="form.variableContent" 
                    type="textarea" 
                    :rows="4" 
                    placeholder="请输入变量内容" 
                    class="content-input"
                    maxlength="1000"
                    show-word-limit
                  />
                </el-form-item>
              </transition>
            </div>
          </el-form>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel" plain>取 消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitLoading">
            <i class="el-icon-check"></i> {{ form.variableId == null ? '创建' : '保存' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Variable">
import { listVariable, getVariable, delVariable, addVariable, updateVariable } from "@/api/modelMessage/variable";
import { ElMessage } from "element-plus";
const { proxy } = getCurrentInstance();

const variableList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const submitLoading = ref(false); // 新增: 提交按钮加载状态

const data = reactive({
  form: {
    variableName: null,
    variableType: null,
    variableContent: null,
    builtInVariable: null
  },
  queryParams: {
    pageNum: 1,
    pageSize: 8,
    variableName: null,
  },
  rules: {
    variableName: [{ required: true, message: '请输入变量名称', trigger: 'blur' }],
    variableType: [{ required: true, message: '请选择变量类型', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data); 
 
/** 查询变量管理列表 */
function getList() {
  loading.value = true;
  listVariable(queryParams.value).then(response => {
    variableList.value = response.rows.map(item => {
      item.isSelected = false;
      return item;
    });
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = { variableId: null, variableName: null, variableType: null, variableContent: null, builtInVariable: null,
    createBy: null, createTime: null, updateBy: null, updateTime: null, remark: null };
  proxy.resetForm("variableRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 处理卡片选择变化
function handleSelectChange(item) {
  // 更新ids数组，用于批量操作
  const selectedItems = variableList.value.filter(i => i.isSelected);
  ids.value = selectedItems.map(i => i.variableId);
  single.value = selectedItems.length != 1;
  multiple.value = !selectedItems.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加变量";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _variableId = row.variableId || ids.value
  getVariable(_variableId).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "编辑变量";
  });
}

// 当变量类型改变时触发
function handleVariableTypeChange(value) {
  if (value === '内置变量') {
    form.value.builtInVariable = null;
    form.value.variableContent = '';
  } else {
    form.value.variableContent = ''; 
  }
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["variableRef"].validate(async (valid) => {
    if (valid) {
      try {
        submitLoading.value = true;
        let result;
        if (form.value.variableId != null) {
          result = await updateVariable(form.value);
          proxy.$modal.msgSuccess("编辑成功");
        } else {
          result = await addVariable(form.value);
          proxy.$modal.msgSuccess("新增成功");
        }

        // 关闭对话框并刷新列表
        open.value = false;
        getList();
      } catch (error) {
        ElMessage.error('操作失败');
      } finally {
        submitLoading.value = false;
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _variableIds = row.variableId || ids.value;
  proxy.$modal.confirm('是否确认删除变量管理编号为"' + _variableIds + '"的数据项？').then(function() {
   return delVariable(_variableIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('modelMessage/variable/export', {
    ...queryParams.value
  }, `variable_${new Date().getTime()}.xlsx`)
}

// 根据变量类型获取标签类型
function getVariableTypeTag(type) {
  if (type === '指定文本') return 'success';
  if (type === '内置变量') return 'primary';
  return 'info';
}

// 格式化变量内容展示
function formatVariableContent(item) {
  if (item.variableType === '内置变量') {
    return getBuiltInVariableLabel(item.variableContent);
  }
  return item.variableContent;
}

// 获取内置变量的显示文本
function getBuiltInVariableLabel(value) {
  const labels = {
    'time': '当前时间',
    'date': '当前日期',
    'datetime': '当前日期和时间',
    'addresser': '发件人信息',
    'recipients': '收件人信息',
    'RandomnDigits': '随机数字',
    'RandomnCharacters': '随机字母',
    'RandomNDigitLetters': '随机数字和字母'
  };
  return labels[value] || value;
}

// 获取内置变量的图标
function getBuiltInVariableIcon(value) {
  const icons = {
    'time': 'el-icon-time',
    'date': 'el-icon-date',
    'datetime': 'el-icon-date',
    'addresser': 'el-icon-user',
    'recipients': 'el-icon-user',
    'RandomnDigits': 'el-icon-coin',
    'RandomnCharacters': 'el-icon-collection',
    'RandomNDigitLetters': 'el-icon-collection-tag'
  };
  return icons[value] || 'el-icon-data-analysis';
}

// 获取变量预览示例
function getVariablePreview(value) {
  const now = new Date();
  const previews = {
    'time': now.toLocaleTimeString(),
    'date': now.toLocaleDateString(),
    'datetime': now.toLocaleString(),
    'addresser': '系统管理员',
    'recipients': '用户A、用户B',
    'RandomnDigits': '123456',
    'RandomnCharacters': 'abcDEF',
    'RandomNDigitLetters': 'a1B2c3'
  };
  return previews[value] || '示例值';
}

// 内置变量选项配置
const builtInVariableOptions = {
  'time': { 
    label: '发送时间', 
    icon: 'el-icon-time',
    description: '当前完整时间',
    color: '#3498db' // 蓝色系 - 时间类
  },
  'date': { 
    label: '发送日期', 
    icon: 'el-icon-date',
    description: '当前完整日期',
    color: '#3498db' // 蓝色系 - 时间类
  },
  'datetime': { 
    label: '发送日期+时间', 
    icon: 'el-icon-timer',
    description: '当前完整日期时间',
    color: '#3498db' // 蓝色系 - 时间类
  },
  'addresser': { 
    label: '发件人', 
    icon: 'el-icon-user',
    description: '发送方的名称或账号',
    color: '#e74c3c' // 红色系 - 人员类
  },
  'recipients': { 
    label: '收件人', 
    icon: 'el-icon-avatar',
    description: '接收方的名称或账号',
    color: '#e74c3c' // 红色系 - 人员类
  },
  'RandomnDigits': { 
    label: '随机数字', 
    icon: 'el-icon-coin',
    description: '生成随机数字序列',
    color: '#2ecc71' // 绿色系 - 随机类
  },
  'RandomnCharacters': { 
    label: '随机字母', 
    icon: 'el-icon-collection',
    description: '生成随机字母序列',
    color: '#2ecc71' // 绿色系 - 随机类
  },
  'RandomNDigitLetters': { 
    label: '随机数字+字母', 
    icon: 'el-icon-collection-tag',
    description: '生成随机字母和数字',
    color: '#2ecc71' // 绿色系 - 随机类
  }
};

// 选择内置变量
function selectBuiltInVariable(key) {
  form.value.variableContent = key;
}

// 根据变量类型和颜色生成图标样式
function getVariableIconStyle(type, color) {
  // 根据变量类型分配不同的渐变颜色
  const gradients = {
    'time': 'linear-gradient(135deg, #3498db, #2980b9)',
    'date': 'linear-gradient(135deg, #3498db, #2980b9)',
    'datetime': 'linear-gradient(135deg, #3498db, #2980b9)',
    'addresser': 'linear-gradient(135deg, #e74c3c, #c0392b)',
    'recipients': 'linear-gradient(135deg, #e74c3c, #c0392b)',
    'RandomnDigits': 'linear-gradient(135deg, #2ecc71, #27ae60)',
    'RandomnCharacters': 'linear-gradient(135deg, #2ecc71, #27ae60)',
    'RandomNDigitLetters': 'linear-gradient(135deg, #2ecc71, #27ae60)'
  };
  
  // 返回带有背景渐变的样式对象
  return {
    backgroundImage: gradients[type] || `linear-gradient(135deg, ${color}, ${adjustColor(color, -20)})`,
    color: 'white'
  };
}

// 辅助函数：调整颜色深浅
function adjustColor(color, amount) {
  // 简单实现，如果有需要可以使用更复杂的颜色处理
  return color; // 这里简化处理，实际应该返回调整后的颜色
}

getList();
</script>

<style scoped>
.app-container { padding: 24px; min-height: 100vh; background: linear-gradient(135deg, #f8f9fa, #edf2f7); }
.el-form { background: rgba(255, 255, 255, 0.98); border-radius: 15px; padding: 25px; box-shadow: 0 8px 15px rgba(0,0,0,0.05); backdrop-filter: blur(4px); margin-bottom: 20px; }

/* 按钮样式优化 */
.el-button { position: relative; overflow: hidden; transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1); border-radius: 8px; font-weight: 500; box-shadow: 0 2px 5px rgba(0,0,0,0.08); }
.el-button:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
.el-button--primary { background: linear-gradient(45deg, #3a7bd5, #00d2ff); border: none; color: white; }
.el-button--danger { background: linear-gradient(45deg, #ff5f6d, #ffc371); border: none; color: white; }
.el-button--warning { background: linear-gradient(45deg, #f7971e, #ffd200); border: none; color: white; }
.el-button--info { background: linear-gradient(45deg, #8e9eab, #eef2f3); border: none; color: #333; }

/* 输入框样式优化 */
.el-input__inner, .el-textarea__inner { border-radius: 8px; border: 1px solid #e8edf2; transition: all 0.3s ease; box-shadow: inset 0 1px 2px rgba(0,0,0,0.03); }
.el-input__inner:focus, .el-textarea__inner:focus { border-color: #3a7bd5; box-shadow: 0 0 0 2px rgba(58, 123, 213, 0.2); }

/* 卡片视图布局 - 统一样式并修复间距问题 */
.card-view { margin: 20px 0; }
.el-row { margin-left: -8px !important; margin-right: -8px !important; }
.card-col { padding: 8px !important; height: auto; }
.variable-card { height: 225px; border-radius: 16px; background: #fff; box-shadow: 0 10px 20px rgba(0,0,0,0.06); transition: all 0.3s ease; display: flex; flex-direction: column; }
.variable-card:hover { transform: translateY(-3px); box-shadow: 0 15px 25px rgba(0,0,0,0.12); }
.variable-card .el-card__header { background: linear-gradient(to right, rgba(255,255,255,0.8), rgba(255,255,255,0.9)); border-bottom: 1px solid #f0f3f7; padding: 12px 15px; height: 50px; box-sizing: border-box; display: flex; align-items: center; }
.variable-card .el-card__body { padding: 15px; height: calc(100% - 50px); display: flex; flex-direction: column; }
.card-header { width: 100%; display: flex; justify-content: space-between; align-items: center; }
.card-header-left { display: flex; align-items: center; width: 75%; gap: 8px; }
.card-title { font-weight: 600; font-size: 15px; color: #303133; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }
.card-checkbox { margin: 0; flex-shrink: 0; }
.card-content { display: flex; flex-direction: column; height: 100%; }
.card-item { display: flex; flex-direction: column; gap: 5px; flex-grow: 1; overflow: hidden; }
.card-label { font-size: 13px; color: #8492a6; font-weight: 500; }
.card-value { font-size: 14px; color: #505A6D; word-break: break-all; line-height: 1.5; overflow: hidden; display: -webkit-box; -webkit-line-clamp: 3; -webkit-box-orient: vertical; }
.card-actions { display: flex; justify-content: flex-end; gap: 8px; padding-top: 10px; border-top: 1px solid #f0f3f7; margin-top: auto; }

/* 标签样式优化 */
.el-tag { border-radius: 6px; padding: 0 8px; font-weight: 500; border: none; box-shadow: 0 2px 4px rgba(0,0,0,0.05); white-space: nowrap; }
.el-tag--success { background: linear-gradient(120deg, #84fab0, #8fd3f4); color: #1f4037; }
.el-tag--primary { background: linear-gradient(120deg, #6a85b6, #bac8e0); color: #2c3e50; }
.el-tag--info { background: linear-gradient(120deg, #e0c3fc, #8ec5fc); color: #4a00e0; }

/* 分页和动画 */
.el-pagination { margin-top: 20px; justify-content: center; padding: 15px 0; background: rgba(255,255,255,0.8); border-radius: 10px; }
@keyframes fadeIn { from { opacity: 0; transform: translateY(10px); } to { opacity: 1; transform: translateY(0); } }

/* 对话框基本样式 */
.el-dialog { border-radius: 20px; overflow: hidden; box-shadow: 0 15px 30px rgba(0,0,0,0.1); backdrop-filter: blur(10px); }
.el-dialog__header { background: linear-gradient(to right, #3a7bd5, #00d2ff); padding: 20px; }
.el-dialog__title { color: white; font-weight: 600; font-size: 18px; text-shadow: 0 1px 2px rgba(0,0,0,0.1); }
.el-dialog__body { padding: 30px 25px; }
.el-dialog__footer { border-top: 1px solid #f0f3f7; padding: 15px 25px; }

/* 适配不同屏幕大小 */
@media screen and (max-width: 768px) { .variable-card { height: 245px; } .built-in-variables-grid { grid-template-columns: 1fr; } .variable-dialog { width: 95% !important; } }
@media screen and (max-width: 576px) { .card-actions { flex-direction: column; gap: 8px; align-items: stretch; } .card-actions .el-button { margin-left: 0 !important; } }

/* 卡片内容样式优化 */
.content-display { background: #f8f9fa; border-radius: 8px; padding: 10px 12px; position: relative; min-height: 45px; display: flex; align-items: flex-start; transition: all 0.3s ease; }
.content-display:hover { background: #f0f2f5; }
.content-built-in { background: rgba(58, 123, 213, 0.08); padding-left: 35px; }
.built-in-icon { position: absolute; left: 10px; top: 12px; color: #3a7bd5; font-size: 16px; }
.variable-card-built-in .el-card__header { background: linear-gradient(to right, rgba(235, 244, 255, 0.8), rgba(235, 244, 255, 0.9)); }

/* 对话框美化 - 进一步增强 */
.variable-dialog .el-dialog__header { position: relative; background-image: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px 25px; }
.variable-dialog .el-dialog__title { font-size: 20px; letter-spacing: 0.5px; }
.variable-dialog .el-dialog__body { padding: 0; }
.dialog-container { display: flex; flex-direction: column; }
.dialog-header-info { display: flex; align-items: center; padding: 20px 25px; background-color: rgba(103, 126, 234, 0.05); border-bottom: 1px solid #edf2f7; }
.header-icon { width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; background-image: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; font-size: 20px; margin-right: 15px; box-shadow: 0 4px 10px rgba(103, 126, 234, 0.3); }
.header-text { color: #606266; font-size: 14px; line-height: 1.6; flex: 1; }
.dialog-form-container { padding: 25px; min-height: 450px; }

/* 表单样式 */
.enhanced-form .el-form-item { margin-bottom: 20px; } /* 减少底部边距 */
.enhanced-form .el-input__inner, .enhanced-form .el-textarea__inner { border-radius: 8px; padding-left: 40px; }
.variable-icon { margin-left: 12px; font-size: 16px; color: #667eea; }
.form-tip { display: block; font-size: 12px; color: #909399; margin-top: 8px; line-height: 1.4; }
.tip-icon { color: #667eea; margin-right: 4px; font-size: 14px; }
.built-in-form-item { margin-bottom: 20px !important; padding-bottom: 200px; } /* 强制减少底部边距 */

/* 变量类型选择区样式 */
.variable-type-section { margin-bottom: 25px; }
.type-selection-wrapper { width: 100%; }
.type-radio-group { width: 100%; display: flex; margin-bottom: 10px; }
.type-radio-group .el-radio-button { flex: 1; }
.type-radio-group .el-radio-button__inner { width: 100%; border-radius: 8px; height: 46px; display: flex; align-items: center; justify-content: center; font-size: 15px; }
.type-radio-group .el-radio-button:first-child .el-radio-button__inner { border-top-right-radius: 0; border-bottom-right-radius: 0; }
.type-radio-group .el-radio-button:last-child .el-radio-button__inner { border-top-left-radius: 0; border-bottom-left-radius: 0; }
.type-radio-group .el-radio-button__original-radio:checked+.el-radio-button__inner { background-image: linear-gradient(135deg, #667eea 0%, #764ba2 100%); box-shadow: none; }
.type-radio-group i { margin-right: 6px; font-size: 16px; }
.type-description { font-size: 12px; color: #909399; background: #f8f9fa; border-radius: 4px; padding: 8px 12px; margin-top: 8px; }
.variable-content { position: relative; min-height: 280px; } /* 设置固定的最小高度 */
.content-transition-item { width: 100%; position: absolute; left: 0; right: 0; } /* 绝对定位防止抖动 */

/* 淡入淡出过渡效果优化 */
.fade-enter-active, .fade-leave-active { transition: opacity 0.25s ease, transform 0.25s ease; }
.fade-enter-from, .fade-leave-to { opacity: 0; transform: translateY(10px); }
.fade-enter-to, .fade-leave-from { opacity: 1; transform: translateY(0); }

/* 内置变量区域样式 - 优化高度和显示 */
.built-in-variables-grid { 
  display: grid; 
  grid-template-columns: repeat(2, 1fr); 
  gap: 8px; 
  margin-bottom: 12px; 
  max-height: none; /* 移除最大高度限制，解决显示不全问题 */ 
  overflow-y: visible; /* 修改为visible，确保内容完全显示 */
}

.variable-option { 
  display: flex; 
  align-items: center; 
  border: 1px solid #dcdfe6; 
  border-radius: 8px; 
  padding: 6px 10px; 
  cursor: pointer; 
  transition: all 0.2s ease; 
  height: 48px; 
  box-sizing: border-box;
}

.variable-option:hover { background-color: #f5f7fa; border-color: #c0c4cc; transform: translateY(-2px); box-shadow: 0 2px 8px rgba(0,0,0,0.05); }
.variable-option.selected { background-color: rgba(103, 126, 234, 0.1); border-color: #667eea; transform: translateY(-2px); box-shadow: 0 2px 8px rgba(103, 126, 234, 0.2); }

.variable-option-icon { 
  min-width: 28px; 
  height: 28px; 
  border-radius: 6px; 
  display: flex; 
  align-items: center; 
  justify-content: center; 
  margin-right: 8px; 
  font-size: 14px; 
  transition: all 0.3s ease; 
}

/* 移除可能导致冲突的CSS背景图像定义 */
/* .variable-option[data-type="time"] .variable-option-icon... 这些样式已由内联样式替代 */

/* 只保留选中状态的特殊样式 */
.variable-option.selected .variable-option-icon { 
  transform: scale(1.05); 
  box-shadow: 0 3px 6px rgba(0,0,0,0.1); 
}

/* 优化预览区域定位 */
.variable-preview {
  margin-top: 15px;
  position: relative;
  z-index: 1; /* 确保预览区在上层 */
}

/* 确保表单区域有足够的空间容纳所有内容 */
.dialog-form-container {
  padding: 25px;
  min-height: 450px; /* 设置最小高度确保内容有足够空间 */
}

/* 图标颜色定义 */
.variable-option[data-type="time"] .variable-option-icon, .variable-option[data-type="date"] .variable-option-icon, .variable-option[data-type="datetime"] .variable-option-icon { background-image: linear-gradient(135deg, #3498db, #2980b9); color: white; }
.variable-option[data-type="addresser"] .variable-option-icon, .variable-option[data-type="recipients"] .variable-option-icon { background-image: linear-gradient(135deg, #e74c3c, #c0392b); color: white; }
.variable-option[data-type="RandomnDigits"] .variable-option-icon, .variable-option[data-type="RandomnCharacters"] .variable-option-icon, .variable-option[data-type="RandomNDigitLetters"] .variable-option-icon { background-image: linear-gradient(135deg, #2ecc71, #27ae60); color: white; }
.variable-option.selected .variable-option-icon { background-image: linear-gradient(135deg, #667eea, #764ba2); color: white; transform: scale(1.05); }

/* 预览区样式 */
.variable-preview { margin-top: 12px; background: rgba(103, 126, 234, 0.05); border-radius: 8px; padding: 0; border: 1px solid #e6e8f1; overflow: hidden; } /* 减少顶部边距 */
.preview-header { padding: 8px 12px; background-color: rgba(103, 126, 234, 0.1); font-size: 12px; font-weight: 600; color: #667eea; border-bottom: 1px solid #e6e8f1; display: flex; align-items: center; } /* 减少内边距和字体 */
.preview-header i { margin-right: 6px; }

/* 对话框底部操作区 */
.dialog-footer { padding: 20px 25px; display: flex; justify-content: flex-end; gap: 10px; }
.dialog-footer .el-button--primary { background-image: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; padding: 12px 25px; font-weight: 500; }
</style>