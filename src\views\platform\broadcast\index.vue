<script setup name="broadcast">
import { listLive, getLive, delLive, addLive, updateLive, getLiveOption, getLiveInfo } from "@/api/platform/live";
import { getKeywordFormat } from "@/api/platform/keyword";
import { useRoute, useRouter } from "vue-router";
import useLiveStore from '@/store/modules/live';
import { stringToColor } from "@/utils/geek";
import useInfoStore from '@/store/modules/info';
import useSoftWareStore from "@/store/modules/software";
import { listScenecon } from "@/api/platform/scenecon";
import { listGoods } from "@/api/platform/goods";
import { getCurrentInstance, ref, reactive, toRefs, nextTick } from 'vue';
import modal from "@/plugins/modal";

const goodsList = ref({});
const sceneconList = ref({});
const { proxy } = getCurrentInstance();
const { platform_live_live_type } = proxy.useDict("platform_live_live_type");
const liveList = ref([]);
const open = ref(false);
const loading = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const route = useRoute()
const router = useRouter()
const infoStore = useInfoStore()
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectId: null,
    liveName: null,
    liveType: null,
    sceneconId: null,
    goodsId: [],
  },
  rules: {
    sceneconId: [
      { required: true, message: "请先选择场控或产品", trigger: "blur" },
    ],
    goodsId: [
      { required: true, message: "请先选择场控或产品", trigger: "blur" },
      {
        validator: (rule, value, callback) => {
          if (value.length <= 0) {
            return callback(new Error("请先选择场控或产品"))
          } else {
            callback()
          }
        }
      }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询直播管理列表 */
function getList() {
  loading.value = true;
  queryParams.value.projectId = parseInt(route.params.projectId)
  listLive(queryParams.value).then(response => {
    liveList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  })
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    liveId: null,
    liveName: null,
    projectId: null,
    liveType: null,
    sceneconId: null,
    goodsId: [],
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    remark: null
  };
  proxy.resetForm("liveRef");
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.liveId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

// 处理单个卡片复选框点击
function handleSelection(val, item) {
  let selection = [...ids.value];
  if (val) {
    if (!selection.includes(item.liveId)) {
      selection.push(item.liveId);
    }
  } else {
    const index = selection.indexOf(item.liveId);
    if (index !== -1) {
      selection.splice(index, 1);
    }
  }
  ids.value = selection;
  single.value = selection.length !== 1;
  multiple.value = selection.length === 0;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加直播管理";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _liveId = row.liveId || ids.value
  getLive(_liveId).then(response => {
    form.value = response.data;
    form.value.sceneconId = sceneconList.value[form.value.sceneconId] ? form.value.sceneconId : null
    form.value.goodsId = form.value.goodsId.filter(i => !!goodsList.value[i])

    open.value = true;
    title.value = "修改直播管理";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["liveRef"].validate((valid, fields) => {
    if (valid) {
      if (form.value.projectId) {
        updateLive(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        form.value.projectId = parseInt(route.params.projectId)
        addLive(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    } else {
      for (let field in fields) {
        fields[field].forEach(i => {
          proxy.$modal.msgError(i.message)
        })
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _liveIds = row.liveId || ids.value;
  proxy.$modal.confirm('是否确认删除直播管理编号为"' + _liveIds + '"的数据项？').then(function () {
    return delLive(_liveIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}
getList();

listScenecon({ projectId: route.params.projectId }).then(res => {
  let _sceneconList = {};
  res.rows.forEach(i => {
    _sceneconList[i.sceneconId] = i.sceneconName
  })
  sceneconList.value = _sceneconList;
});

listGoods({ projectId: route.params.projectId }).then(res => {
  let _goodsList = {};
  res.rows.forEach(i => {
    _goodsList[i.goodsId] = i.goodsName
  })
  goodsList.value = _goodsList;
});

function handleLive(row) {
  useLiveStore().liveId = row.liveId
  router.push({ path: `/project/${route.params.projectId}/liveControl`, query: { projectTitle: route.query.projectTitle } })

}
//产品展示详情
const goodsVisiable = ref(false)
const goodsContent = ref([])
function openGoods(row) {
  goodsVisiable.value = true
  handleGoods(row)
}
function handleGoods(row) {
  goodsContent.value = row.goodsId.map(i => ({
    goodsId: i,
    goodsName: goodsList.value[i],
    goodsColor: stringToColor(goodsList.value[i])
  }));
}

async function openLive(row) {
  await updateLive(row)
  await useSoftWareStore().openLiveWindow(row.liveId)
}
useSoftWareStore().initLiveDb()
</script>
<template>
  <div class="broadcast-platform">
    <div class="broadcast-header">
      <div class="header-content">
        <div class="header-title">
          <div class="title-icon"><i class="el-icon-video-camera-filled"></i></div>
          <div class="title-text">
            <h1>直播中心</h1>
          </div>
        </div>
        <div class="header-actions">
          <el-button type="primary" icon="Plus" @click="handleAdd" class="btn-create">创建直播</el-button>
          <el-button type="danger" icon="Delete" :disabled="multiple" @click="handleDelete" class="btn-delete">批量删除</el-button>
        </div>
      </div>
      <div class="header-decoration"></div>
    </div>

    <div class="broadcast-body" v-loading="loading">
      <div v-if="liveList.length === 0 && !loading" class="broadcast-empty">
        <div class="empty-content">
          <i class="el-icon-video-camera"></i>
          <p>您还没有创建任何直播</p>
          <el-button type="primary" @click="handleAdd" class="create-btn">立即创建</el-button>
        </div>
      </div>

      <div v-else class="broadcast-grid">
        <div v-for="(item, index) in liveList" :key="item.liveId" class="broadcast-item" :style="{'--delay': index * 0.1 + 's'}">
          <div class="item-wrapper" :class="{'item-selected': ids.includes(item.liveId)}">
            <div class="item-selection">
              <el-checkbox :model-value="ids.includes(item.liveId)" @change="(val) => handleSelection(val, item)"></el-checkbox>
            </div>
            
            <div class="item-header">
              <div class="item-status">
                <el-tag :type="item.liveType === 0 ? 'danger' : 'success'" effect="dark" class="status-tag">
                  {{ platform_live_live_type.find(dict => dict.value === item.liveType)?.label }}
                </el-tag>
              </div>
              <div class="item-title">{{ item.liveName }}</div>
            </div>
            
            <div class="item-body">
              <div class="item-info">
                <div class="info-row">
                  <span class="info-label">场控</span>
                  <span class="info-value">{{ sceneconList[item.sceneconId] || '未设置' }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">产品</span>
                  <span class="info-value clickable" @click="openGoods(item)">查看产品列表</span>
                </div>
                <div class="info-row" v-if="item.remark">
                  <span class="info-label">备注</span>
                  <span class="info-value remark">{{ item.remark }}</span>
                </div>
              </div>
            </div>
            
            <div class="item-footer">
              <el-button type="danger" @click="openLive(item)" class="btn-start">
                <i class="el-icon-video-play"></i> 开始直播
              </el-button>
              <div class="item-actions">
                <el-button circle type="primary" icon="Edit" @click="handleUpdate(item)" size="small"></el-button>
                <el-button circle type="danger" icon="Delete" @click="handleDelete(item)" size="small"></el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <pagination 
        v-show="total > 0" 
        :total="total" 
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" 
        @pagination="getList" 
        class="broadcast-pagination" 
      />
    </div>

    <!-- 添加或修改直播管理对话框 -->
    <el-dialog 
      :title="title" 
      v-model="open" 
      width="550px" 
      append-to-body 
      custom-class="live-dialog"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <div class="dialog-content">
        <el-form ref="liveRef" :model="form" :rules="rules" label-width="90px" class="custom-form">
          <el-form-item label="直播名称" prop="liveName">
            <el-input v-model="form.liveName" placeholder="请输入直播名称" class="custom-input" />
          </el-form-item>
          
          <el-form-item label="直播类型" prop="liveType">
            <el-select v-model="form.liveType" placeholder="请选择直播类型" class="full-width custom-select">
              <el-option v-for="item in platform_live_live_type" :key="item.value" :label="item.label"
                :value="item.value" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="场控" prop="sceneconId">
            <el-select v-model="form.sceneconId" placeholder="请选择场控" class="full-width custom-select">
              <el-option v-for="(v, k) in sceneconList" :label="v" :value="+k" :key="k" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="产品" prop="goodsId" class="goods-form-item">
            <div class="checkbox-container custom-checkbox-container">
              <el-checkbox-group v-model="form.goodsId" class="custom-checkbox-group">
                <el-checkbox v-for="(v, k) in goodsList" :label="+k" :key="k" class="checkbox-item custom-checkbox">
                  {{ v }}
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </el-form-item>
          
          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" class="custom-textarea" rows="3" />
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button plain @click="cancel" class="btn-cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm" class="btn-submit">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 产品详情对话框 -->
    <el-dialog 
      v-model="goodsVisiable" 
      title="产品详情" 
      custom-class="goods-dialog"
      width="700px"
    >
      <div class="goods-header">
        <div class="goods-header-icon"><i class="el-icon-shopping-cart-2"></i></div>
        <div class="goods-header-title">本次直播产品列表</div>
      </div>
      
      <div class="goods-content">
        <div v-if="goodsContent.length" class="goods-grid">
          <div v-for="item in goodsContent" :key="item.goodsId" class="goods-item">
            <div class="goods-badge" :style="{ background: item.goodsColor }">
              <span>{{ item.goodsName.substring(0, 1) }}</span>
            </div>
            <div class="goods-name">{{ item.goodsName }}</div>
            <div class="goods-tag">产品编号: {{ item.goodsId }}</div>
          </div>
        </div>
        <el-empty v-else description="暂无产品数据" />
        
        <div class="goods-footer">
          <div class="goods-total">共 {{ goodsContent.length }} 个产品</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
/* 全局样式 - 明亮现代风格 */
.broadcast-platform { 
  display: flex; 
  flex-direction: column; 
  min-height: 100vh; 
  background: #f8f9fe; 
  padding: 0; 
  position: relative; 
  font-family: 'PingFang SC', Arial, sans-serif; 
  color: #333; 
}

/* 头部样式 - 明亮的渐变和图形 */
.broadcast-header { 
  background: linear-gradient(135deg, #3f51b5 0%, #2196f3 100%); 
  padding: 28px 32px; 
  color: white; 
  box-shadow: 0 6px 20px rgba(33, 150, 243, 0.3); 
  position: relative; 
  overflow: hidden; 
  margin-bottom: 24px; 
  border-radius: 0 0 30px 30px;
}

.broadcast-header:before { 
  content: ''; 
  position: absolute; 
  top: -10%; 
  right: -5%; 
  width: 40%; 
  height: 150%; 
  background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%); 
  transform: rotate(-20deg);
}

.header-decoration {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 20px;
  background-image: 
    radial-gradient(circle at 10px -5px, transparent 10px, #f8f9fe 11px),
    radial-gradient(circle at 30px -5px, transparent 10px, #f8f9fe 11px),
    radial-gradient(circle at 50px -5px, transparent 10px, #f8f9fe 11px),
    radial-gradient(circle at 70px -5px, transparent 10px, #f8f9fe 11px),
    radial-gradient(circle at 90px -5px, transparent 10px, #f8f9fe 11px),
    radial-gradient(circle at 110px -5px, transparent 10px, #f8f9fe 11px),
    radial-gradient(circle at 130px -5px, transparent 10px, #f8f9fe 11px),
    radial-gradient(circle at 150px -5px, transparent 10px, #f8f9fe 11px),
    radial-gradient(circle at 170px -5px, transparent 10px, #f8f9fe 11px),
    radial-gradient(circle at 190px -5px, transparent 10px, #f8f9fe 11px);
  background-size: 200px 20px;
  background-repeat: repeat-x;
}

.header-content { display: flex; justify-content: space-between; align-items: center; position: relative; z-index: 2; }
.header-title { display: flex; align-items: center; }
/* 标题图标改为圆形 */
.title-icon { margin-right: 20px; font-size: 26px; background: rgba(255,255,255,0.2); border-radius: 50%; width: 60px; height: 60px; display: flex; align-items: center; justify-content: center; box-shadow: 0 0 20px rgba(255, 255, 255, 0.3); animation: pulse 2s infinite; }

.title-text h1 { margin: 0; font-size: 28px; font-weight: 700; letter-spacing: 0.5px; text-shadow: 0 2px 5px rgba(0,0,0,0.2); }
.title-text p { margin: 6px 0 0; font-size: 16px; opacity: 0.9; }
.header-actions { display: flex; gap: 12px; }

.header-actions .el-button { 
  padding: 10px 18px; 
  font-weight: 500; 
  border-radius: 8px;
  transition: all 0.3s;
}

.btn-create {
  background: #4caf50; 
  border-color: #4caf50;
  box-shadow: 0 4px 10px rgba(76, 175, 80, 0.3);
}

.btn-create:hover {
  transform: translateY(-3px); 
  box-shadow: 0 6px 15px rgba(76, 175, 80, 0.4);
  background: #43a047;
}

.btn-edit {
  background: #ff9800;
  border-color: #ff9800;
  box-shadow: 0 4px 10px rgba(255, 152, 0, 0.3);
}

.btn-edit:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(255, 152, 0, 0.4);
  background: #f57c00;
}

.btn-delete {
  background: #f44336;
  border-color: #f44336;
  box-shadow: 0 4px 10px rgba(244, 67, 54, 0.3);
}

.btn-delete:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(244, 67, 54, 0.4);
  background: #e53935;
}

/* 内容区域样式 */
.broadcast-body { padding: 0 32px 32px; flex: 1; position: relative; z-index: 1; }
.broadcast-grid { 
  display: grid; 
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr)); 
  gap: 24px; 
  margin-bottom: 24px; 
}

/* 空状态样式 */
.broadcast-empty { 
  display: flex; 
  justify-content: center; 
  align-items: center; 
  min-height: 400px; 
  background: white; 
  border-radius: 16px; 
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
  border: 1px dashed #e0e0e0;
}

.empty-content { display: flex; flex-direction: column; align-items: center; }
.empty-content i { font-size: 70px; color: #3f51b5; margin-bottom: 20px; opacity: 0.8; }
.empty-content p { font-size: 18px; color: #757575; margin-bottom: 24px; }
.create-btn { 
  background: #3f51b5; 
  border-color: #3f51b5;
  padding: 10px 24px;
  font-size: 16px;
  transition: all 0.3s;
}
.create-btn:hover {
  transform: scale(1.05);
  background: #303f9f;
}

/* 卡片项目样式 */
.item-wrapper { 
  background: white; 
  border-radius: 12px; 
  overflow: hidden; 
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05); 
  transition: all 0.3s ease; 
  height: 100%; 
  display: flex; 
  flex-direction: column; 
  position: relative;
  border: 1px solid #f0f0f0;
}

.item-wrapper:hover { 
  transform: translateY(-8px); 
  box-shadow: 0 15px 30px rgba(33, 150, 243, 0.15); 
}

.item-selection { position: absolute; top: 16px; right: 16px; z-index: 5; }
.item-selected { 
  box-shadow: 0 0 0 2px #3f51b5, 0 10px 30px rgba(63, 81, 181, 0.2); 
}

/* 卡片头部样式 */
.item-header { 
  padding: 24px; 
  position: relative; 
  background: linear-gradient(135deg, #f5f7ff 0%, #eef1fd 100%); 
  border-bottom: 1px solid #eaeaea;
}
.item-status { margin-bottom: 12px; }
.status-tag { 
  font-weight: 500; 
  font-size: 14px;
  padding: 5px 12px;
  border-radius: 20px;
}
.item-title { 
  font-size: 20px; 
  font-weight: 600; 
  color: #333; 
  overflow: hidden; 
  text-overflow: ellipsis; 
  white-space: nowrap;
}

/* 卡片内容样式 */
.item-body { padding: 24px; flex-grow: 1; }
.info-row { display: flex; margin-bottom: 16px; }
.info-label { width: 60px; font-size: 14px; color: #888; flex-shrink: 0; }
.info-value { flex-grow: 1; font-size: 15px; color: #333; }
.clickable { 
  color: #3f51b5; 
  cursor: pointer; 
  text-decoration: none; 
  font-weight: 500;
  position: relative;
  transition: all 0.3s;
}
.clickable:after {
  content: '';
  position: absolute;
  width: 100%;
  height: 1px;
  bottom: 0;
  left: 0;
  background: #3f51b5;
  transform: scaleX(0);
  transition: transform 0.3s;
}
.clickable:hover { 
  color: #303f9f;  
}
.clickable:hover:after {
  transform: scaleX(1);
}
.remark { display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden; }

/* 卡片底部样式 */
.item-footer { 
  display: flex; 
  justify-content: space-between; 
  align-items: center; 
  padding: 16px 24px; 
  background: #fafbff; 
  border-top: 1px solid #eaeaea;
}

/* 改进开始直播按钮样式 */
.btn-start { background: linear-gradient(45deg, #FF416C, #FF4B2B); border: none; flex-grow: 1; margin-right: 12px; padding: 10px 20px; border-radius: 8px; box-shadow: 0 4px 10px rgba(255, 65, 108, 0.3); transition: all 0.3s; font-weight: 500; }
.btn-start:hover { background: linear-gradient(45deg, #FF4B2B, #FF416C); box-shadow: 0 8px 20px rgba(255, 65, 108, 0.5); transform: translateY(-2px); }

.btn-start i {
  margin-right: 8px;
  font-size: 16px;
}

.item-actions { display: flex; gap: 10px; }
.item-actions .el-button {
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.item-actions .el-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 12px rgba(0, 0, 0, 0.15);
}

/* 分页样式 */
.broadcast-pagination { 
  padding: 10px 0; 
  text-align: center; 
  margin-top: 10px; 
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* 对话框样式 */
.live-dialog :deep(.el-dialog__header) { background: linear-gradient(135deg, #3f51b5 0%, #2196f3 100%); color: white; padding: 20px; border-radius: 10px 10px 0 0; position: relative; }
.live-dialog :deep(.el-dialog__title) { color: white; font-weight: 600; font-size: 20px; }
.live-dialog :deep(.el-dialog__headerbtn .el-icon) { color: white; font-size: 18px; }
.live-dialog :deep(.el-dialog__body) { padding: 30px 25px; }
.live-dialog :deep(.el-dialog) { border-radius: 10px; overflow: hidden; box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15); border: none; }

/* 对话框内容样式 */
.dialog-content { padding: 0; position: relative; }
.custom-form { padding: 0; }
.custom-input { border-radius: 8px; height: 42px; }
.custom-input :deep(input) { height: 42px; }
.custom-select { border-radius: 8px; }
.custom-select :deep(.el-input__inner) { height: 42px; line-height: 42px; }
.full-width { width: 100%; }

/* 复选框容器样式 */
.custom-checkbox-container { max-height: 150px; overflow-y: auto; border: 1px solid #e0e0e0; border-radius: 8px; padding: 12px; background: #f9fafc; }
.custom-checkbox-group { display: flex; flex-wrap: wrap; }
.custom-checkbox { margin-right: 15px; margin-bottom: 10px; padding: 5px 8px; border-radius: 4px; transition: all 0.3s; border: 1px solid transparent; }
.custom-checkbox:hover { background: #e8eaf6; border-color: #c5cae9; }
.custom-textarea :deep(.el-textarea__inner) { border-radius: 8px; padding: 12px; }

/* 对话框底部按钮样式 */
.dialog-footer { display: flex; justify-content: center; gap: 15px; margin-top: 10px; }
.btn-cancel { border-radius: 8px; padding: 10px 25px; transition: all 0.3s; }
.btn-submit { border-radius: 8px; padding: 10px 25px; background: linear-gradient(45deg, #3f51b5, #2196f3); border-color: transparent; box-shadow: 0 3px 10px rgba(33, 150, 243, 0.3); transition: all 0.3s; }
.btn-submit:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(33, 150, 243, 0.4); background: linear-gradient(45deg, #3949ab, #1e88e5); }

/* 产品详情对话框样式 */
.goods-dialog :deep(.el-dialog__header) { background: linear-gradient(135deg, #673ab7 0%, #9c27b0 100%); color: white; padding: 20px; border-radius: 10px 10px 0 0; }
.goods-dialog :deep(.el-dialog__title) { color: white; font-weight: 600; font-size: 20px; }
.goods-dialog :deep(.el-dialog__headerbtn .el-icon) { color: white; }
.goods-dialog :deep(.el-dialog) { border-radius: 10px; overflow: hidden; box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15); border: none; }

/* 产品列表内容样式增强 */
.goods-header { display: flex; align-items: center; margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px solid #e0e0e0; }
.goods-header-icon { width: 50px; height: 50px; border-radius: 10px; background: linear-gradient(45deg, #673ab7, #9c27b0); display: flex; justify-content: center; align-items: center; margin-right: 15px; box-shadow: 0 4px 10px rgba(156, 39, 176, 0.2); }
.goods-header-icon i { font-size: 24px; color: white; }
.goods-header-title { font-size: 18px; font-weight: 600; color: #333; }

.goods-content { padding: 0; }
.goods-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); gap: 20px; margin-bottom: 20px; }
.goods-item { display: flex; flex-direction: column; align-items: center; padding: 20px 15px; border-radius: 15px; background: #fff; transition: all 0.3s ease; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05); border: 1px solid #f0f0f0; position: relative; overflow: hidden; }
.goods-item:after { content: ''; position: absolute; top: 0; left: 0; width: 100%; height: 5px; background: linear-gradient(90deg, transparent, rgba(156, 39, 176, 0.5), transparent); }
.goods-item:hover { transform: translateY(-5px) scale(1.02); box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08); }
.goods-badge { width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-bottom: 16px; box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15); animation: float 3s ease-in-out infinite; }
.goods-badge span { color: white; font-size: 28px; font-weight: bold; text-shadow: 0 2px 3px rgba(0, 0, 0, 0.2); }
.goods-name { font-size: 16px; color: #333; text-align: center; width: 100%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; font-weight: 600; margin-bottom: 8px; }
.goods-tag { font-size: 12px; color: #888; background: #f5f5f5; padding: 3px 8px; border-radius: 20px; text-align: center; }
.goods-footer { display: flex; justify-content: flex-end; margin-top: 20px; padding-top: 15px; border-top: 1px dashed #e0e0e0; }
.goods-total { font-size: 14px; color: #888; background: #f5f5f5; padding: 5px 15px; border-radius: 20px; }

/* 动画效果 */
@keyframes float { 0% { transform: translateY(0px); } 50% { transform: translateY(-5px); } 100% { transform: translateY(0px); } }
/* 修复动画 */
@keyframes pulse { 0% { transform: scale(1); box-shadow: 0 0 20px rgba(255, 255, 255, 0.3); } 50% { transform: scale(1.05); box-shadow: 0 0 30px rgba(255, 255, 255, 0.5); } 100% { transform: scale(1); box-shadow: 0 0 20px rgba(255, 255, 255, 0.3); } }
</style>