<template>
  <el-dialog title="选择视频合成版本" v-model="dialogVisible" width="550px" center destroy-on-close 
    custom-class="version-dialog" @closed="$emit('closed')">
    <div class="version-selection">
      <!-- 已选择形象的提示信息 -->
      <div class="selected-image-info" v-if="imageData">
        <el-avatar :src="imageData.imageAddress" :size="60" class="image-avatar"><el-icon><Picture /></el-icon></el-avatar>
        <div class="image-info">
          <div class="image-name">{{ imageData.imageName }}</div>
          <div class="image-hint">您已选择上述形象，请选择要使用的视频合成版本</div>
        </div>
      </div>
      <!-- 版本选择卡片 -->
      <div class="version-options">
        <div class="version-card" @click="goToVersion('M')">
          <div class="version-icon m-version"><el-icon :size="28"><VideoPlay /></el-icon></div>
          <div class="version-content">
            <div class="version-label">M版</div>
            <div class="version-desc">Muse-Talk视频合成</div>
          </div>
          <div class="version-arrow"><el-icon><ArrowRight /></el-icon></div>
        </div>
        <div class="version-card" @click="goToVersion('V')">
          <div class="version-icon v-version"><el-icon :size="28"><VideoCamera /></el-icon></div>
          <div class="version-content">
            <div class="version-label">V版</div>
            <div class="version-desc">视频语音同步技术</div>
          </div>
          <div class="version-arrow"><el-icon><ArrowRight /></el-icon></div>
        </div>
        <!-- 新增H版 -->
        <div class="version-card" @click="goToVersion('H')">
          <div class="version-icon h-version"><el-icon :size="28"><MagicStick /></el-icon></div>
          <div class="version-content">
            <div class="version-label">H版</div>
            <div class="version-desc">高清智能视频合成</div>
          </div>
          <div class="version-arrow"><el-icon><ArrowRight /></el-icon></div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue';
import { useRouter } from 'vue-router';
import { VideoPlay, VideoCamera, Picture, ArrowRight, MagicStick } from '@element-plus/icons-vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  imageData: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['update:visible', 'closed']);

const router = useRouter();
const dialogVisible = ref(false);

// 路由配置映射表
const VERSION_ROUTES = {
  'M': '/szbVideo/createMusetalk',
  'V': '/szbVideo/synthesisCreate',
  'H': '/szbVideo/createHeygem' 
};

// 监听visible属性变化
watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val;
  },
  { immediate: true }
);

// 监听对话框状态变化
watch(
  () => dialogVisible.value,
  (val) => {
    emit('update:visible', val);
  }
);

// 统一的版本跳转方法
function goToVersion(version) {
  if (!props.imageData) return;
  
  const routePath = VERSION_ROUTES[version] || VERSION_ROUTES.M;
  
  router.push({
    path: routePath,
    query: { 
      imageId: props.imageData.imageId, 
      imageName: props.imageData.imageName, 
      imageAddress: props.imageData.imageAddress 
    }
  });
  
  dialogVisible.value = false;
}
</script>

<style scoped lang="scss">
// 版本选择对话框
:deep(.version-dialog) {
  .el-dialog { 
    border-radius: 12px; 
    overflow: hidden; 
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15); 
  }
  
  .el-dialog__header {
    margin: 0;
    padding: 20px 24px;
    border-bottom: 1px solid #ebeef5;
    background: linear-gradient(135deg, #f8f9fa 0%, #f0f2f5 100%);
    text-align: center;
    
    .el-dialog__title { 
      font-size: 18px; 
      font-weight: 600; 
      color: #303133; 
    }
  }
  
  .el-dialog__body { 
    padding: 24px; 
  }
}

// 版本选择区域
.version-selection {
  .selected-image-info {
    display: flex;
    align-items: center;
    padding: 18px;
    background-color: #f5f7fa;
    border-radius: 10px;
    margin-bottom: 24px;
    border: 1px solid #e4e7ed;
    
    .image-avatar { 
      border: 3px solid #fff; 
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); 
    }
    
    .image-info {
      margin-left: 18px;
      flex: 1;
      
      .image-name { 
        font-size: 16px; 
        font-weight: 600; 
        margin-bottom: 6px; 
        color: #303133; 
      }
      
      .image-hint { 
        color: #606266; 
        font-size: 14px; 
        line-height: 1.4; 
      }
    }
  }
  
  .version-options {
    display: flex;
    flex-direction: column;
    gap: 16px;
    
    .version-card {
      display: flex;
      align-items: center;
      padding: 24px;
      border-radius: 10px;
      background-color: white;
      border: 1px solid #e6e6e6;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
      
      &:hover {
        border-color: #409eff;
        transform: translateY(-2px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        
        .version-arrow { 
          transform: translateX(5px); 
          color: #409eff; 
          opacity: 1; 
        }
        
        .version-icon { 
          transform: scale(1.1); 
        }
      }
      
      .version-icon {
        width: 60px;
        height: 60px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20px;
        transition: transform 0.3s ease;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        color: white;
        
        &.m-version {
          background: linear-gradient(135deg, #3b88ff, #1054cc);
        }
        
        &.v-version {
          background: linear-gradient(135deg, #ff7f50, #e65823);
        }
        
        // 新增H版样式
        &.h-version {
          background: linear-gradient(135deg, #67c23a, #4e8c2c);
        }
      }
      
      .version-content {
        flex: 1;
        
        .version-label { 
          font-size: 18px; 
          font-weight: 600; 
          margin-bottom: 8px; 
          color: #303133; 
        }
        
        .version-desc { 
          color: #606266; 
          font-size: 14px; 
        }
      }
      
      .version-arrow {
        color: #c0c4cc;
        font-size: 22px;
        transition: all 0.3s ease;
        margin-left: 15px;
        opacity: 0.7;
      }
    }
  }
}
</style>