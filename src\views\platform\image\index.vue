<template>
  <div class="app-container">
    <div class="page-header">
      <h2 class="page-title">数字人形象素材库</h2>
      <p class="page-subtitle">创建和管理您的数字人形象视频素材，用于合成高质量视频内容</p>
    </div>

    <div class="operation-bar">
      <div class="search-section">
        <el-input v-model="queryParams.imageName" placeholder="搜索形象名称" class="search-input" clearable @keyup.enter="handleQuery">
          <template #prefix><el-icon><Search /></el-icon></template>
          <template #append><el-button @click="handleQuery">搜索</el-button></template>
        </el-input>
        <el-button icon="Refresh" @click="resetQuery" class="reset-btn">重置</el-button>
      </div>
      <div class="action-section">
        <el-button type="primary" @click="handleAdd" v-hasPermi="['platform:image:add']" class="create-btn"><el-icon><Plus /></el-icon>创建形象</el-button>
        <el-button type="info" @click="handleSelectAll" class="select-all-btn"><el-icon><Check /></el-icon>全选</el-button>
        <el-button type="danger" :disabled="multiple" @click="handleDelete" class="delete-btn"><el-icon><Delete /></el-icon>批量删除</el-button>
      </div>
    </div>

    <div v-loading="loading" class="material-card-container" v-if="imageList.length > 0">
      <el-checkbox-group v-model="selectedIds" @change="handleSelectionChange">
        <div class="material-grid">
          <div v-for="item in imageList" :key="item.imageId" class="material-card">>
            <div class="material-preview" @click="handleGoToMusetalk(item)">
              <div class="checkbox-wrapper"><el-checkbox :label="item.imageId" @click.stop /></div>
              <div class="play-btn"><el-icon><VideoPlay /></el-icon></div>
            </div>
            <div class="material-content">
              <h3 class="material-title">{{ item.imageName }}</h3>
              <div class="user-info">
                <div class="user-item"><el-icon><User /></el-icon>{{ item.createBy || '未知用户' }}</div>
                <div class="time-item"><el-icon><Timer /></el-icon>{{ item.createTime }}</div>
              </div>
              <div class="button-group">
                <el-button type="primary" plain @click.stop="handleUpdate(item)" v-hasPermi="['platform:image:edit']"><el-icon><Edit /></el-icon> 编辑</el-button>
                <el-button type="danger" @click.stop="handleDelete(item)" v-hasPermi="['platform:image:remove']"><el-icon><Delete /></el-icon> 删除</el-button>
              </div>
            </div>
          </div>
        </div>
      </el-checkbox-group>
      <div class="pagination-container">
        <pagination v-show="total>0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
      </div>
    </div>

    <el-empty v-else-if="!loading" class="empty-state">
      <template #description><p>暂无数字人形象素材，立即创建开始您的视频制作之旅</p></template>
      <el-button type="primary" @click="handleAdd">立即创建</el-button>
    </el-empty>

    <!-- 添加或修改形象管理对话框 -->
    <el-dialog :title="title" v-model="open" width="550px" append-to-body destroy-on-close 
      :close-on-click-modal="false" custom-class="image-dialog">
      <div class="dialog-content">
        <el-form ref="imageRef" :model="form" :rules="rules" label-width="100px">
          <el-form-item label="形象名称" prop="imageName">
            <el-input v-model="form.imageName" placeholder="请输入形象名称" maxlength="30" show-word-limit />
          </el-form-item>
          <el-form-item label="形象素材" prop="imageAddress">
            <div class="upload-wrapper">
              <UploadOneImage v-model="form.imageAddress" :existingVideoUrl="form.currentImageAddress"
                :showChunkInfo="true" ref="uploadRef" @file-selected="handleFileSelected" />
              <ChunkUploader ref="chunkUploaderRef" :file-obj="form.imageAddress" :file-name="form.imageName"
                :image-id="form.imageId" v-model:uploading="submitLoading" @upload-success="handleUploadSuccess"
                @upload-error="handleUploadError" />
            </div>
            <div class="upload-tips">仅支持MP4、MOV、AVI视频格式，文件大小不超过500MB</div>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitLoading">{{ form.imageId ? '保存' : '创建' }}</el-button>
        </div>
      </template>
    </el-dialog>
    
    <version-select v-model:visible="versionDialogVisible" :image-data="currentImage" />
  </div>
</template>

<script setup>
import { listImage, getImage, delImage, finishEditImage } from "@/api/platform/image";
import UploadOneImage from "./components/uploadOneImage.vue";
import VersionSelect from "./components/versionSelect.vue";
import ChunkUploader from "./components/chunkUploader.vue";
import { ref, reactive, toRefs, getCurrentInstance } from 'vue';
import { Search, Plus, Delete, Edit, VideoPlay, Timer, User, Check } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();
const imageList = ref([]);
const loading = ref(true);
const total = ref(0);
const selectedIds = ref([]);
const single = ref(true);
const multiple = ref(true);
const open = ref(false);
const title = ref("");
const submitLoading = ref(false);
const versionDialogVisible = ref(false);
const currentImage = ref(null);
const uploadRef = ref(null);
const chunkUploaderRef = ref(null);

// 表单数据
const data = reactive({
  form: {
    imageId: null,
    imageName: null,
    imageStatus: '0',
    imageAddress: null,
    currentImageAddress: null
  },
  queryParams: { 
    pageNum: 1, 
    pageSize: 10, 
    imageName: null 
  },
  rules: {
    imageName: [
      { required: true, message: '请输入形象名称', trigger: 'blur' },
      { min: 2, max: 30, message: '形象名称长度在2-30个字符之间', trigger: 'blur' }
    ],
    imageAddress: [{ 
      validator: (rule, value, callback) => {
        if (form.value.imageId && form.value.currentImageAddress) {
          callback();
        } else if (value) {
          callback();
        } else {
          callback(new Error('请提供形象素材'));
        }
      }, 
      trigger: 'change' 
    }]
  }
});

const { queryParams, form, rules } = toRefs(data);

// 查询形象列表
const getList = async () => {
  loading.value = true;
  try {
    const response = await listImage(queryParams.value);
    imageList.value = response.rows;
    total.value = response.total;
  } catch (error) {
    proxy.$modal.msgError("获取列表失败: " + (error.message || "未知错误"));
  } finally {
    loading.value = false;
  }
};

// 表单提交
const submitForm = async () => {
  await proxy.$refs["imageRef"].validate();
  submitLoading.value = true;
  
  try {
    if (form.value.imageId) {
      // 编辑模式
      if (form.value.imageAddress instanceof File) {
        await chunkUploaderRef.value.startEditUpload(form.value.imageId, form.value.imageName);
      } else {
        await finishEditImage(form.value.imageId, "", "", [], form.value.imageName);
        proxy.$modal.msgSuccess("形象信息更新成功");
        open.value = false;
        getList();
      }
    } else {
      // 新增模式
      if (!form.value.imageAddress || !(form.value.imageAddress instanceof File)) {
        throw new Error('请选择要上传的视频文件');
      }
      await chunkUploaderRef.value.startChunkUpload();
    }
  } catch (error) {
    proxy.$modal.msgError("操作失败: " + (error.message || "未知错误"));
  } finally {
    submitLoading.value = false;
  }
};

// 分片上传事件处理
const handleUploadSuccess = (data) => {
  const message = form.value.imageId ? "编辑成功" : "形象创建成功";
  proxy.$modal.msgSuccess(message);
  open.value = false;
  getList();
};

const handleUploadError = (error) => {
  proxy.$modal.msgError("上传失败: " + (error.message || "未知错误"));
  uploadRef.value?.resetUpload();
};

// 取消操作
const cancel = () => {
  uploadRef.value?.resetComponent();
  open.value = false;
  reset();
};

// 重置表单
const reset = () => {
  form.value = {
    imageId: null, 
    imageName: null, 
    imageStatus: '0',
    imageAddress: null, 
    currentImageAddress: null
  };
  proxy.resetForm("imageRef");
};

// 搜索和重置
const handleQuery = () => { 
  queryParams.value.pageNum = 1; 
  getList(); 
};

const resetQuery = () => { 
  queryParams.value.imageName = null; 
  handleQuery(); 
};

// 多选处理
const handleSelectionChange = (selection) => {
  selectedIds.value = selection;
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
};

// 全选/取消全选
const handleSelectAll = () => {
  selectedIds.value = selectedIds.value.length === imageList.value.length ? [] : imageList.value.map(item => item.imageId);
  handleSelectionChange(selectedIds.value);
};

// 新增形象
const handleAdd = () => { 
  reset(); 
  open.value = true; 
  title.value = "创建形象"; 
};

// 编辑形象
const handleUpdate = (row) => {
  reset();
  const _imageId = row?.imageId || selectedIds.value[0];
  getImage(_imageId).then(response => {
    form.value = { 
      ...response.data, 
      currentImageAddress: response.data.imageAddress, 
      imageAddress: response.data.imageAddress 
    };
    open.value = true;
    title.value = "编辑形象";
  });
};

// 删除形象
const handleDelete = (row) => {
  const _imageIds = row?.imageId || selectedIds.value;
  proxy.$modal.confirm('是否确认删除形象管理编号为"' + _imageIds + '"的数据项？')
    .then(() => delImage(_imageIds))
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
      selectedIds.value = []; 
      handleSelectionChange(selectedIds.value);
    })
    .catch(() => {});
};

const handleGoToMusetalk = (row) => {
  currentImage.value = row;
  versionDialogVisible.value = true;
};

getList();
</script>  

<style scoped lang="scss">
.app-container { 
  padding: 20px; 
  background-color: #f5f7fa; 
  min-height: calc(100vh - 84px); 
}

.page-header {
  background: linear-gradient(135deg, #4a6bdd 0%, #2d4aad 100%);
  color: #fff;
  border-radius: 10px;
  padding: 24px 30px;
  margin-bottom: 24px;
  box-shadow: 0 4px 15px rgba(74, 107, 221, 0.2);
  position: relative;
  overflow: hidden;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 180px;
    height: 100%;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" opacity="0.1"><path d="M80,0 L100,0 L100,100 L0,100 Z" fill="white"/></svg>');
    background-size: cover;
    opacity: 0.2;
  }
  
  .page-title { 
    font-size: 24px; 
    font-weight: 600; 
    margin: 0 0 8px; 
  }
  
  .page-subtitle { 
    font-size: 14px; 
    margin: 0; 
    opacity: 0.8; 
    max-width: 500px; 
  }
}

.operation-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 15px;
  
  .search-section {
    display: flex;
    align-items: center;
    
    .search-input {
      width: 320px;
      border-radius: 8px;
      
      :deep(.el-input__wrapper) {
        box-shadow: 0 0 0 1px #dcdfe6 inset;
        &:hover { box-shadow: 0 0 0 1px #c0c4cc inset; }
        &.is-focus { box-shadow: 0 0 0 1px #409eff inset; }
      }
      
      :deep(.el-input-group__append) {
        background-color: #409eff;
        border-color: #409eff;
        color: #fff;
        border-top-right-radius: 8px;
        border-bottom-right-radius: 8px;
        &:hover { background-color: #66b1ff; border-color: #66b1ff; }
      }
    }
    
    .reset-btn {
      margin-left: 10px;
      border-radius: 8px;
      background-color: #f0f2f5;
      border: none;
      color: #606266;
      &:hover { background-color: #e4e7ed; }
    }
  }
  
  .action-section {
    display: flex;
    gap: 10px;
    
    .create-btn {
      background: linear-gradient(to right, #3e8ef7, #4a6bdd);
      border: none;
      border-radius: 8px;
      padding: 10px 20px;
      font-weight: 500;
      &:hover { background: linear-gradient(to right, #2d7ef0, #3a5bcd); }
    }
    
    .select-all-btn, .delete-btn {
      border-radius: 8px;
      padding: 10px 20px;
      font-weight: 500;
    }
    
    .select-all-btn {
      background-color: #909399;
      border-color: #909399;
      color: white;
      &:hover { background-color: #a6a9ad; border-color: #a6a9ad; }
    }
    
    .delete-btn {
      background-color: rgba(245, 108, 108, 0.1);
      color: #f56c6c;
      border: 1px solid rgba(245, 108, 108, 0.2);
      &:hover:not(:disabled) { background-color: #f56c6c; color: white; }
      &:disabled { opacity: 0.6; cursor: not-allowed; }
    }
  }
}

.material-card-container {
  margin-bottom: 30px;
  
  .material-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    padding: 10px 0;
  }
  
  .material-card {
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid #ebeef5;
    height: 300px;
    
    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 5px 15px rgba(64, 158, 255, 0.15);
      border-color: rgba(64, 158, 255, 0.3);
      
      .play-btn { 
        opacity: 1; 
        transform: translate(-50%, -50%) scale(1); 
      }
      
      .material-preview::after { 
        opacity: 0.5; 
      }
    }
    
    .material-preview {
      position: relative;
      height: 168px;
      background: linear-gradient(135deg, #f2f4f8 0%, #e5e9f0 100%);
      overflow: hidden;
      cursor: pointer;
      
      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(64, 158, 255, 0.3) 0%, rgba(74, 107, 221, 0.3) 100%);
        opacity: 0.35;
        transition: opacity 0.3s ease;
        z-index: 2;
      }
      
      .checkbox-wrapper {
        position: absolute;
        top: 10px;
        left: 10px;
        z-index: 10;
        
        :deep(.el-checkbox__input) {
          &.is-checked .el-checkbox__inner { 
            background-color: #409eff; 
            border-color: #409eff; 
          }
          
          .el-checkbox__inner {
            width: 18px;
            height: 18px;
            border: 1.5px solid #fff;
            background-color: rgba(255, 255, 255, 0.4);
            &::after { height: 8px; width: 3px; left: 6px; top: 2px; }
          }
        }
      }
      
      .play-btn {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) scale(0.9);
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.9);
        color: #409eff;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0.7;
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        z-index: 5;
        
        .el-icon { 
          font-size: 34px; 
          margin-left: 4px; 
        }
      }
    }
    
    .material-content {
      padding: 16px;
      flex: 1;
      display: flex;
      flex-direction: column;
      
      .material-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin: 0 0 12px 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      .user-info {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        flex: 1;
        
        .user-item, .time-item {
          display: flex;
          align-items: center;
          font-size: 15px;
          color: #606266;
          
          .el-icon { 
            font-size: 14px; 
            margin-right: 5px; 
            color: #909399; 
          }
        }
        
        .user-item { 
          margin-right: 16px; 
          flex: 1; 
        }
      }
      
      .button-group {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;
        
        .el-button {
          font-size: 14px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 4px;
          padding: 8px 0;
          height: 34px;
          transition: all 0.25s ease;
          
          .el-icon { 
            margin-right: 5px; 
            font-size: 14px; 
          }
        }
      }
    }
  }
}

.pagination-container { 
  margin-top: 30px; 
  text-align: center; 
}

.empty-state {
  padding: 60px 0;
  background-color: #fff;
  border-radius: 10px;
  margin: 20px 0;
  
  :deep(.el-empty__image) { 
    width: 160px; 
    height: 160px; 
  }
  
  :deep(.el-button--primary) {
    background: linear-gradient(to right, #3e8ef7, #4a6bdd);
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-weight: 500;
  }
}

.image-dialog {
  :deep(.el-dialog__header) {
    padding: 20px 25px;
    border-bottom: 1px solid #f0f2f5;
    
    .el-dialog__title { 
      font-weight: 600; 
      color: #303133; 
      font-size: 18px; 
    }
  }
  
  :deep(.el-dialog__body) { 
    padding: 25px; 
  }
  
  :deep(.el-dialog__footer) { 
    border-top: 1px solid #f0f2f5; 
    padding: 15px 25px; 
  }
  
  .dialog-content .upload-tips { 
    font-size: 12px; 
    color: #909399; 
    margin-top: 8px; 
  }
}
</style>