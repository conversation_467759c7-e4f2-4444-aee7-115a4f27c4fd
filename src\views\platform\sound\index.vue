<template>
  <div class="sound-container">
    <div class="sound-header">
      <div class="header-content">
        <div class="header-left">
          <div class="sound-logo">
            <div class="sound-waves">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
          <h2>声音资源中心</h2>
        </div>
        <div class="header-right">
          <div class="equalizer-container">
            <div class="equalizer">
              <span></span><span></span><span></span>
              <span></span><span></span><span></span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="content-section">
      <div class="search-section">
        <el-card shadow="hover" class="search-panel">
          <template #header>
            <div class="card-header">
              <i class="filter-icon"></i>
              <span>搜索筛选</span>
            </div>
          </template>
          <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px" @submit.prevent class="search-form">
            <el-form-item label="声音名称" prop="soundName" v-hasPermi="['platform:sound:listName']">
              <el-input v-model="queryParams.soundName" placeholder="请输入声音名称" clearable @keyup.enter="handleQuery" class="query-input">
                <template #prefix><el-icon><search /></el-icon></template>
              </el-input>
            </el-form-item>
            <el-form-item label="声音状态" prop="soundStatus" v-hasPermi="['platform:sound:listState']">
              <el-select v-model="queryParams.soundStatus" placeholder="请选择声音状态" class="query-select">
                <el-option v-for="dict in platform_sound_sound_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="数据范围" prop="soundFiltration" v-hasPermi="['platform:sound:listSoundFiltration']">
              <el-select v-model="queryParams.soundFiltration" placeholder="请选择数据范围" class="query-select">
                <el-option v-for="dict in platform_sound_sound_filtration" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="部门列表" prop="deptId" v-hasPermi="['platform:sound:listDeptId']">
              <el-tree-select 
                v-model="queryParams.deptId" :props="{value: 'deptId',label: 'deptName', children: 'children' }" 
                :data="deptOptions" check-strictly placeholder="请选择部门列表" class="query-select"/>
            </el-form-item>
            <el-form-item class="action-item">
              <div class="button-group">
                <el-button type="primary" @click="handleQuery" class="action-button search-btn"><el-icon><search /></el-icon>搜索</el-button>
                <el-button @click="resetQuery" class="action-button reset-btn"><el-icon><refresh /></el-icon>重置</el-button>
              </div>
            </el-form-item>
          </el-form>
        </el-card>
      </div>

      <div class="action-toolbar">
        <div class="main-actions">
          <el-button type="primary" @click="openAddSoundByAudio = true" class="fancy-btn upload-btn">
            <div class="btn-content">
              <i class="upload-icon"></i>
              <span>上传待训练声音</span>
            </div>
          </el-button>
          <el-button type="success" @click="openAddSoundByModel = true" v-hasPermi="['platform:sound:addModel']" class="fancy-btn model-btn">
            <div class="btn-content">
              <i class="model-icon"></i>
              <span>上传已训练声音</span>
            </div>
          </el-button>
          <el-button type="danger" plain :disabled="!selectedRows.length" @click="handleBatchDelete" v-hasPermi="['platform:sound:deleteModel']" class="fancy-btn delete-btn">
            <div class="btn-content">
              <el-icon><Delete /></el-icon>
              <span>批量删除</span>
            </div>
          </el-button>
        </div>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </div>

      <el-card shadow="hover" class="table-panel">
        <template #header>
          <div class="card-header">
            <i class="sound-list-icon"></i>
            <span>声音资源列表</span>
          </div>
        </template>
        <div class="table-container">
          <el-table v-loading="loading" :data="soundList" @selection-change="handleSelectionChange" 
            class="sound-table" :header-cell-style="{background:'#f5f7fa', color: '#606266', fontWeight: '600'}"
            :row-class-name="tableRowClassName">
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column type="index" width="50" label="#" align="center" />
            <el-table-column label="声音名称" prop="soundName" min-width="140" show-overflow-tooltip>
              <template #default="scope">
                <div class="sound-name-cell">
                  <div class="sound-icon-wrapper">
                    <i class="cell-sound-icon"></i>
                  </div>
                  <span class="sound-name-text">{{ scope.row.soundName }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="声音状态" align="center" prop="soundStatus" width="200">
              <template #default="scope">
                <div :class="['status-tag', 'status-' + scope.row.soundStatus]">
                  <div class="status-indicator"></div>
                  <dict-tag :options="platform_sound_sound_status" :value="scope.row.soundStatus" />
                </div>
              </template>
            </el-table-column>
            <el-table-column label="所属部门" align="center" prop="deptName" min-width="120" v-hasPermi="['platform:sound:listDeptId']" show-overflow-tooltip />
            <el-table-column label="数据范围" align="center" prop="soundFiltration" width="100" v-hasPermi="['platform:sound:listSoundFiltration']">
              <template #default="scope">
                <el-tag :type="scope.row.soundFiltration === 0 ? 'info' : 'success'" effect="plain" size="small" class="data-scope-tag">
                  <dict-tag :options="platform_sound_sound_filtration" :value="scope.row.soundFiltration" />
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作人" align="center" prop="createBy" v-if="!isCustomer"/>
            <el-table-column label="操作时间" align="center" prop="createTime" v-if="!isCustomer" width="200">
              <template #default="scope">
                <div class="time-info">
                  <div>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}-{h}:{i}:{s}') }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="280">
              <template #default="scope">
                <div class="table-actions">
                  <el-tooltip v-if="scope.row.soundStatus == 0 || scope.row.soundStatus==4" content="提交审核" placement="top" effect="light">
                    <button class="icon-btn success-btn" @click="handleTrain(scope.row)">
                      <el-icon><upload /></el-icon>
                    </button>
                  </el-tooltip>
                  
                  <el-tooltip v-if="scope.row.soundStatus == 1 && scope.row.soundTrain != null" content="正在训练中" placement="top" effect="light">
                    <button class="icon-btn training-btn">
                      <div class="loading-wave">
                        <span></span><span></span><span></span><span></span><span></span>
                      </div>
                    </button>
                  </el-tooltip>
                  
                  <el-tooltip v-if="!(isCustomer && scope.row.soundFiltration == 1)" content="修改" placement="top" effect="light">
                    <button class="icon-btn primary-btn" @click="handleUpdate(scope.row)">
                      <el-icon><edit /></el-icon>
                    </button>
                  </el-tooltip>
                  
                  <el-tooltip v-if="!(isCustomer && scope.row.soundFiltration == 1)" content="删除" placement="top" effect="light">
                    <button class="icon-btn danger-btn" @click="handleDelete(scope.row)">
                      <el-icon><delete /></el-icon>
                    </button>
                  </el-tooltip>
                  
                  <el-tooltip content="审核" placement="top" effect="light">
                    <button class="icon-btn warning-btn" :class="{'disabled-btn': scope.row.soundStatus != 0}"
                      @click="scope.row.soundStatus == 0 ? handleAudit(scope.row) : null"
                      v-hasPermi="['platform:sound:soundAudit']">
                      <el-icon><check /></el-icon>
                    </button>
                  </el-tooltip>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <pagination v-show="total>0" :total="total" v-model:page="queryParams.pageNum" 
            v-model:limit="queryParams.pageSize" @pagination="getList" background />
        </div>
      </el-card>
    </div>

    <!-- 审核对话框 -->
    <el-dialog :title="title" v-model="dialogVisible" width="450px" center custom-class="custom-dialog audit-dialog" :show-close="false" :close-on-click-modal="false">
      <div class="dialog-body">
        <div class="dialog-icon">
          <i class="large-audit-icon"></i>
          <div class="pulse-ring"></div>
        </div>
        <div class="dialog-message">
          确认通过该声音？它将被加入到队列中，并创建一个任务等待训练。
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button plain @click="confirmAudit(false)" class="dialog-btn reject-btn">驳回</el-button>
          <el-button type="primary" @click="confirmAudit(true)" class="dialog-btn approve-btn">通过</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 修改声音对话框 -->
    <el-dialog v-model="openUpdate" @close="resetForm" center title="修改声音" width="550px"   custom-class="update-dialog">
      <el-form :model="form" class="update-form" label-width="100px">
        <el-form-item label="声音名称" required>
          <el-input v-model="form.soundName" placeholder="请输入声音名称" maxlength="50" show-word-limit />
        </el-form-item>
        <el-form-item label="参考音频">
          <soundUpload :uploadFileUrl="'/platform/sound/uploadSoundref'"  @uploadSuccess="uploadRefAudio"
            v-model:modelValue="form.soundRef"  @removeFile="removeFile"
            :fileType="['mp3', 'wav', 'flac', 'm4a']" :limit="1" :fileSize="10" ref="soundUploadRef"/>
        </el-form-item>
        <el-form-item v-if="refAudioURL" >
          <audio :src="refAudioURL" controls style="width: 100%"></audio>
        </el-form-item>
        <el-form-item label="参考文本" required>
          <el-input type="textarea" v-model="form.soundRefText"  placeholder="请确保参考文本与参考音频内容完全一致，以免影响文案转音频的效果。"  rows="4" maxlength="500" show-word-limit />
        </el-form-item>
        <el-form-item label="部门列表" prop="deptId" v-hasPermi="['platform:sound:editDeptId']">
          <el-tree-select v-model="form.deptId" :props="{value: 'deptId',label: 'deptName',children: 'children'}" 
            :data="deptOptions" check-strictly  placeholder="请选择部门列表" style="width: 100%"/>
        </el-form-item>
        <el-form-item label="数据范围" prop="soundFiltration" v-hasPermi="['platform:sound:editSoundFiltration']">
          <el-select v-model="form.soundFiltration" placeholder="请选择数据范围" style="width: 100%">
            <el-option v-for="dict in platform_sound_sound_filtration" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="openUpdate = false">取消</el-button>
          <el-button type="primary" @click="submit">提交</el-button>
        </div>
      </template>
    </el-dialog>

    <addByAudio v-model="openAddSoundByAudio" @uploadByAudioSuccess="uploadByAudioSuccess" />
    <addByModel v-model="openAddSoundByModel" @uploadByModelSuccess="uploadByModelSuccess" />
  </div>
</template>

<script setup name="Sound">
import { downloadSoundRefBySoundId } from "@/api/platform/audio";
import { delSound, getSound, listSound, updateSound, soundAudit } from "@/api/platform/sound";
import { fetchDeptOptions } from '@/utils/deptUtils';
import { computed, ref, onMounted } from "vue";
import addByAudio from "./add-by-audio.vue";
import addByModel from "./add-by-model.vue";
import soundUpload from "./sound-upload.vue";
import useUserStore from '@/store/modules/user';
import { Delete } from '@element-plus/icons-vue'; // 导入删除图标

const form = ref({
  soundId: null,
  soundName: null,
  soundRef: null,
  soundRefText: null,
  deptId: null
});
const refAudioFromLocal = ref(null);
const refAudioFromServer = ref(null);
const refAudioURL = computed(() => refAudioFromLocal.value ? URL.createObjectURL(refAudioFromLocal.value.raw) : refAudioFromServer.value);
const soundUploadRef = ref(null);
const fileValidationFailed = ref(false); // 新增：文件验证失败标志

const { proxy } = getCurrentInstance();
const { platform_sound_sound_status, platform_sound_sound_filtration } = proxy.useDict("platform_sound_sound_status", "platform_sound_sound_filtration");

const soundList = ref([]);
const openAddSoundByAudio = ref(false);
const openAddSoundByModel = ref(false);
const openUpdate = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);
const title = ref("");
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  soundName: null,
  soundStatus: null,
  soundStatus: null,
  soundFiltration: null
});
const userStore = useUserStore();
const isCustomer = computed(() => !userStore.roles.includes('admin') && !userStore.roles.includes('engineer'));
const deptOptions = ref([]); // 获取部门树桩列表
const dialogVisible = ref(false);
const selectedSoundId = ref(null);
let originalForm = {};

// 添加选中行数据
const selectedRows = ref([]);

/** 查询声音管理列表 */
function getList() { 
  loading.value = true;
  listSound(queryParams.value).then(response => {
    soundList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 重置修改表单 */
function resetForm() {
  form.value = {
    soundId: null,
    soundName: null,
    soundRef: null,
    soundRefText: null,
    deptId: null,
    soundFiltration: null
  };
  soundUploadRef.value.clearUploadList();
  refAudioFromLocal.value = null;
  refAudioFromServer.value = null;
  refAudioURL.value = null;
  fileValidationFailed.value = false; // 重置验证标志
}

/** 修改按钮操作 */
function handleUpdate(row) {
  // 检查状态是否为0（待审核）
  if (row.soundStatus == 1) {
    proxy.$modal.msgWarning("当前声音模型正在训练中，不能编辑！");
    return;
  }
  openUpdate.value = true;
  const _soundId = row.soundId || ids.value;
  getSound(_soundId).then(response => {
    form.value = response.data;
    originalForm = { ...form.value }; 
    openUpdate.value = true;
    title.value = "修改声音管理";
    downloadSoundRefBySoundId(_soundId).then(response => {
      refAudioFromServer.value = URL.createObjectURL(response);
    });
  });
}

/** 修改提交按钮操作 */
async function submit() {
  // 检查文件验证状态
  if (fileValidationFailed.value) {
    proxy.$modal.msgError("文件格式或大小不符合要求，请检查！");
    return;
  }
  try {
    const uploadedFiles = await soundUploadRef.value.executeUpload();
    if (uploadedFiles.length > 0) {
      form.value.soundRef = uploadedFiles[0].url; // 使用uploadList中保存的url
    }
    if (form.value.soundRefText !== originalForm.soundRefText || form.value.soundRef !== originalForm.soundRef) {
      form.value.soundStatus = 4;
    }
    await updateSound(form.value);
    getList();
    proxy.$modal.msgSuccess("修改成功");
    openUpdate.value = false;
    soundUploadRef.value.clearUploadList();
    form.value = {
      soundId: null,
      soundName: null,
      soundRef: null,
      soundRefText: null,
      deptId: null,
      soundFiltration: null
    };
  } catch (error) {
    console.log(error);
  }
}

/** 上传参考音频 */
function uploadRefAudio(refAudio) {
  form.value.soundRef = refAudio[0].url;
  // 更新本地音频预览
  const file = soundUploadRef.value.$refs.fileUpload.uploadFiles[0];
  if (file && file.raw) {
    refAudioFromLocal.value = file;
    refAudioFromServer.value = null;
  }
}

function uploadByAudioSuccess(res) {
  getList()
  openAddSoundByAudio.value = false
}

function uploadByModelSuccess(res) {
  getList()
  openAddSoundByModel.value = false
}

/** 删除上传列表中的音频 */
function removeFile(file) {
  form.value.soundRef = null;
  refAudioFromLocal.value = null;
  refAudioFromServer.value = null;
  fileValidationFailed.value = false; // 重置验证标志
}

/** 提交审核 */
function handleTrain(row) {
  updateSound({soundId: row.soundId, soundStatus: 0}).then(response => {
    getList();
    proxy.$modal.msgSuccess("等待管理员审核！");
  })
}

//审核弹窗
function handleAudit (row) {
  title.value = '用户 '+row.createBy+' 声音审核'
  selectedSoundId.value = row.soundId;
  dialogVisible.value = true; // 显示对话框
};

/** 审核声音模型 */
const confirmAudit = async (isApproved) => {
  try {
    await soundAudit(selectedSoundId.value, isApproved);
    const message = isApproved ? '审核通过' : '已驳回';
    proxy.$modal.msgSuccess(`${message}`);
    dialogVisible.value = false;
    getList(); // 刷新列表
  } catch (error) {
    proxy.$modal.msgError('操作失败，请重试');
  }
};

/** 删除按钮操作 */
function handleDelete(row) {
  const _soundIds = row.soundId || ids.value;
  proxy.$modal.confirm('是否确认删除声音管理编号为"' + _soundIds + '"的数据项？').then(function () {
    return delSound(_soundIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

// 处理选择变化
function handleSelectionChange(rows) {
  selectedRows.value = rows;
}

// 批量删除功能
function handleBatchDelete() {
  const soundIds = selectedRows.value.map(row => row.soundId).join(',');
  proxy.$modal.confirm('是否确认删除选中的' + selectedRows.value.length + '条声音数据？').then(() => {
    return delSound(soundIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
    selectedRows.value = []; // 清空选择
  }).catch(() => {});
}
// 获取部门树状信息列表
onMounted(async () => {
  deptOptions.value = await fetchDeptOptions();
});
getList();
</script>

<style lang="scss" scoped>
/* 整体容器和基本布局 */
.sound-container { background-color: #f0f2f5; min-height: 100vh; padding: 20px; font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif; }

/* 顶部标题区域 */
.sound-header { background: linear-gradient(135deg, #4776e6, #8e54e9); border-radius: 12px; padding: 0; overflow: hidden; margin-bottom: 24px; box-shadow: 0 8px 24px rgba(71, 118, 230, 0.2); }
.header-content { display: flex; justify-content: space-between; align-items: center; padding: 20px 30px; position: relative; z-index: 1; }
.header-content:before { content: ""; position: absolute; top: 0; right: 0; bottom: 0; left: 0; background-image: url("data:image/svg+xml,%3Csvg width='100' height='20' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0,10 Q25,20 50,10 T100,10' stroke='rgba(255,255,255,0.1)' fill='none' stroke-width='1'/%3E%3C/svg%3E"); background-size: 100px 20px; opacity: 0.2; z-index: -1; }
.header-left { display: flex; align-items: center; }
.header-left h2 { color: #ffffff; font-size: 22px; font-weight: 500; margin: 0; text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); }

/* 声音波动动画 */
.sound-logo { width: 42px; height: 42px; background-color: rgba(255, 255, 255, 0.15); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 16px; }
.sound-waves { display: flex; align-items: center; justify-content: center; height: 20px; width: 20px; }
.sound-waves span { display: inline-block; width: 4px; margin: 0 1px; background: #ffffff; border-radius: 2px; animation: soundWave 1.2s ease-in-out infinite; }
.sound-waves span:nth-child(1) { animation-delay: 0s; height: 8px; }
.sound-waves span:nth-child(2) { animation-delay: 0.3s; height: 16px; }
.sound-waves span:nth-child(3) { animation-delay: 0.6s; height: 10px; }

/* 均衡器动画 */
.equalizer-container { background: rgba(255, 255, 255, 0.1); padding: 8px 16px; border-radius: 30px; }
.equalizer { display: flex; align-items: flex-end; height: 20px; gap: 3px; }
.equalizer span { width: 3px; background: #ffffff; border-radius: 1px; animation: equalize 1.5s ease-in-out infinite; opacity: 0.8; }
.equalizer span:nth-child(1) { animation-delay: 0.1s; height: 8px; }
.equalizer span:nth-child(2) { animation-delay: 0.3s; height: 12px; }
.equalizer span:nth-child(3) { animation-delay: 0.5s; height: 16px; }
.equalizer span:nth-child(4) { animation-delay: 0.2s; height: 14px; }
.equalizer span:nth-child(5) { animation-delay: 0.4s; height: 10px; }
.equalizer span:nth-child(6) { animation-delay: 0.6s; height: 6px; }

/* 内容布局 */
.content-section { display: flex; flex-direction: column; gap: 24px; }
.search-section { display: flex; width: 100%; }
.search-panel { width: 100%; border-radius: 12px; box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04) !important; border: none; }
.card-header { display: flex; align-items: center; height: 32px; font-weight: 500; color: #303133; font-size: 16px; }
.filter-icon { width: 20px; height: 20px; background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234776e6'%3E%3Cpath d='M10,18h4v-2h-4V18z M3,6v2h18V6H3z M6,13h12v-2H6V13z'/%3E%3C/svg%3E"); margin-right: 8px; background-size: contain; background-repeat: no-repeat; }
.sound-list-icon { width: 20px; height: 20px; background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234776e6'%3E%3Cpath d='M4,10h18V8H4V10z M4,16h18v-2H4V16z M4,4v2h18V4H4z M2,20v-2h18v2H2z'/%3E%3C/svg%3E"); margin-right: 8px; background-size: contain; background-repeat: no-repeat; }

/* 表单样式 */
.search-form { display: flex; flex-wrap: wrap; gap: 8px; align-items: flex-start; }
.el-form-item { margin-bottom: 16px; margin-right: 0; }
.query-input, .query-select { width: 220px; transition: all 0.3s; box-shadow: 0 1px 4px rgba(0,0,0,0.05); }
.query-input:hover, .query-select:hover { box-shadow: 0 2px 8px rgba(0,0,0,0.09); }
.action-item { margin-left: auto; margin-right: 0; align-self: flex-end; }
.button-group { display: flex; gap: 12px; justify-content: flex-end; }
.action-button { min-width: 90px; padding: 10px 20px; font-weight: 500; border-radius: 6px; transition: all 0.3s; box-shadow: 0 2px 6px rgba(0,0,0,0.1); }
.search-btn { background: linear-gradient(to right, #4776e6, #5a8cf0); border-color: transparent; }
.reset-btn { background: #fff; border-color: #e4e7ed; color: #606266; }
.action-button:hover { transform: translateY(-2px); box-shadow: 0 4px 10px rgba(0,0,0,0.12); }

/* 操作工具栏 */
.action-toolbar { display: flex; justify-content: space-between; align-items: center; }
.main-actions { display: flex; gap: 16px; }
.fancy-btn { height: 44px; padding: 0 20px; border-radius: 8px; overflow: hidden; position: relative; border: none; transition: all 0.3s ease; }
.fancy-btn:before { content: ''; position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(45deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.1) 100%); z-index: 1; }
.upload-btn { background: linear-gradient(45deg, #4776e6, #5a8cf0); box-shadow: 0 4px 14px rgba(71, 118, 230, 0.3); }
.model-btn { background: linear-gradient(45deg, #42b983, #59d4a0); box-shadow: 0 4px 14px rgba(66, 185, 131, 0.3); }
.btn-content { display: flex; align-items: center; justify-content: center; gap: 8px; z-index: 2; position: relative; }
.upload-icon, .model-icon { display: block; width: 18px; height: 18px; background-size: contain; background-repeat: no-repeat; background-position: center; }
.upload-icon { background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23FFFFFF' viewBox='0 0 24 24'%3E%3Cpath d='M9 16h6v-6h4l-7-7-7 7h4zm-4 2h14v2H5z'/%3E%3C/svg%3E"); }
.model-icon { background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23FFFFFF' viewBox='0 0 24 24'%3E%3Cpath d='M12 3v9.28c-.47-.17-.97-.28-1.5-.28C8.01 12 6 14.01 6 16.5S8.01 21 10.5 21c2.31 0 4.2-1.75 4.45-4H15V6h4V3h-7z'/%3E%3C/svg%3E"); }

/* 表格样式 */
.table-panel { width: 100%; border-radius: 12px; overflow: hidden; border: none; box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04) !important; }
.table-container { padding: 0; overflow: hidden; }
.sound-table { border-radius: 8px; overflow: hidden; }
.sound-table .highlighted-row { background-color: #f8faff; }
.sound-name-cell { display: flex; align-items: center; gap: 10px; padding: 4px 0; }
.sound-icon-wrapper { display: flex; align-items: center; justify-content: center; width: 30px; height: 30px; background-color: #eef1f7; border-radius: 6px; }
.cell-sound-icon { width: 18px; height: 18px; background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23606266' viewBox='0 0 24 24'%3E%3Cpath d='M12 3v9.28c-.47-.17-.97-.28-1.5-.28C8.01 12 6 14.01 6 16.5S8.01 21 10.5 21c2.31 0 4.2-1.75 4.45-4H15V6h4V3h-7z'/%3E%3C/svg%3E"); background-size: contain; background-repeat: no-repeat; background-position: center; }
.sound-name-text { font-weight: 500; color: #303133; }

/* 状态标签样式 */
.status-tag { display: inline-flex; align-items: center; padding: 4px 10px; border-radius: 12px; font-size: 12px; gap: 6px; }
.status-0 { background: rgba(144, 147, 153, 0.1); } /* 待审核 */
.status-1 { background: rgba(103, 194, 58, 0.1); } /* 训练中 */
.status-2 { background: rgba(103, 194, 58, 0.1); } /* 已完成 */
.status-3 { background: rgba(245, 108, 108, 0.1); } /* 已驳回 */
.status-4 { background: rgba(230, 162, 60, 0.1); } /* 等待审核 */
.status-indicator { width: 8px; height: 8px; border-radius: 50%; }
.status-0 .status-indicator { background-color: #909399; } /* 待审核 */
.status-1 .status-indicator { background-color: #67C23A; box-shadow: 0 0 0 3px rgba(103, 194, 58, 0.2); animation: pulse 1.5s infinite; } /* 训练中 */
.status-2 .status-indicator { background-color: #67C23A; } /* 已完成 */
.status-3 .status-indicator { background-color: #F56C6C; } /* 已驳回 */
.status-4 .status-indicator { background-color: #E6A23C; } /* 等待审核 */
.data-scope-tag { font-size: 12px; border-radius: 10px; padding: 0 8px; }

/* 表格操作按钮 */
.table-actions { display: flex; justify-content: center; gap: 10px; padding: 5px 0; }
.icon-btn { position: relative; width: 38px; height: 38px; border-radius: 8px; border: none; display: flex; align-items: center; justify-content: center; cursor: pointer; transition: all 0.3s ease; overflow: hidden; color: white; box-shadow: 0 3px 8px rgba(0,0,0,0.12); }
.icon-btn::before { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(rgba(255,255,255,0.1), rgba(255,255,255,0)); z-index: 1; }
.icon-btn .el-icon { position: relative; z-index: 2; font-size: 18px; opacity: 0.95; transition: all 0.3s ease; }
.icon-btn:hover { transform: translateY(-3px) scale(1.05); box-shadow: 0 5px 12px rgba(0,0,0,0.18); }
.icon-btn:hover .el-icon { opacity: 1; transform: scale(1.1); }
.icon-btn:active { transform: translateY(1px) scale(0.98); }
.success-btn { background: linear-gradient(135deg, #42b983, #33a97e); box-shadow: 0 3px 8px rgba(66, 185, 131, 0.25); }
.primary-btn { background: linear-gradient(135deg, #409eff, #2c8df3); box-shadow: 0 3px 8px rgba(64, 158, 255, 0.25); }
.danger-btn { background: linear-gradient(135deg, #f56c6c, #e64242); box-shadow: 0 3px 8px rgba(245, 108, 108, 0.25); }
.warning-btn { background: linear-gradient(135deg, #e6a23c, #d78b1e); box-shadow: 0 3px 8px rgba(230, 162, 60, 0.25); }
.training-btn { background: linear-gradient(135deg, #909399, #737579); box-shadow: 0 3px 8px rgba(144, 147, 153, 0.25); cursor: default; }
.disabled-btn { opacity: 0.6; cursor: not-allowed; transform: none; }
.disabled-btn:hover { transform: none; box-shadow: 0 3px 8px rgba(0,0,0,0.12); }

/* 加载波动画 */
.loading-wave { display: flex; align-items: flex-end; height: 20px; justify-content: center; width: 100%; }
.loading-wave span { width: 3px; height: 3px; margin: 0 2px; background-color: white; display: inline-block; animation: wave 1.2s infinite ease-in-out; border-radius: 1px; }
.loading-wave span:nth-child(1) { animation-delay: -1.2s; }
.loading-wave span:nth-child(2) { animation-delay: -1.0s; }
.loading-wave span:nth-child(3) { animation-delay: -0.8s; }
.loading-wave span:nth-child(4) { animation-delay: -0.6s; }
.loading-wave span:nth-child(5) { animation-delay: -0.4s; }

/* 修改声音弹窗样式 */
.update-dialog { border-radius: 12px; overflow: hidden; }
.update-dialog :deep(.el-dialog__header) { padding: 15px 20px; margin-right: 0; background: #f8f9fb; border-bottom: 1px solid #ebeef5; }
.update-dialog :deep(.el-dialog__body) { padding: 20px; }
.update-dialog :deep(.el-form-item__label) { font-weight: 500; }
.update-form { max-height: 60vh; overflow-y: auto; }
.dialog-footer { display: flex; justify-content: flex-end; gap: 10px; }

/* 音频播放器样式 */
.audio-player-wrapper { width: 100%; border-radius: 8px; padding: 12px; background: #f8f9fb; border: 1px solid #ebeef5; position: relative; }
.audio-visualizer { display: flex; align-items: flex-end; height: 30px; gap: 2px; margin-bottom: 12px; justify-content: center; }
.visualizer-bar { width: 4px; height: 16px; background: #4776e6; opacity: 0.7; border-radius: 2px; animation: visualize 1.2s ease-in-out infinite; }
.visualizer-bar:nth-child(odd) { animation-delay: 0.2s; height: 20px; }
.visualizer-bar:nth-child(even) { animation-delay: 0.4s; height: 12px; }
.visualizer-bar:nth-child(3n) { animation-delay: 0.6s; height: 24px; }
.audio-player { width: 100%; height: 40px; outline: none; border-radius: 20px; }
.audio-player::-webkit-media-controls-panel { background: linear-gradient(135deg, #eef2f7, #f8f9fb); }

/* 对话框底部按钮 */
.dialog-footer { display: flex; justify-content: flex-end; padding: 0 24px 20px; gap: 12px; }
.dialog-btn { min-width: 100px; padding: 10px 20px; border-radius: 6px; font-weight: 500; transition: all 0.3s; }
.submit-btn { background: linear-gradient(135deg, #4776e6, #5a8cf0); border-color: transparent; box-shadow: 0 3px 8px rgba(71, 118, 230, 0.2); }
.submit-btn:hover { background: linear-gradient(135deg, #3a67d6, #4c7ee0); transform: translateY(-2px); box-shadow: 0 5px 12px rgba(71, 118, 230, 0.3); }

/* 动画关键帧 */
@keyframes soundWave { 0%, 100% { transform: scaleY(1); } 50% { transform: scaleY(2); } }
@keyframes equalize { 0%, 100% { transform: scaleY(1); } 50% { transform: scaleY(1.5); } }
@keyframes pulse { 0% { box-shadow: 0 0 0 0px rgba(103, 194, 58, 0.4); } 100% { box-shadow: 0 0 0 6px rgba(103, 194, 58, 0); } }
@keyframes wave { 0%, 40%, 100% { transform: scaleY(1); } 20% { transform: scaleY(6); } }
@keyframes visualize { 0%, 100% { transform: scaleY(1); } 50% { transform: scaleY(1.2); } }

/* 响应式布局 */
@media (max-width: 768px) {
  .search-form { flex-direction: column; width: 100%; }
  .query-input, .query-select { width: 100%; }
  .action-item { width: 100%; margin-left: 0; }
  .button-group { width: 100%; justify-content: space-between; }
  .action-button { flex: 1; }
}
</style>