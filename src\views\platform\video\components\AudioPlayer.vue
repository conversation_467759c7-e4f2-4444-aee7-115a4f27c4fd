<template>
  <div class="audio-container">
    <div class="audio-title">驱动音频</div>
    <div class="audio-player" v-if="audioSrc">
      <audio 
        ref="audioRef" 
        :src="audioSrc" 
        @loadedmetadata="handleLoadedMetadata"
        @timeupdate="handleTimeUpdate"
        @play="$emit('update:isPlaying', true)"
        @pause="$emit('update:isPlaying', false)"
      ></audio>
      <div class="player-controls">
        <button :class="isPlaying ? 'pause-btn' : 'play-btn'" @click="togglePlay">
          <el-icon><component :is="isPlaying ? 'video-pause' : 'video-play'" /></el-icon>
        </button>
        <div class="progress-bar">
          <div class="progress-bg" @click="seek">
            <div class="progress-fill" :style="{ width: `${progress}%` }"></div>
          </div>
          <div class="time-info">
            <span>{{ formatTime(currentTime) }}</span>
            <span>{{ formatTime(duration) }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
  audioSrc: String,
  index: Number
});

const emit = defineEmits(['update:isPlaying', 'update:progress']);
const audioRef = ref(null);
const isPlaying = ref(false);
const currentTime = ref(0);
const duration = ref(0);
const progress = ref(0);

// 当音频元数据加载完成时获取时长
const handleLoadedMetadata = () => {
  if (audioRef.value) {
    duration.value = audioRef.value.duration;
  }
};

// 更新进度
const handleTimeUpdate = () => {
  if (audioRef.value) {
    currentTime.value = audioRef.value.currentTime;
    progress.value = (currentTime.value / duration.value) * 100;
    emit('update:progress', progress.value);
  }
};

// 切换播放/暂停
const togglePlay = () => {
  if (!audioRef.value) return;
  if (isPlaying.value) {
    audioRef.value.pause();
  } else {
    audioRef.value.play();
  }
  isPlaying.value = !isPlaying.value;
  emit('update:isPlaying', isPlaying.value);
};

// 进度条点击seek
const seek = (e) => {
  if (!audioRef.value) return;
  const rect = e.target.getBoundingClientRect();
  const percent = (e.clientX - rect.left) / rect.width;
  audioRef.value.currentTime = percent * duration.value;
};

const formatTime = (time) => {
  if (!time || isNaN(time)) return '00:00';
  const minutes = Math.floor(time / 60);
  const seconds = Math.floor(time % 60);
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

// 监听音频源变化
watch(() => props.audioSrc, () => {
  if (audioRef.value) {
    audioRef.value.load();
    isPlaying.value = false;
    currentTime.value = 0;
    progress.value = 0;
  }
});
</script>

<style lang="scss" scoped>
.audio-container {
  display: flex;
  flex-direction: column;
  gap: 8px;

  .audio-title {
    font-size: 14px;
    font-weight: 500;
    color: #606266;
    padding-bottom: 8px;
    border-bottom: 1px solid #ebeef5;
  }

  .audio-player {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    
    .player-controls {
      display: flex;
      align-items: center;
      gap: 12px;

      button {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        color: white;

        &.play-btn {
          background: #409eff;
          &:hover { background: #66b1ff; }
        }

        &.pause-btn {
          background: #f56c6c;
          &:hover { background: #f78989; }
        }
      }
    }

    .progress-bar {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 4px;

      .progress-bg {
        height: 6px;
        background: #e4e7ed;
        border-radius: 3px;
        cursor: pointer;
        position: relative;
        overflow: hidden;

        .progress-fill {
          position: absolute;
          left: 0;
          top: 0;
          height: 100%;
          background: #409eff;
          transition: width 0.1s linear;
        }
      }

      .time-info {
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        color: #909399;
      }
    }
  }
}
</style>