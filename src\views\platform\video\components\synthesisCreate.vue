<template>
  <div class="app-container">
    <div class="task-card animate__animated animate__fadeIn">
      <div class="task-header">
        <span class="task-title">创建视频合成任务</span>
        <span class="task-title version-tag">V版</span>
      </div>

      <div class="task-content">
        <!-- 左侧区域 -->
        <transition name="slide-fade">
          <div class="content-section left-section">
            <!-- 必需项标题 -->
            <div class="requirements-title">
              <el-icon><info-filled /></el-icon>
              <span>请提供以下3项必要素材进行视频合成</span>
            </div>
            
            <!-- 模型选择区域 -->
            <div class="model-section-box">
              <div class="section-header">
                <div class="section-title">选择模型 <span class="required">*</span></div>
              </div>
              <div class="model-content">
                <el-select v-model="synthesisForm.modelId" placeholder="请选择模型" :loading="modelLoading" class="model-select">
                  <el-option v-for="m in modelList" :key="m.modelCode" :label="m.modelName" :value="m.modelCode">
                    <div class="model-option">
                      <span>{{ m.modelName }}</span>
                      <span class="desc">{{ m.modelDesc }}</span>
                    </div>
                  </el-option>
                </el-select>
              </div>
            </div>
            
            <div class="material-grid">
              <!-- 形象素材区域 -->
              <div class="section-box">
                <div class="section-header">
                  <div class="section-title">形象素材 <span class="required">*</span></div>
                  <el-button v-if="videoFromImage" link type="danger" @click="clearVideoImage" class="clear-btn">
                    <el-icon><delete /></el-icon><span>移除</span>
                  </el-button>
                </div>
                <div class="video-container-compact" v-loading="imageLoading" element-loading-text="正在加载中..." :class="{'has-content': videoFromImage}">
                  <video v-if="videoFromImage" controls class="preview-video" :src="videoFromImage"></video>
                  <div v-else class="empty-media">
                    <div class="empty-media-content">
                      <el-icon><video-camera /></el-icon>
                      <span>您还没有选择形象素材</span>
                      <el-button type="primary" size="small" @click="goToImagePage">选择形象</el-button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 音频素材区域 -->
              <div class="section-box">
                <div class="section-header">
                  <div class="section-title">音频素材 <span class="required">*</span></div>
                  <el-button v-if="audioPreview.savedPath" link type="danger" @click="clearAudio" class="clear-btn">
                    <el-icon><delete /></el-icon><span>移除</span>
                  </el-button>
                </div>
                <div class="audio-container" v-loading="audioLoading" element-loading-text="正在上传中..." :class="{'has-content': audioPreview.savedPath}">
                  <div v-if="audioPreview.savedPath" class="audio-player">
                    <div class="audio-info">
                      <audio controls class="audio-element" :src="audioPreview.url"></audio>
                      <div class="audio-meta">
                        <el-tag size="small">{{ audioPreview.duration }}秒</el-tag>
                        <el-tag type="success" size="small">{{ audioPreview.size }}MB</el-tag>
                      </div>
                    </div>
                  </div>
                  <el-upload v-else class="upload-demo" drag :http-request="uploadAudio" :before-upload="beforeAudioUpload" accept="audio/*" :show-file-list="false">
                    <div class="upload-area-content">
                      <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                      <div class="el-upload__text">将音频文件拖到此处 或 <em>点击上传</em></div>
                      <div class="upload-tip">支持MP3、WAV格式 (≤100MB)</div>
                    </div>
                  </el-upload>
                </div>
              </div>
            </div>

            <!-- 状态检查和提交区域 -->
            <div class="submit-section">
              <div class="status-indicators">
                <div class="status-item" :class="{'completed': synthesisForm.modelId}">
                  <el-icon :class="{'completed': synthesisForm.modelId}">
                    <circle-check v-if="synthesisForm.modelId" />
                    <circle-close v-else />
                  </el-icon>
                  <span>模型</span>
                </div>
                <div class="status-item" :class="{'completed': videoFromImage}">
                  <el-icon :class="{'completed': videoFromImage}">
                    <circle-check v-if="videoFromImage" />
                    <circle-close v-else />
                  </el-icon>
                  <span>形象素材</span>
                </div>
                <div class="status-item" :class="{'completed': audioPreview.url}">
                  <el-icon :class="{'completed': audioPreview.url}">
                    <circle-check v-if="audioPreview.url" />
                    <circle-close v-else />
                  </el-icon>
                  <span>音频素材</span>
                </div>
              </div>
              <div class="buttons-row">
                <el-button type="primary" class="action-btn create-btn" @click="submitSynthesis" :loading="synthesizing" 
                  :disabled="!isReadyToSynthesize || synthesizing">
                  <el-icon><video-play /></el-icon>开始合成
                </el-button>
                <el-button class="action-btn reset-btn" @click="resetForm" :disabled="synthesizing">
                  <el-icon><refresh /></el-icon>重置
                </el-button>
              </div>
            </div>
          </div>
        </transition>

        <!-- 右侧结果区域 -->
        <transition name="slide-fade">
          <div class="content-section right-section" v-loading="synthesizing">
            <div class="section-header result-header">
              <div class="section-title">合成结果</div>
            </div>
            <div class="result-container">
              <video v-if="resultVideo" :src="resultVideo" controls class="result-video animate__animated animate__fadeIn"></video>
              <div v-else class="empty-result animate__animated animate__fadeIn">
                <el-icon><video-camera /></el-icon>
                <span>{{ synthesizing ? '视频正在合成中，请稍候...' : '合成结果将在这里显示' }}</span>
              </div>
            </div>
          </div>
        </transition>
      </div>
    </div>
  </div>
</template>

<script setup name="SynthesisCreate">
import { ref, onMounted, computed, getCurrentInstance, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { createVideoSynthesis, uploadMedia, getAvailableModels, getTaskStatus } from "@/api/platform/video";
import { imageDetail } from "@/api/platform/image";

const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();

// 表单和预览状态
const synthesisForm = ref({ modelId: '', liveVideoUrl: '', liveSoundUrl: ''});
const audioPreview = ref({ url: '', tempUrl: '', duration: 0, size: 0 });
const audioLoading = ref(false);
const synthesizing = ref(false);
const resultVideo = ref('');

// 形象视频相关状态
const videoFromImage = ref('');
const actualVideoPath = ref(''); // 保存实际视频路径
const previousImageId = ref(null); // 记录上一次加载的形象ID
const imageLoading = ref(false); // 新增：形象加载状态

// 模型相关的状态
const modelList = ref([]);
const modelLoading = ref(false);

onMounted(() => {
  loadModels();
  loadImageFromRoute();
});

// 监听路由参数变化，以便在用户选择新形象时刷新
watch(() => route.query, (newQuery) => {
  if (newQuery.imageId && (newQuery.imageId !== previousImageId.value)) {
    loadImageFromRoute();
  }
}, { deep: true });

// 从路由参数加载形象
const loadImageFromRoute = () => {
  const { imageAddress, imageId } = route.query;
  if (imageAddress) {
    loadImageVideo(imageAddress);
    previousImageId.value = imageId;
  }
};

// 加载形象视频
const loadImageVideo = async (imageAddress) => {
  if (!imageAddress) return;
  try {
    imageLoading.value = true; // 开始加载
    clearVideoImage();  // 先清除之前的形象，避免显示旧数据
    const response = await imageDetail(imageAddress);
    if (response.data && response.data.url) {
      videoFromImage.value = response.data.url;
      actualVideoPath.value = response.data.imageAddress || response.data.url;
      synthesisForm.value.liveVideoUrl = actualVideoPath.value;
    }
  } catch (error) {
    console.error('加载形象视频失败:', error);
  } finally {
    imageLoading.value = false; // 结束加载
  }
};

// 清除形象视频的方法
const clearVideoImage = () => {
  videoFromImage.value = '';
  actualVideoPath.value = '';
  synthesisForm.value.liveVideoUrl = '';
};

// 音频上传前检查
const beforeAudioUpload = (file) => {
  const isValid = file.type.startsWith('audio/') && file.size / 1024 / 1024 < 100;
  !isValid && proxy.$modal.msgError(file.type.startsWith('audio/') ? '音频大小不能超过100MB!' : '请上传音频文件!');
  return isValid;
};

// 获取媒体文件实际时长的方法
const getMediaDuration = (file) => {
  return new Promise((resolve) => {
    const media = document.createElement('audio');
    media.preload = 'metadata';
    media.onloadedmetadata = () => resolve(Math.round(media.duration));
    media.src = URL.createObjectURL(file);
  });
};

// 音频上传处理函数
const uploadAudio = async ({file}) => {
  try {
    audioLoading.value = true;
    const duration = await getMediaDuration(file);
    const response = await uploadMedia(file, 'audio');
    if (response.code === 200) {
      const { savedPath, url } = response.data;
      audioPreview.value = { 
        savedPath: savedPath,
        url: url,
        duration: duration, 
        size: (file.size / 1024 / 1024).toFixed(2) 
      };
      console.log(audioPreview.value);
      
      synthesisForm.value.liveSoundUrl = url;
    }
  } catch (error) { 
    console.error(error); 
    proxy.$modal.msgError('上传失败'); 
  } finally { 
    audioLoading.value = false; 
  }
};

// 清除音频
const clearAudio = () => { 
  audioPreview.value = { url: '', tempUrl: '', duration: 0, size: 0 };
  synthesisForm.value.liveSoundUrl = '';
};

// 轮询任务状态的方法
const pollTaskStatus = async (taskNo) => {
  try {
    const res = await getTaskStatus(taskNo);
    if (res.code === 200 && res.data) {
      const { status, videoUrl } = res.data;
      if (status === '3') {  // 成功
        synthesizing.value = false; // 成功时恢复按钮状态
        if (videoUrl) {
          resultVideo.value = videoUrl;
          proxy.$modal.msgSuccess("视频合成成功");
        } else {
          proxy.$modal.msgError("未获取到结果视频地址");
        }
        return true;
      } else if (status === '4') {  // 失败
        synthesizing.value = false; // 失败时恢复按钮状态
        proxy.$modal.msgError("视频合成失败");
        return true;
      } else if (status === '1' || status === '2') {  // 处理中
        return false;  // 继续轮询
      }
    }
    return false;
  } catch (error) {
    console.error('获取任务状态失败:', error);
    synthesizing.value = false; // 出错时恢复按钮状态
    return false;
  }
};

// 提交合成任务方法
const submitSynthesis = () => {
  if (!actualVideoPath.value) {
    return proxy.$modal.msgError('请选择形象素材');
  }
  
  if (!audioPreview.value.url) {
    return proxy.$modal.msgError('请上传音频素材');
  }
  
  if (!synthesisForm.value.modelId) {
    return proxy.$modal.msgError('请选择模型');
  }
  
  // 设置合成中状态，禁用按钮
  synthesizing.value = true;
  const requestData = {
    model: synthesisForm.value.modelId,
    messages: [{
      role: "user",
      content: {
        type: 1,
        video_name: `synthesis_${new Date().getTime()}`,
        live_video_url: actualVideoPath.value,
        live_sound_url: audioPreview.value.url,
        priority: 0
      }
    }]
  };
  
  createVideoSynthesis(requestData)
    .then(response => {
      if (response.code === 200 && response.data) {
        const taskNo = response.data.task_id || response.data.taskNo;
        if (taskNo) {
          proxy.$modal.msgSuccess("任务已创建，正在处理中...");
          
          // 开始轮询任务状态
          let pollCount = 0;
          const maxPolls = 60;
          const pollInterval = setInterval(async () => {
            pollCount++;
            const isDone = await pollTaskStatus(taskNo);
            
            if (isDone || pollCount >= maxPolls) {
              clearInterval(pollInterval);
              if (pollCount >= maxPolls) {
                synthesizing.value = false; // 超时时恢复按钮状态
                proxy.$modal.msgError("任务处理超时，请稍后在任务列表中查看");
              }
            }
          }, 30000);
        } else {
          synthesizing.value = false; // 失败时恢复按钮状态
          proxy.$modal.msgError("创建任务失败：未获取到任务编号");
        }
      } else {
        synthesizing.value = false; // 失败时恢复按钮状态
        proxy.$modal.msgError(response.msg || "创建失败");
      }
    })
    .catch(error => {
      console.error('创建失败:', error);
      proxy.$modal.msgError("创建任务失败");
      synthesizing.value = false; // 出错时恢复按钮状态
    });
};

// 重置表单
const resetForm = () => {
  // 如果正在合成，不允许重置
  if (synthesizing.value) {
    return;
  }
  
  synthesisForm.value = { 
    modelId: '', 
    liveVideoUrl: actualVideoPath.value, // 保留形象视频路径
    liveSoundUrl: ''
  };
  audioPreview.value = { url: '', tempUrl: '', duration: 0, size: 0 };
  resultVideo.value = ''; // 清除结果视频
};

// 获取模型列表
const loadModels = async () => {
  try {
    modelLoading.value = true;
    const { code, data, msg } = await getAvailableModels();
    code === 200 ? modelList.value = data : proxy.$modal.msgError(msg || '获取模型列表失败');
  } catch (error) {
    console.error('获取模型列表失败:', error);
    proxy.$modal.msgError('获取模型列表失败');
  } finally {
    modelLoading.value = false;
  }
};

// 计算属性：是否准备好开始合成
const isReadyToSynthesize = computed(() => {
  return videoFromImage.value && 
         audioPreview.value.url && 
         synthesisForm.value.modelId;
});

// 跳转到形象素材页面
const goToImagePage = () => {
  router.push({ 
    path: "/szbVideo/image"
  });
};

defineExpose({ resetForm });
</script>

<style lang="scss" scoped>
.task-card {
  background: white; 
  border-radius: 12px; 
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05); 
  overflow: hidden; 
  margin-bottom: 20px;
  transition: all 0.3s ease;
  
  &:hover { 
    transform: translateY(-2px); 
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1); 
  }
}

.task-header {
  padding: 16px 20px; 
  background: linear-gradient(to right, #409eff, #66b1ff); 
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between; // 左右对齐
  align-items: center;  
  
  .version-tag {
    font-size: 14px;
    background-color: rgba(255, 255, 255, 0.2);
    padding: 4px 10px;
    border-radius: 12px;
    font-weight: normal;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  }

  .task-title { 
    font-size: 18px; 
    font-weight: 600; 
    color: white; 
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); 
  }
}

.task-content {
  display: grid; 
  grid-template-columns: 1fr 1fr; 
  gap: 24px; 
  padding: 24px; 
  min-height: 700px;
}

.left-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.requirements-title {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 12px;
  background: rgba(64, 158, 255, 0.1);
  border-radius: 6px;
  border-left: 3px solid #409eff;
  margin-bottom: 5px;
  
  .el-icon {
    color: #409eff;
    font-size: 16px;
  }
  
  span {
    font-size: 13px;
    font-weight: 500;
    color: #606266;
  }
}

.material-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  min-height: 240px;
}

.section-box {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-bottom: 1px solid #ebeef5;
  
  .section-title {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    display: flex;
    align-items: center;
    gap: 4px;
    
    .required {
      color: #f56c6c;
    }
  }
  
  .clear-btn {
    padding: 2px 4px;
    font-size: 12px;
    
    &:hover {
      color: #f56c6c;
      background: rgba(245, 108, 108, 0.1);
    }
  }
}

.video-container-compact {
  height: 240px;
  background: #f8f9fa;
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &.has-content {
    background: #000;
  }

  video { 
    width: 100%; 
    height: 100%; 
    object-fit: contain; 
  }

  .empty-media {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .empty-media-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      
      .el-icon {
        font-size: 48px;
        color: #c0c4cc;
      }
      
      span {
        font-size: 14px;
        color: #909399;
        margin-bottom: 5px;
      }
    }
  }
}

.audio-container {
  height: 240px;
  background: #f8f9fa;
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px;
  
  &.has-content {
    background: #fff;
  }
  
  .audio-player {
    width: 100%;
    
    .audio-info {
      width: 100%;
    }
    
    .audio-element {
      width: 100%;
      height: 50px;
      margin-bottom: 15px;
    }
    
    .audio-meta {
      display: flex;
      gap: 8px;
    }
  }
  
  .upload-demo {
    width: 100%;
    height: 100%;
    
    :deep(.el-upload) {
      width: 100%;
      height: 100%;
      display: flex;
    }
    
    :deep(.el-upload-dragger) {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .upload-area-content {
      text-align: center;
      padding: 10px;
      
      .el-icon--upload {
        font-size: 48px;
        color: #909399;
        margin-bottom: 8px;
      }
      
      .el-upload__text {
        font-size: 16px;
        color: #606266;
        margin-bottom: 6px;
        
        em {
          color: #409EFF;
          font-style: normal;
        }
      }
      
      .upload-tip {
        font-size: 12px;
        color: #909399;
      }
    }
  }
}

.model-section-box {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  margin-bottom: 20px;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
  
  .section-header {
    background: linear-gradient(to right, #f0f7ff, #f8f9fa);
    
    .section-title {
      font-weight: 600;
      font-size: 15px;
      color: #303133;
    }
  }
  
  .model-content {
    padding: 16px;
    background: rgba(64, 158, 255, 0.03);
  }
  
  .model-select {
    width: 100%;
    
    :deep(.el-input__wrapper) {
      padding: 0 15px;
      height: 45px;
      box-shadow: 0 0 0 1px #dcdfe6 inset;
      transition: all 0.3s ease;
      
      &:hover {
        box-shadow: 0 0 0 1px #409eff inset;
      }
      
      &.is-focus {
        box-shadow: 0 0 0 1px #409eff inset;
      }
    }
  }
}

.submit-section {
  margin-top: 10px;
  padding: 20px;
  background: linear-gradient(to right, #f8f9fa, #ffffff);
  border-radius: 8px;
  border: 1px solid #ebeef5;
  text-align: center;
  
  .status-indicators {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    
    .status-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      flex: 1;
      
      .el-icon {
        font-size: 24px;
        color: #f56c6c;
        
        &.completed {
          color: #67c23a;
        }
      }
      
      span {
        font-size: 14px;
        color: #909399;
      }
      
      &.completed span {
        color: #67c23a;
      }
    }
  }
  
  .buttons-row {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 15px;
    
    .action-btn {
      flex: 1;
      max-width: 200px;
      height: 42px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      transition: all 0.3s ease;
      font-size: 15px;
      
      .el-icon {
        font-size: 16px;
      }
      
      &:hover:not(:disabled) {
        transform: translateY(-2px);
      }
    }
    
    .create-btn {
      background: linear-gradient(to right, #409eff, #66b1ff);
      border: none;
      
      &:hover:not(:disabled) {
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
      }
      
      &:disabled {
        background: #a0cfff;
        color: #fff;
        cursor: not-allowed;
      }
    }
    
    .reset-btn {
      background: #f4f4f5;
      color: #606266;
      border-color: #dcdfe6;
      
      &:hover {
        color: #409EFF;
        border-color: #c6e2ff;
        background-color: #ecf5ff;
      }
    }
  }
}

.right-section {
  display: flex;
  flex-direction: column;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  overflow: hidden;
  min-height: 620px;
  
  .result-header {
    margin-bottom: 0;
    background: linear-gradient(to right, #f0f7ff, #f8f9fa);
  }
  
  .result-container {
    width: 100%;
    flex-grow: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #000;
    position: relative; /* 添加相对定位 */
    overflow: hidden; /* 防止内容溢出 */
    max-height: 630px; /* 限制最大高度 */
    
    video {
      width: 100%; /* 调整宽度 */
      max-height: 630px; /* 设置视频的最大高度 */
      object-fit: contain;
      margin: auto; /* 确保视频居中 */
    }
    
    .empty-result {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      width: 100%;
      background: #f0f2f5;
      
      .el-icon {
        font-size: 64px;
        color: #c0c4cc;
        margin-bottom: 20px;
      }
      
      span {
        font-size: 16px;
        color: #909399;
        text-align: center;
        max-width: 80%;
      }
    }
  }
}

.slide-fade-enter-active, .slide-fade-leave-active { 
  transition: all 0.3s ease; 
}

.slide-fade-enter-from, .slide-fade-leave-to { 
  opacity: 0; 
  transform: translateX(10px); 
}

.required {
  color: #f56c6c;
}

.model-option { 
  display: flex; 
  justify-content: space-between; 
  align-items: center; 
  width: 100%;
  
  .desc { 
    color: #909399;
    font-size: 12px;
  }
}

.loading-state {
  display: none;
}
</style>