<script setup name="platformVideo">
import { listMusetalk, delMusetalk, getAvailableModels } from "@/api/platform/video";
import { ref, getCurrentInstance, nextTick, watch, onMounted } from 'vue';
import AudioPlayer from './components/AudioPlayer.vue';

const { proxy } = getCurrentInstance();
const { video_status } = proxy.useDict("video_status"); 
const total = ref(0);
const loading = ref(true);
const musetalkList = ref([]);
const isPlaying = ref([]);
const progress = ref([]);
const selectedIds = ref([]);
const ids = ref([]);
const isAllSelected = ref(false);
const queryParams = ref({
  pageNum: 1,
  pageSize: 9,
  number: null,
  status: null
});

// 新增路由路径常量
const ROUTE_PATHS = {
  M: '/szbVideo/createMusetalk',
  V: '/szbVideo/synthesisCreate',
  H: '/szbVideo/createHeygem'
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  console.log(queryParams.value);
  
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

function getList() {
  loading.value = true;
  listMusetalk(queryParams.value).then(response => {
    musetalkList.value = response.rows;
    total.value = response.total;
    loading.value = false;
    // 初始化每个音频的播放状态和进度条
    isPlaying.value = musetalkList.value.map(() => false);
    progress.value = musetalkList.value.map(() => 0);
    
    // 在nextTick中展开所有行
    nextTick(() => {
      const table = document.querySelector('.el-table');
      if (table) {
        const expandBtns = table.querySelectorAll('.el-table__expand-icon');
        expandBtns.forEach(btn => {
          if (!btn.classList.contains('el-table__expand-icon--expanded')) {
            btn.click();
          }
        });
      }
    });
  });
}

// 删除处理函数（支持单个和批量）
function handleDelete(row) {
  const _taskIds = row?.id || selectedIds.value;
  proxy.$modal.confirm('是否确认删除任务编号为"' + _taskIds + '"的数据项？').then(function() {
    return delMusetalk(_taskIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功！");
  }).catch(() => {});
}

// 多选框选中数据
function handleSelectionChange(checked, row) {
  if (checked) {
    selectedIds.value.push(row.id);
  } else {
    const index = selectedIds.value.indexOf(row.id);
    if (index > -1) {
      selectedIds.value.splice(index, 1);
    }
  }
  ids.value = selectedIds.value;
  isAllSelected.value = selectedIds.value.length === musetalkList.value.length;
}

// 批量删除直接调用handleDelete
const handleBatchDelete = () => {
  if (!selectedIds.value.length) {
    proxy.$modal.msgError("请选择要删除的数据");
    return;
  }
  handleDelete();
};

// 修改值文本颜色类获取方法
const getValueColorClass = (value) => {
  const numValue = Number(value) || 0;
  if (numValue < -2) return 'text-danger';
  if (numValue < 2) return 'text-warning';
  return 'text-success';
};

// 全选/取消全选处理
function handleSelectAll(val) {
  selectedIds.value = val ? musetalkList.value.map(item => item.id) : [];
}

function updateIsPlaying(index, value) {
  isPlaying.value[index] = value;
}

function updateProgress(index, value) {
  progress.value[index] = value;
}

// 添加版本选择对话框状态
const versionDialogVisible = ref(false);

// 修改添加按钮处理函数
function handleAddWithVersion() {
  versionDialogVisible.value = true;
}

// 根据选择跳转到不同版本的合成页面 - 修改后的方法
function goToVersion(version) {
  proxy.$router.push({ path: ROUTE_PATHS[version] || ROUTE_PATHS.M });
  versionDialogVisible.value = false;
}

// 添加模型列表状态和获取模型名称的方法
const modelList = ref([]);

// 获取模型名称的方法
function getModelName(modelCode) {
  if (!modelCode) return '未知模型';
  const model = modelList.value.find(m => m.modelCode === modelCode);
  return model ? model.modelName : modelCode;
}

onMounted(() => {
  getList();
  
  // 加载模型列表
  getAvailableModels().then(res => {
    if (res.code === 200) {
      modelList.value = res.data || [];
    }
  }).catch(err => {
    console.error('加载模型列表失败:', err);
  });
});

// 监听列表数据变化，重置选择状态
watch(() => musetalkList.value, () => {
  selectedIds.value = [];
  isAllSelected.value = false;
}, { deep: true });
</script>

<template>
  <div class="synthesis-container">
    <!-- 简化查询表单 -->
    <div class="query-container">
      <el-card class="query-card" shadow="hover">
        <template #header>
          <div class="query-header"><div class="query-title"><el-icon><search /></el-icon><span>搜索</span></div></div>
        </template>
        <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="88px">
          <div class="form-container">
            <el-form-item label="视频名称" prop="number">
              <el-input v-model="queryParams.number" placeholder="请输入视频名称" clearable prefix-icon="search" @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="任务状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择任务状态" clearable style="width: 220px">
                <el-option v-for="dict in video_status" :key="dict.value" :label="dict.label" :value="dict.value">
                  <div class="status-option">
                    <div class="status-indicator"></div>
                    <span>{{ dict.label }}</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
            <div class="query-buttons">
              <el-button type="primary" :icon="Search" @click="handleQuery">搜 索</el-button>
              <el-button type="info" :icon="Refresh" @click="resetQuery">重 置</el-button>
            </div>
          </div>
        </el-form>
      </el-card>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-toolbar">
      <div class="left-actions">
        <el-button type="primary" class="action-btn create-btn" @click="handleAddWithVersion"><el-icon><plus /></el-icon> 视频合成</el-button>
        <el-button type="danger" class="action-btn delete-btn" :disabled="!selectedIds.length" @click="handleBatchDelete">
          <el-icon><delete /></el-icon> 批量删除
          <el-tag v-if="selectedIds.length" effect="dark" class="delete-count">{{selectedIds.length}}</el-tag>
        </el-button>
      </div>
      <div class="right-actions">
        <el-checkbox v-model="isAllSelected" @change="handleSelectAll" class="select-all">全选</el-checkbox>
        <el-divider direction="vertical" />
        <div class="data-summary">共 <span class="data-count">{{ total }}</span> 条数据</div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="tasks-grid">
      <!-- 暂无数据显示区域 -->
      <template v-if="musetalkList.length === 0 && !loading">
        <div class="empty-data">
          <el-empty description="暂无数据" :image-size="120">
            <template #image><el-icon class="empty-icon"><video-camera /></el-icon></template>
            <template #description><p>没有找到符合条件的视频合成任务</p></template>
            <el-button type="primary" @click="handleAddWithVersion"><el-icon><plus /></el-icon>创建视频合成任务</el-button>
          </el-empty>
        </div>
      </template>

      <!-- 任务卡片列表 -->
      <el-card v-for="(item, index) in musetalkList" :key="item.id" class="task-card">
        <div class="card-header">
          <el-checkbox :model-value="selectedIds.includes(item.id)" @change="(val) => handleSelectionChange(val, item)"><h3># {{ item.number }}</h3></el-checkbox>
          <dict-tag :options="video_status" :value="item.status"/>
        </div>

        <div class="media-preview">
          <div class="video-section">
            <div class="section-title"><h4>驱动视频</h4></div>
            <div class="video-wrapper"><video :src="item.drivenVideo" controls class="preview-video"></video></div>
          </div>
          
          <div class="audio-section">
            <AudioPlayer :audioSrc="item.drivenAudio" :index="index" @update:isPlaying="(val) => updateIsPlaying(index, val)" @update:progress="(val) => updateProgress(index, val)"/>
          </div>
        </div>

        <div class="result-section">
          <div class="section-title"><h4>合成结果</h4></div>
          <div class="video-wrapper">
            <template v-if="item.status === 1 || item.status === 2">
              <div class="no-preview">
                <el-icon class="rotating"><loading /></el-icon>
                <span>{{ item.status === 1 ? '等待处理' : '正在生成' }}</span>
              </div>
            </template>
            <video v-else :src="item.resultVideo" controls class="result-video"></video>
          </div>
        </div>

        <div class="card-footer">
          <div class="task-info">
            <template v-if="item.version == 'H' "><span class="model-info">模型: {{ item.version }}</span></template>
            <template v-else-if="item.bboxShiftValue !== null && item.bboxShiftValue !== undefined">
              <span class="model-info">调整值: <span class="value-tag" :class="getValueColorClass(item.bboxShiftValue)">{{ item.bboxShiftValue || 0 }}</span></span>
            </template>
            <template v-else-if="item.model"><span class="model-info">模型: {{ getModelName(item.model) }}</span></template>
            <template v-else><span class="model-info">无数据</span></template>
            &nbsp;
            <span class="creator-info"><el-icon><user /></el-icon> {{ item.createBy || 'admin' }}</span>
            <span class="info-divider">|</span>
            <span class="time-info" :title="item.createdAt"><el-icon><timer/></el-icon>{{parseTime(item.createdAt)}}</span>
          </div>
          <el-button type="danger" link @click="handleDelete(item)"><el-icon><delete /></el-icon>删除</el-button>
        </div>
      </el-card>
    </div>

    <!-- 精简优化版本选择对话框 - 修改后的代码 -->
    <el-dialog v-model="versionDialogVisible" title="选择版本" width="620px" destroy-on-close center align-center custom-class="version-select-dialog">
      <div class="version-wrapper">
        <div class="version-option" @click="goToVersion('M')">
          <div class="version-icon m-icon"><el-icon><video-camera /></el-icon></div>
          <div class="version-title">M版</div>
          <div class="version-desc">基础合成 · 简单操作 · 快速生成</div>
          <el-button type="primary" class="version-btn">选择</el-button>
        </div>
        
        <div class="version-divider"></div>
        
        <div class="version-option" @click="goToVersion('V')">
          <div class="version-badge">推荐</div>
          <div class="version-icon s-icon"><el-icon><film /></el-icon></div>
          <div class="version-title">V版</div>
          <div class="version-desc">高级特效 · 互动能力 · 多模型</div>
          <el-button plain class="version-btn">选择</el-button>
        </div>
        
        <div class="version-divider"></div>
        
        <div class="version-option" @click="goToVersion('H')">
          <div class="version-icon h-icon"><el-icon><magic-stick /></el-icon></div>
          <div class="version-title">H版</div>
          <div class="version-desc">高清画质 · 多语言支持 · AI增强</div>
          <el-button plain class="version-btn">选择</el-button>
        </div>
      </div>
      
      <template #footer>
        <el-button link @click="versionDialogVisible = false">取消</el-button>
      </template>
    </el-dialog>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
  </div>
</template>

<style lang="scss" scoped>
.synthesis-container {
  padding: 20px;
}

// 查询表单样式优化
.query-container {
  margin-bottom: 20px;
  
  .query-card {
    border-radius: 8px;
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }
  
  .query-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .query-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: bold;
      color: #409EFF;
    }
  }
  
  .form-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
  }
  
  .query-buttons {
    display: flex;
    gap: 10px;
    margin-left: auto;
  }
  
  :deep(.el-form-item) {
    margin-bottom: 10px;
    margin-right: 20px;
    
    .el-input__wrapper,
    .el-select .el-input__wrapper {
      box-shadow: 0 0 0 1px #dcdfe6 inset;
      border-radius: 4px;
      transition: all 0.2s;
      
      &:hover, &:focus {
        box-shadow: 0 0 0 1px #409EFF inset;
      }
    }
  }
}

.action-toolbar {
  background: white;
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .left-actions {
    display: flex;
    gap: 10px;
    
    .action-btn {
      display: flex;
      align-items: center;
      gap: 5px;
      border-radius: 4px;
      padding: 10px 15px;
      font-weight: 500;
      transition: all 0.3s;
      
      &:hover {
        transform: translateY(-2px);
      }
      
      &.create-btn {
        background: linear-gradient(to right, #409EFF, #53a8ff);
        border: none;
      }
      
      &.delete-btn {
        position: relative;
        
        .delete-count {
          position: absolute;
          top: -8px;
          right: -8px;
          border-radius: 10px;
          font-size: 12px;
          padding: 0 6px;
          height: 18px;
          line-height: 18px;
          background-color: #fff;
          color: #F56C6C;
          border: 1px solid currentColor;
        }
      }
    }
  }
  
  .right-actions {
    display: flex;
    align-items: center;
    gap: 10px;
    
    .select-all {
      margin-right: 5px;
      font-size: 14px;
      
      :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
        background-color: #409EFF;
        border-color: #409EFF;
      }
    }
    
    .data-summary {
      color: #606266;
      font-size: 14px;
      
      .data-count {
        color: #409EFF;
        font-weight: bold;
        padding: 0 4px;
      }
    }
  }
}

// 状态选择器样式
.status-option {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }
}

// 任务卡片样式
.tasks-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(450px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.task-card {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;

  h3 {
    margin: 0;
    font-size: 18px;
  }
}

.media-preview {
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;
  margin-bottom: 15px;
}

.video-section, .audio-section {
  background: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;

  h4 {
    margin: 0;
    font-size: 14px;
    color: #606266;
  }
}

.value-tag {
  font-size: 16px;
  font-weight: 500;
  padding: 2px 8px;
  border-radius: 4px;

  &.text-danger { color: #f56c6c; }
  &.text-warning { color: #e6a23c; }
  &.text-success { color: #67c23a; }
}

.video-wrapper {
  position: relative;
  width: 100%;
  background: #f8f9fa;
  border-radius: 4px;
  overflow: hidden;
}

.preview-video, .result-video {
  width: 100%;
  height: 240px;
  object-fit: contain;
  background: #000;
  display: block;
}

.no-preview {
  height: 240px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f0f2f5;
  color: #909399;
  font-size: 14px;
  gap: 12px;

  .rotating {
    font-size: 32px;
    animation: rotate 2s linear infinite;
  }
}

.result-section {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #eee;

  .video-wrapper {
    margin-top: 8px;
  }
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.task-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #909399;
  font-size: 14px;
}

.info-divider {
  color: #dcdfe6;
}

.creator-info, .time-info {
  display: flex;
  align-items: center;
  gap: 4px;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

// 版本选择对话框样式
.version-selection {
  padding: 10px;

  .version-tip {
    text-align: center;
    margin-bottom: 20px;
    font-size: 16px;
    color: #606266;
  }

  .version-options {
    display: flex;
    justify-content: space-around;
    gap: 20px;
  }

  .version-card {
    flex: 1;
    text-align: center;
    padding: 20px 0;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
    
    .version-icon {
      margin-bottom: 10px;
      color: #409EFF;
    }
    
    .version-label {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 8px;
      background: linear-gradient(45deg, #409EFF, #6ac6ff);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    
    .version-desc {
      font-size: 12px;
      color: #909399;
    }
  }
}

// 更新暂无数据的样式
.empty-data {
  grid-column: 1 / -1; // 横跨所有列
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: #f9fafc;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
  
  :deep(.el-empty) {
    padding: 40px;
    
    .el-empty__description {
      margin-top: 20px;
      
      p {
        color: #909399;
        font-size: 16px;
        margin-bottom: 10px;
      }
    }
    
    .el-button {
      padding: 12px 24px;
      font-size: 14px;
    }
  }
  
  .empty-icon {
    font-size: 80px;
    color: #c0c4cc;
    animation: float 3s ease-in-out infinite;
  }
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

// 美化后的版本选择对话框样式
:deep(.version-dialog) {
  border-radius: 12px;
  overflow: hidden;
  
  .el-dialog__header {
    padding: 20px 24px;
    margin: 0;
    border-bottom: 1px solid #f0f0f0;
    
    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .el-dialog__headerbtn {
    top: 20px;
    right: 20px;
  }
  
  .el-dialog__body {
    padding: 24px;
  }
  
  .el-dialog__footer {
    border-top: 1px solid #f0f0f0;
    padding: 15px 24px;
  }
}

.version-container {
  .version-options {
    display: flex;
    align-items: center;
    padding: 10px 0;
  }
  
  .version-card {
    flex: 1;
    position: relative;
    padding: 24px;
    border-radius: 10px;
    background: #f9fafc;
    transition: all 0.3s ease;
    cursor: pointer;
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 24px rgba(31, 45, 61, 0.12);
      background: #fff;
    }
    
    .version-content {
      display: flex;
      align-items: flex-start;
      gap: 20px;
    }
    
    .recommend-badge {
      position: absolute;
      top: -10px;
      right: 20px;
      background: linear-gradient(90deg, #409EFF, #53a8ff);
      color: white;
      font-size: 12px;
      padding: 4px 12px;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
    }
  }
  
  .version-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &.m-version {
      background: linear-gradient(135deg, #409EFF, #53a8ff);
      color: white;
      box-shadow: 0 8px 16px rgba(64, 158, 255, 0.3);
    }
    
    &.s-version {
      background: linear-gradient(135deg, #67C23A, #85ce61);
      color: white;
      box-shadow: 0 8px 16px rgba(103, 194, 58, 0.3);
    }
  }
  
  .version-info {
    flex: 1;
    
    h3 {
      margin: 0 0 6px;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
    
    p {
      margin: 0 0 16px;
      color: #606266;
      font-size: 14px;
    }
  }
  
  .feature-list {
    list-style: none;
    padding: 0;
    margin: 0 0 20px;
    
    li {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;
      color: #606266;
      font-size: 14px;
      
      .el-icon {
        color: #67C23A;
      }
    }
  }
  
  .select-btn {
    width: 100%;
    padding: 10px 0;
    font-weight: 500;
    border-radius: 6px;
  }
  
  .version-divider {
    width: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 20px;
    
    .divider-line {
      height: 60px;
      width: 1px;
      background: #dcdfe6;
    }
    
    .divider-text {
      padding: 10px 0;
      color: #909399;
      font-size: 14px;
    }
  }
}

/* 精简优化的版本选择对话框样式 */
:deep(.version-select-dialog) {
  border-radius: 16px; overflow: hidden; box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
  
  .el-dialog__header { padding: 16px; margin: 0; border-bottom: 1px solid #f0f0f0; }
  .el-dialog__body { padding: 30px; }
  .el-dialog__footer { padding: 12px; text-align: center; }
}

.version-wrapper {
  display: flex; align-items: center; justify-content: center; gap: 0;
}

.version-option {
  position: relative; flex: 1; text-align: center; padding: 30px 20px; border-radius: 12px; 
  background: #f9fafc; cursor: pointer; transition: all 0.3s;
  
  &:hover {
    transform: translateY(-6px); background: #fff; box-shadow: 0 10px 30px rgba(0,0,0,0.08);
    .version-title { transform: translateY(-2px); }
    .version-icon { transform: scale(1.1); }
  }
}

.version-badge {
  position: absolute; top: -10px; right: 10px; background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
  color: white; font-size: 12px; padding: 3px 10px; border-radius: 20px; font-weight: 500;
  box-shadow: 0 4px 10px rgba(255, 107, 107, 0.3);
}

.version-icon {
  width: 70px; height: 70px; border-radius: 50%; margin: 0 auto 16px; display: flex; 
  align-items: center; justify-content: center; transition: all 0.3s; font-size: 28px;
  
  &.m-icon { 
    background: linear-gradient(135deg, #409EFF, #53a8ff); color: white; 
    box-shadow: 0 8px 16px rgba(64, 158, 255, 0.2);
  }
  
  &.s-icon { 
    background: linear-gradient(135deg, #67C23A, #85ce61); color: white; 
    box-shadow: 0 8px 16px rgba(103, 194, 58, 0.2);
  }
}

.version-title {
  font-size: 20px; font-weight: 600; margin-bottom: 8px; transition: all 0.3s;
  background: linear-gradient(to right, #303133, #606266);
  background-clip: text; -webkit-background-clip: text; color: transparent;
}

.version-desc {
  color: #909399; font-size: 13px; margin-bottom: 20px;
}

.version-btn {
  min-width: 120px; border-radius: 20px; font-weight: 500;
}

.version-divider {
  width: 1px; height: 140px; background: linear-gradient(to bottom, transparent, #dcdfe6, transparent); 
  margin: 0 15px;
}
.version-icon.h-icon {
  background-color: #67C23A;
}
.version-icon.h-icon el-icon {
  color: white;
}
</style>
