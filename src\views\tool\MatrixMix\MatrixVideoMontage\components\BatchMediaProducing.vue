<template>
  <div class="batch-media-producing">
    <!-- 基础配置面板 -->
    <BasicConfigPanel
      :task-info="taskInfo"
      :text-config="textConfig"
      @update:task-info="taskInfo = $event"
      @update:text-config="textConfig = $event"
    />

    <!-- 媒体管理器 -->
    <MediaManager
      v-model="mediaGroups"
      @media-added="handleMediaAdded"
      @api-config-changed="handleMediaGroupsApiChange"
    />

    <!-- 高级配置面板 -->
    <AdvancedConfigPanel
      :editing-config="editingConfig"
      :output-config="outputConfig"
      @update:editing-config="editingConfig = $event"
      @update:output-config="outputConfig = $event"
    />

    <!-- 模板选择器 -->
    <TemplateSelector
      v-model="selectedTemplate"
      :templates="templateList"
      :loading="templateLoading"
      @template-changed="handleTemplateChanged"
      @api-config-changed="handleTemplateApiChange"
    />
    
    <!-- 配置预设区域 -->
    <div class="preset-section">
      <el-card class="preset-card">
        <h3 class="section-title">
          <i class="el-icon-magic-stick"></i>
          快速配置
        </h3>
        <div class="preset-buttons">
          <el-button @click="applyConfigPreset('shortVideo')" size="small">
            短视频模式
          </el-button>
          <el-button @click="applyConfigPreset('longVideo')" size="small">
            长视频模式
          </el-button>
          <el-button @click="applyConfigPreset('highQuality')" size="small">
            高质量模式
          </el-button>
          <el-button @click="applyConfigPreset('quickMode')" size="small">
            快速模式
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 配置状态监控 -->
    <div class="config-monitor">
      <el-card class="monitor-card">
        <h3 class="section-title">
          <i class="el-icon-monitor"></i>
          配置状态监控
        </h3>
        <div class="monitor-content">
          <div class="monitor-item">
            <span class="monitor-label">视频音量:</span>
            <span class="monitor-value">{{ editingConfig.videoVolume }}%</span>
          </div>
          <div class="monitor-item">
            <span class="monitor-label">旁白音量:</span>
            <span class="monitor-value">{{ editingConfig.speechVolume }}%</span>
          </div>
          <div class="monitor-item">
            <span class="monitor-label">背景音乐:</span>
            <span class="monitor-value">{{ editingConfig.enableBGM ? `${editingConfig.bgmVolume}%` : '关闭' }}</span>
          </div>
          <div class="monitor-item">
            <span class="monitor-label">生成数量:</span>
            <span class="monitor-value">{{ outputConfig.count }}个</span>
          </div>
          <div class="monitor-item">
            <span class="monitor-label">视频时长:</span>
            <span class="monitor-value">{{ outputConfig.maxDuration }}秒</span>
          </div>
          <div class="monitor-item">
            <span class="monitor-label">分辨率:</span>
            <span class="monitor-value">{{ outputConfig.resolution }}</span>
          </div>
          <div class="monitor-item">
            <span class="monitor-label">视频质量:</span>
            <span class="monitor-value">CRF {{ outputConfig.quality }}</span>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 提交按钮区域 -->
    <div class="submit-section">
      <el-card class="submit-card">
        <div class="submit-actions">
          <el-button @click="resetForm" size="large">
            重置表单
          </el-button>

          <!-- 配置管理下拉菜单 -->
          <el-dropdown @command="handleConfigCommand">
            <el-button size="large">
              配置管理 <i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="save">保存当前配置</el-dropdown-item>
                <el-dropdown-item command="preview">预览API参数</el-dropdown-item>
                <el-dropdown-item divided v-if="getSavedConfigs().length > 0">加载配置:</el-dropdown-item>
                <el-dropdown-item
                  v-for="config in getSavedConfigs()"
                  :key="config.name"
                  :command="`load:${config.name}`"
                >
                  {{ config.name }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>

          <el-button
            type="primary"
            size="large"
            @click="submitTask"
            :loading="submitting"
            :disabled="!canSubmit"
          >
            {{ submitting ? '提交中...' : '提交任务' }}
          </el-button>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import BasicConfigPanel from './BatchMediaProducingComponents/BasicConfigPanel.vue';
import MediaManager from './BatchMediaProducingComponents/MediaManager.vue';
import AdvancedConfigPanel from './BatchMediaProducingComponents/AdvancedConfigPanel.vue';
import TemplateSelector from './BatchMediaProducingComponents/TemplateSelector.vue';
import { listTemplates } from '../../api/template';
import { submitBatchMediaProducingJob } from '../../api/batchJob';
import { listMediaBasicInfo } from '../../api/media';
import type {
  MediaItem,
  MediaGroupUI,
  TemplateInfo,
  MediaType,
  ConfigPreset,
  ConfigPresetType,
  TaskInfo,
  TextConfig,
  UIEditingConfig,
  UIOutputConfig
} from '../../types/batchProducing';

// 主页面数据 - 使用动态默认值
const taskInfo = ref({
  taskName: '',
  taskDescription: '',
});

const mediaGroups = ref<MediaGroupUI[]>([
  { name: '媒体组1', media: [] }
]);

// 移除硬编码的form数据，使用响应式的独立数据

// 动态数据状态
const templateLoading = ref(false);
const mediaLoading = ref(false);
const templateList = ref<TemplateInfo[]>([]);
const myMediaList = ref<MediaItem[]>([]);

const selectedTemplate = ref('');

// API配置数据 - 由各个子组件提供
const apiConfigs = ref({
  mediaGroups: [] as any[],
  editingConfig: {} as any,
  outputConfig: {} as any,
  templateConfig: null as any
});

// 响应式数据
const submitting = ref(false);

// 文本配置数据 - 动态可调整
const textConfig = ref<TextConfig>({
  titles: [''],
  speechTexts: ['']
});

// 剪辑配置数据 - 使用合理的默认值，可动态调整
const editingConfig = ref<UIEditingConfig>({
  videoVolume: 50,        // 视频音量 0-100
  speechVolume: 80,       // 旁白音量 0-100
  enableBGM: true,        // 是否启用背景音乐
  bgmVolume: 30,          // 背景音乐音量 0-100
  enableSubtitle: true,   // 是否启用AI字幕
  enableTransition: true, // 是否启用镜头切换特效
  enableSpeechSync: false,// 是否启用语音识别同步字幕
  enableSmartCrop: false  // 是否启用智能裁剪
});

// 输出配置数据 - 可动态调整的输出参数
const outputConfig = ref<UIOutputConfig>({
  count: 10,              // 输出视频数量
  maxDuration: 15,        // 单个视频最大时长(秒)
  resolution: '1080x1920',// 输出分辨率
  quality: 23             // 视频质量 (CRF值，越小质量越高)
});



// 计算属性
const totalMediaCount = computed(() => {
  return mediaGroups.value.reduce((total, group) => {
    return total + group.media.length;
  }, 0);
});

const canSubmit = computed(() => {
  return taskInfo.value.taskName.trim() && 
         totalMediaCount.value > 0 && 
         selectedTemplate.value && 
         outputConfig.value.count > 0;
});

// 动态数据获取方法
const fetchTemplateList = async () => {
  templateLoading.value = true;
  try {
    const response = await listTemplates({
      pageNo: 1,
      pageSize: 50,
      type: 'Timeline',
      status: 'Available'
    });
    
    if (response.code === 200) {
      // 转换API数据格式为组件期望的格式
      templateList.value = response.data.Templates.map(template => ({
        id: template.TemplateId,
        name: template.Name,
        description: template.Status === 'Available' ? '可用模板' : '处理中模板',
        icon: 'el-icon-magic-stick'
      }));
      
      // 设置默认选中的模板
      if (templateList.value.length > 0 && !selectedTemplate.value) {
        selectedTemplate.value = templateList.value[0].id;
      }
      console.log('✅ 模板列表获取成功:', templateList.value.length, '个模板');
    } else {
      ElMessage.error(`获取模板列表失败: ${response.msg}`);
    }
  } catch (error) {
    console.error('❌ 获取模板列表失败:', error);
    ElMessage.error('获取模板列表时发生网络错误');
  } finally {
    templateLoading.value = false;
  }
};

const fetchMediaList = async () => {
  mediaLoading.value = true;
  try {
    const response = await listMediaBasicInfo({
      MaxResults: 100,
      Status: 'Normal',
      IncludeFileBasicInfo: true
    });

    if (response.RequestId) {
      // 转换API数据格式为组件期望的格式
      myMediaList.value = (response.MediaInfos || []).map(media => {
        const fileInfo = media.FileInfoList?.[0];
        const basicInfo = fileInfo?.FileBasicInfo;

        // 根据文件扩展名判断媒体类型
        const getMediaType = (fileName: string): MediaType => {
          const ext = fileName.toLowerCase().split('.').pop() || '';
          if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'].includes(ext)) return 'video';
          if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(ext)) return 'image';
          if (['mp3', 'wav', 'aac', 'flac', 'ogg'].includes(ext)) return 'audio';
          return 'video'; // 默认为视频
        };

        return {
          id: media.MediaId,
          name: basicInfo?.FileName || media.MediaId,
          type: getMediaType(basicInfo?.FileName || ''),
          url: basicInfo?.FileUrl || '',
          thumbnail: basicInfo?.FileUrl || '',
          duration: parseFloat(basicInfo?.Duration || '0') || 0,
          size: formatBytes(parseInt(basicInfo?.FileSize || '0') || 0)
        };
      });

      console.log('✅ 媒体列表加载成功:', myMediaList.value.length, '个媒体');
    } else {
      ElMessage.error('获取媒体列表失败');
    }
  } catch (error) {
    console.error('❌ 获取媒体列表失败:', error);
    ElMessage.error('获取媒体列表时发生网络错误');
  } finally {
    mediaLoading.value = false;
  }
};

// 格式化文件大小的工具函数
const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

// 配置预设 - 提供常用的配置模板
const configPresets: Record<ConfigPresetType, ConfigPreset> = {
  // 短视频模式 - 适合抖音、快手等平台
  shortVideo: {
    editing: {
      videoVolume: 60,
      speechVolume: 85,
      enableBGM: true,
      bgmVolume: 25,
      enableSubtitle: true,
      enableTransition: true,
      enableSpeechSync: false,
      enableSmartCrop: true
    },
    output: {
      count: 20,
      maxDuration: 15,
      resolution: '1080x1920',
      quality: 25
    }
  },

  // 长视频模式 - 适合B站、YouTube等平台
  longVideo: {
    editing: {
      videoVolume: 55,
      speechVolume: 80,
      enableBGM: true,
      bgmVolume: 35,
      enableSubtitle: true,
      enableTransition: false,
      enableSpeechSync: true,
      enableSmartCrop: false
    },
    output: {
      count: 5,
      maxDuration: 120,
      resolution: '1920x1080',
      quality: 20
    }
  },

  // 高质量模式 - 追求最佳画质和音质
  highQuality: {
    editing: {
      videoVolume: 50,
      speechVolume: 75,
      enableBGM: true,
      bgmVolume: 20,
      enableSubtitle: true,
      enableTransition: true,
      enableSpeechSync: true,
      enableSmartCrop: false
    },
    output: {
      count: 3,
      maxDuration: 60,
      resolution: '1920x1080',
      quality: 18
    }
  },

  // 快速模式 - 快速生成，适合测试
  quickMode: {
    editing: {
      videoVolume: 50,
      speechVolume: 80,
      enableBGM: false,
      bgmVolume: 0,
      enableSubtitle: false,
      enableTransition: false,
      enableSpeechSync: false,
      enableSmartCrop: false
    },
    output: {
      count: 3,
      maxDuration: 10,
      resolution: '720x1280',
      quality: 30
    }
  }
};

// 应用配置预设
const applyConfigPreset = (presetName: ConfigPresetType) => {
  const preset = configPresets[presetName];
  if (preset) {
    editingConfig.value = { ...preset.editing };
    outputConfig.value = { ...preset.output };
    console.log(`✅ 已应用${presetName}配置预设`);
    ElMessage.success(`已应用${getPresetDisplayName(presetName)}配置`);
  }
};

// 获取预设显示名称
const getPresetDisplayName = (presetName: ConfigPresetType): string => {
  const names = {
    shortVideo: '短视频模式',
    longVideo: '长视频模式',
    highQuality: '高质量模式',
    quickMode: '快速模式'
  };
  return names[presetName] || presetName;
};

// 处理子组件API配置变化
const handleMediaGroupsApiChange = (config: any[]) => {
  apiConfigs.value.mediaGroups = config;
  console.log('📊 媒体组API配置更新:', config);
};



const handleTemplateApiChange = (config: any) => {
  apiConfigs.value.templateConfig = config;
  console.log('📊 模板API配置更新:', config);
};

// 重置表单 - 恢复到合理的默认值
const resetForm = () => {
  // 重置任务信息
  taskInfo.value = {
    taskName: '',
    taskDescription: ''
  };

  // 重置媒体组
  mediaGroups.value = [{
    name: '媒体组1',
    media: []
  }];

  // 重置文本配置
  textConfig.value = {
    titles: [''],
    speechTexts: ['']
  };

  // 重置剪辑配置到推荐值
  editingConfig.value = {
    videoVolume: 50,        // 视频音量适中
    speechVolume: 80,       // 旁白音量较高，确保清晰
    enableBGM: true,        // 默认启用背景音乐
    bgmVolume: 30,          // 背景音乐音量较低，不抢夺主音频
    enableSubtitle: true,   // 默认启用字幕，提升观看体验
    enableTransition: true, // 默认启用转场效果
    enableSpeechSync: false,// 语音同步字幕默认关闭
    enableSmartCrop: false  // 智能裁剪默认关闭
  };

  // 重置输出配置到常用值
  outputConfig.value = {
    count: 10,              // 默认生成10个视频
    maxDuration: 15,        // 默认15秒，适合短视频
    resolution: '1080x1920',// 默认竖屏高清
    quality: 23             // 默认标准质量
  };

  // 重置模板选择
  selectedTemplate.value = '';

  console.log('✅ 表单已重置到默认值');
  ElMessage.success('表单已重置');
};

// 配置管理 - 保存和加载自定义配置
const saveCustomConfig = () => {
  if (!taskInfo.value.taskName?.trim()) {
    ElMessage.warning('请先输入任务名称');
    return;
  }

  const configData = {
    name: `${taskInfo.value.taskName}_配置`,
    description: taskInfo.value.taskDescription,
    editingConfig: editingConfig.value,
    outputConfig: outputConfig.value,
    textConfig: textConfig.value,
    createTime: new Date().toISOString()
  };

  // 保存到本地存储
  const savedConfigs = JSON.parse(localStorage.getItem('batchMediaConfigs') || '[]');
  savedConfigs.push(configData);
  localStorage.setItem('batchMediaConfigs', JSON.stringify(savedConfigs));

  console.log('✅ 配置保存成功:', configData);
  ElMessage.success(`配置"${configData.name}"保存成功！`);
};

// 加载自定义配置
const loadCustomConfig = (configName: string) => {
  const savedConfigs = JSON.parse(localStorage.getItem('batchMediaConfigs') || '[]');
  const config = savedConfigs.find((c: any) => c.name === configName);

  if (config) {
    editingConfig.value = { ...config.editingConfig };
    outputConfig.value = { ...config.outputConfig };
    textConfig.value = { ...config.textConfig };

    console.log('✅ 配置加载成功:', config);
    ElMessage.success(`配置"${configName}"加载成功！`);
  } else {
    ElMessage.error('配置不存在');
  }
};

// 获取已保存的配置列表
const getSavedConfigs = () => {
  return JSON.parse(localStorage.getItem('batchMediaConfigs') || '[]');
};

// 预览API参数
const previewApiParams = () => {
  try {
    const inputConfig = buildInputConfig();
    const editingConfig = buildEditingConfig();
    const outputConfig = buildOutputConfig();
    const templateConfig = buildTemplateConfig();
    const userData = buildUserData();

    const apiParams = {
      InputConfig: JSON.stringify(inputConfig, null, 2),
      EditingConfig: JSON.stringify(editingConfig, null, 2),
      OutputConfig: JSON.stringify(outputConfig, null, 2),
      TemplateConfig: JSON.stringify(templateConfig, null, 2),
      UserData: JSON.stringify(userData, null, 2)
    };

    // 在控制台输出API参数供调试
    console.log('🔍 InputConfig:', inputConfig);
    console.log('🔍 EditingConfig:', editingConfig);
    console.log('🔍 OutputConfig:', outputConfig);
    console.log('🔍 TemplateConfig:', templateConfig);
    console.log('🔍 UserData:', userData);

    // 使用Element Plus的消息框显示
    ElMessage({
      message: '详细API参数已输出到控制台，请按F12查看',
      type: 'success',
      duration: 3000
    });

    console.log('🔍 API参数预览:', apiParams);
    ElMessage.success('API参数预览已打开新窗口');
  } catch (error) {
    console.error('❌ 生成API参数失败:', error);
    ElMessage.error('生成API参数失败，请检查配置');
  }
};

// 配置命令处理
const handleConfigCommand = (command: string) => {
  if (command === 'save') {
    saveCustomConfig();
  } else if (command === 'preview') {
    previewApiParams();
  } else if (command.startsWith('load:')) {
    const configName = command.substring(5);
    loadCustomConfig(configName);
  }
};

// 移除未使用的saveTemplate函数

const submitTask = async () => {
  submitting.value = true;
  try {
    // 数据验证
    if (!taskInfo.value.taskName?.trim()) {
      ElMessage.warning('请输入任务名称');
      return;
    }

    if (mediaGroups.value.every(group => group.media.length === 0)) {
      ElMessage.warning('请至少添加一个媒体文件');
      return;
    }

    if (!selectedTemplate.value) {
      ElMessage.warning('请选择一个模板');
      return;
    }

    // 构建API请求参数
    let inputConfigObj, editingConfigObj, outputConfigObj, templateConfigObj, userDataObj;

    try {
      inputConfigObj = buildInputConfig();
      editingConfigObj = buildEditingConfig();
      outputConfigObj = buildOutputConfig();
      templateConfigObj = buildTemplateConfig();
      userDataObj = buildUserData();
    } catch (buildError: any) {
      console.error('构建配置参数失败:', buildError);
      ElMessage.error(`配置参数错误: ${buildError?.message || '未知错误'}`);
      return;
    }

    // 转换为JSON字符串
    const inputConfig = JSON.stringify(inputConfigObj);
    const editingConfig = JSON.stringify(editingConfigObj);
    const outputConfig = JSON.stringify(outputConfigObj);
    const templateConfig = JSON.stringify(templateConfigObj);
    const userData = JSON.stringify(userDataObj);

    console.log('📤 提交任务数据:');
    console.log('InputConfig:', inputConfigObj);
    console.log('EditingConfig:', editingConfigObj);
    console.log('OutputConfig:', outputConfigObj);
    console.log('TemplateConfig:', templateConfigObj);
    console.log('UserData:', userDataObj);

    // 验证JSON格式
    try {
      JSON.parse(inputConfig);
      JSON.parse(editingConfig);
      JSON.parse(outputConfig);
    } catch (jsonError) {
      console.error('JSON格式验证失败:', jsonError);
      ElMessage.error('配置参数JSON格式错误');
      return;
    }

    // 调用真实的API
    const response = await submitBatchMediaProducingJob(
      inputConfig,
      editingConfig,
      outputConfig,
      userData,
      templateConfig
    );

    // 处理成功响应
    console.log('✅ 任务提交成功:', response);
    ElMessage.success(`批量成片任务提交成功！任务ID: ${response.JobId}`);

    // 可以在这里添加跳转到任务列表或任务详情页面的逻辑
    // router.push(`/matrix-mix/batch-jobs/${response.JobId}`);

  } catch (error: any) {
    console.error('❌ 任务提交失败:', error);

    // 根据错误类型提供更具体的错误信息
    let errorMessage = '任务提交失败，请重试';
    if (error?.message) {
      if (error.message.includes('参数无效')) {
        errorMessage = '参数配置无效，请检查媒体文件、输出配置等参数';
      } else if (error.message.includes('JSON格式')) {
        errorMessage = 'JSON格式错误，请检查配置参数';
      } else {
        errorMessage = `提交失败: ${error.message}`;
      }
    }

    ElMessage.error(errorMessage);
  } finally {
    submitting.value = false;
  }
};

// 构建API参数的辅助方法 - 使用子组件提供的API配置
const buildInputConfig = () => {
  // 过滤有效的标题和旁白
  const validTitles = textConfig.value.titles.filter(title => title.trim());
  const validSpeeches = textConfig.value.speechTexts.filter(speech => speech.trim());

  // 验证MediaGroupArray
  const mediaGroupArray = apiConfigs.value.mediaGroups || [];
  if (mediaGroupArray.length === 0) {
    throw new Error('至少需要一个媒体组');
  }

  // 验证每个媒体组都有素材
  const validMediaGroups = mediaGroupArray.filter(group =>
    group.MediaArray && group.MediaArray.length > 0
  );

  if (validMediaGroups.length === 0) {
    throw new Error('每个媒体组至少需要一个素材文件');
  }

  const config: any = {
    MediaGroupArray: validMediaGroups
  };

  // 只有当有有效标题时才添加TitleArray
  if (validTitles.length > 0) {
    config.TitleArray = validTitles;
  }

  // 只有当有有效旁白时才添加SpeechTextArray（全局口播模式）
  if (validSpeeches.length > 0) {
    config.SpeechTextArray = validSpeeches;
  }

  console.log('🔧 构建InputConfig:', config);
  return config;
};

// 构建剪辑配置 - 使用子组件提供的API配置
const buildEditingConfig = () => {
  const config = apiConfigs.value.editingConfig || {};

  // 确保必需的配置存在
  const editingConfig = {
    MediaConfig: config.MediaConfig || {
      Volume: 0.5
    },
    SpeechConfig: config.SpeechConfig || {
      Volume: 0.8,
      SpeechRate: 1.0
    },
    BackgroundMusicConfig: config.BackgroundMusicConfig || {
      Volume: 0.3
    },
    ProcessConfig: config.ProcessConfig || {
      SingleShotDuration: 3,
      AllowVfxEffect: false,
      AllowTransition: false,
      AlignmentMode: "AutoSpeed",
      ImageDuration: 2
    },
    ...config
  };

  console.log('🔧 构建EditingConfig:', editingConfig);
  return editingConfig;
};

// 构建输出配置 - 使用子组件提供的API配置
const buildOutputConfig = () => {
  const config = apiConfigs.value.outputConfig || {};

  // 确保必需的配置存在
  const outputConfig = {
    Count: config.Count || 1,
    MaxDuration: config.MaxDuration || 15,
    Width: config.Width || 1080,
    Height: config.Height || 1920,
    Video: config.Video || {
      Crf: 27
    },
    GeneratePreviewOnly: config.GeneratePreviewOnly || false,
    MediaURL: config.MediaURL || `http://matrix-video-output.oss-cn-shanghai.aliyuncs.com/batch-videos/default/video_{index}.mp4`,
    FileName: config.FileName || `batch_video_{index}.mp4`,
    ...config
  };

  console.log('🔧 构建OutputConfig:', outputConfig);
  return outputConfig;
};

// 构建模板配置 - 使用子组件提供的API配置
const buildTemplateConfig = () => {
  return apiConfigs.value.templateConfig;
};

// 构建用户数据配置 - 包含任务统计信息
const buildUserData = () => {
  const validTitles = textConfig.value.titles.filter(title => title.trim());
  const validSpeeches = textConfig.value.speechTexts.filter(speech => speech.trim());
  const totalMediaCount = mediaGroups.value.reduce((total, group) => total + group.media.length, 0);

  return {
    TaskName: taskInfo.value.taskName,
    TaskDescription: taskInfo.value.taskDescription,
    CreateTime: new Date().toISOString(),
    MediaCount: totalMediaCount,              // 总媒体文件数量
    GroupCount: mediaGroups.value.length,     // 媒体组数量
    TitleCount: validTitles.length,           // 有效标题数量
    SpeechCount: validSpeeches.length,        // 有效旁白数量
    ConfigSnapshot: {                         // 配置快照，便于后续分析
      editingConfig: editingConfig.value,
      outputConfig: outputConfig.value
    }
  };
};

// 子组件事件处理函数
const handleMediaAdded = (data: { groupIndex: number; media: any[] }) => {
  console.log('媒体添加:', data);
  // 可以在这里添加额外的逻辑，比如验证文件、统计信息等
};



const handleTemplateChanged = (templateId: string) => {
  console.log('模板变更:', templateId);
  // 可以在这里根据模板调整其他配置
  
  // 根据选中的模板可能需要调整某些配置
  const template = templateList.value.find(t => t.id === templateId);
  if (template) {
    console.log('选中模板:', template.name);
  }
};

// 生命周期
onMounted(async () => {
  console.log('🚀 批量成片页面初始化...');
  
  // 并行加载数据
  await Promise.all([
    fetchTemplateList(),
    fetchMediaList()
  ]);
  
  console.log('✅ 页面初始化完成');
});
</script>

<style scoped>
.batch-media-producing {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px 0;
  background: #f5f7fa;
  /* 不要设置height: 100vh; 不要设置overflow; 不要用flex布局 */
  box-sizing: border-box;
}

.batch-media-producing > .el-card {
  margin-bottom: 24px;
}

.el-card {
  box-sizing: border-box;
  width: 100%;
}

.action-panel {
  margin-bottom: 24px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-bottom: 16px;
}

.status-info {
  display: flex;
  justify-content: center;
  gap: 24px;
  font-size: 14px;
  color: #666;
}

.media-count,
.text-count,
.estimated-time {
  padding: 4px 8px;
  background: #f0f2f5;
  border-radius: 4px;
}

.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 20px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.header-left .title {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-left .subtitle {
  margin: 5px 0 0 0;
  color: #7f8c8d;
  font-size: 14px;
}

.main-content {
  flex: 1;
  display: flex;
  gap: 20px;
  padding: 20px;
  overflow: hidden;
}

.left-panel {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
}

.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
}

.section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.section-title {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
}

.add-btn {
  margin-left: auto;
  color: #409eff;
}

.media-groups {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.media-group {
  border: 2px dashed #e1e8ed;
  border-radius: 8px;
  padding: 15px;
  transition: all 0.3s ease;
}

.media-group:hover {
  border-color: #409eff;
  background: rgba(64, 158, 255, 0.05);
}

.group-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.group-name {
  flex: 1;
}

.media-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.media-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
}

.media-preview {
  width: 60px;
  height: 40px;
  border-radius: 4px;
  overflow: hidden;
  background: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
}

.media-preview img,
.media-preview video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.audio-preview {
  color: #6c757d;
  font-size: 20px;
}

.media-info {
  flex: 1;
}

.media-name {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 2px;
}

.media-meta {
  font-size: 12px;
  color: #6c757d;
}

.add-media-btn {
  width: 100%;
  height: 60px;
  border: 2px dashed #d1d5db;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  transition: all 0.3s ease;
}

.add-media-btn:hover {
  border-color: #409eff;
  color: #409eff;
}

.text-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.text-item {
  display: flex;
  gap: 10px;
  align-items: flex-start;
}

.text-item .el-input {
  flex: 1;
}

.add-text-btn {
  width: 100%;
  height: 50px;
  border: 2px dashed #d1d5db;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  transition: all 0.3s ease;
}

.add-text-btn:hover {
  border-color: #409eff;
  color: #409eff;
}

.template-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 10px;
}

.template-card {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.template-card:hover {
  border-color: #409eff;
  background: rgba(64, 158, 255, 0.05);
}

.template-card.active {
  border-color: #409eff;
  background: rgba(64, 158, 255, 0.1);
}

.template-icon {
  width: 40px;
  height: 40px;
  background: #409eff;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.template-info {
  flex: 1;
}

.template-name {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px;
}

.template-desc {
  font-size: 12px;
  color: #6c757d;
}

.template-check {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 20px;
  height: 20px;
  background: #409eff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
}

.form-tip {
  margin-left: 10px;
  color: #6c757d;
  font-size: 12px;
}

.quality-tips {
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
  font-size: 12px;
  color: #6c757d;
}

.bgm-slider {
  margin-top: 10px;
}

.footer {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 15px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 -2px 20px rgba(0, 0, 0, 0.1);
}

.status-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.status-text {
  color: #27ae60;
  font-weight: 500;
}

.media-count {
  color: #6c757d;
  font-size: 14px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.media-dialog {
  .media-selector {
    .media-tabs {
      .media-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
        max-height: 400px;
        overflow-y: auto;
      }
      
      .media-item-selectable {
        border: 2px solid #e1e8ed;
        border-radius: 8px;
        padding: 10px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
        
        &:hover {
          border-color: #409eff;
        }
        
        &.selected {
          border-color: #409eff;
          background: rgba(64, 158, 255, 0.1);
        }
        
        .media-thumbnail {
          width: 100%;
          height: 80px;
          border-radius: 4px;
          overflow: hidden;
          background: #f8f9fa;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 8px;
          
          img, video {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
          
          .audio-thumbnail {
            color: #6c757d;
            font-size: 24px;
          }
        }
        
        .media-name {
          font-size: 12px;
          color: #2c3e50;
          word-break: break-all;
        }
      }
    }
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}

/* 配置预设区域样式 */
.preset-section {
  margin-top: 24px;
}

.preset-card {
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.preset-buttons {
  display: flex;
  justify-content: center;
  gap: 12px;
  flex-wrap: wrap;
  padding: 16px;
}

.preset-buttons .el-button {
  min-width: 100px;
  height: 36px;
  font-size: 14px;
  border-radius: 6px;
}

/* 配置监控区域样式 */
.config-monitor {
  margin-top: 24px;
}

.monitor-card {
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.monitor-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  padding: 16px;
}

.monitor-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #409eff;
}

.monitor-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.monitor-value {
  font-size: 14px;
  color: #333;
  font-weight: 600;
}

/* 提交区域样式 */
.submit-section {
  margin-top: 24px;
}

.submit-card {
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.submit-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 20px;
}

.submit-actions .el-button {
  min-width: 120px;
  height: 44px;
  font-size: 16px;
  border-radius: 8px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
  }
  
  .left-panel,
  .right-panel {
    flex: none;
  }
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .footer {
    flex-direction: column;
    gap: 15px;
  }
  
  .action-buttons {
    width: 100%;
    justify-content: center;
  }
}
</style>
