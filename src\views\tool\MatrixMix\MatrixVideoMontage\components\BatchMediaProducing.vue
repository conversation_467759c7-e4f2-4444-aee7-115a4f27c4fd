<template>
  <div class="batch-media-producing">
    <TaskInfoForm v-model="taskInfo" />
    <MediaGroupManager 
      v-model="mediaGroups" 
      :available-media="myMediaList" 
      @media-added="handleMediaAdded"
      @media-removed="handleMediaRemoved"
    />
    <TextConfigPanel 
      v-model="textConfig" 
      @titles-changed="handleTitlesChanged"
      @speeches-changed="handleSpeechesChanged"
    />
    <EditingConfigPanel 
      v-model="editingConfig" 
      @config-changed="handleEditingConfigChanged"
    />
    <OutputConfigPanel 
      v-model="outputConfig" 
      @config-changed="handleOutputConfigChanged"
    />
    <TemplateSelector 
      v-model="selectedTemplate" 
      :templates="templateList" 
      :loading="templateLoading" 
      @template-changed="handleTemplateChanged"
    />
    
    <!-- 提交按钮区域 -->
    <div class="submit-section">
      <el-card class="submit-card">
        <div class="submit-actions">
          <el-button @click="resetForm" size="large">
            重置表单
          </el-button>
          <el-button @click="saveTemplate" size="large">
            保存为模板
          </el-button>
          <el-button 
            type="primary" 
            size="large" 
            @click="submitTask"
            :loading="submitting"
            :disabled="!canSubmit"
          >
            {{ submitting ? '提交中...' : '提交任务' }}
          </el-button>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import TaskInfoForm from './BatchMediaProducingComponents/TaskInfoForm.vue';
import MediaGroupManager from './BatchMediaProducingComponents/MediaGroupManager.vue';
import TextConfigPanel from './BatchMediaProducingComponents/TextConfigPanel.vue';
import EditingConfigPanel from './BatchMediaProducingComponents/EditingConfigPanel.vue';
import OutputConfigPanel from './BatchMediaProducingComponents/OutputConfigPanel.vue';
import TemplateSelector from './BatchMediaProducingComponents/TemplateSelector.vue';
import { listTemplates } from '../../api/template';
import { getMediaInfo, batchGetMediaInfos } from '../../api/media';
import { submitBatchMediaProducingJob } from '../../api/batchJob';
import type { TemplateInfo } from '../../types/template';
import type { MediaPayload } from '../../types/media';

// 类型定义
interface MediaItem {
  id: string;
  name: string;
  type: 'video' | 'image' | 'audio';
  url: string;
  thumbnail: string;
  duration: number;
  size: string;
}

interface MediaGroup {
  name: string;
  media: MediaItem[];
}

interface FormData {
  taskName: string;
  taskDescription: string;
  mediaGroups: MediaGroup[];
  selectedTemplate: string;
  outputConfig: {
    count: number;
    maxDuration: number;
    resolution: string;
    quality: number;
  };
  audioConfig: {
    videoVolume: number;
    speechVolume: number;
    enableBGM: boolean;
    bgmVolume: number;
  };
  advancedConfig: {
    enableSubtitle: boolean;
    enableTransition: boolean;
    enableSmartCrop: boolean;
  };
}

// 主页面数据
const taskInfo = ref({
  taskName: '',
  taskDescription: '',
});
const mediaGroups = ref<MediaGroup[]>([
  { name: '媒体组1', media: [] }
]);

// 表单数据
const form = reactive<FormData>({
  taskName: '',
  taskDescription: '',
  mediaGroups: [
    {
      name: '媒体组1',
      media: []
    }
  ],
  selectedTemplate: 'template1',
  outputConfig: {
    count: 10,
    maxDuration: 15,
    resolution: '1080x1920',
    quality: 23
  },
  audioConfig: {
    videoVolume: 50,
    speechVolume: 80,
    enableBGM: true,
    bgmVolume: 30
  },
  advancedConfig: {
    enableSubtitle: true,
    enableTransition: true,
    enableSmartCrop: false
  }
});

// 动态数据状态
const templateLoading = ref(false);
const mediaLoading = ref(false);
const templateList = ref<Array<{
  id: string;
  name: string;
  description: string;
  icon: string;
}>>([]);
const myMediaList = ref<MediaItem[]>([]);

const selectedTemplate = ref('');

// 响应式数据
const submitting = ref(false);

// 文本配置数据
const textConfig = ref({
  titles: [''],
  speechTexts: ['']
});

// 剪辑配置数据
const editingConfig = ref({
  videoVolume: 50,
  speechVolume: 80,
  enableBGM: true,
  bgmVolume: 30,
  enableSubtitle: true,
  enableTransition: true,
  enableSpeechSync: false,
  enableSmartCrop: false
});

// 输出配置数据
const outputConfig = ref({
  count: 10,
  maxDuration: 15,
  resolution: '1080x1920',
  quality: 23
});

// 计算属性
const totalMediaCount = computed(() => {
  return mediaGroups.value.reduce((total, group) => {
    return total + group.media.length;
  }, 0);
});

const canSubmit = computed(() => {
  return taskInfo.value.taskName.trim() && 
         totalMediaCount.value > 0 && 
         selectedTemplate.value && 
         outputConfig.value.count > 0;
});

// 动态数据获取方法
const fetchTemplateList = async () => {
  templateLoading.value = true;
  try {
    const response = await listTemplates({
      pageNo: 1,
      pageSize: 50,
      type: 'Timeline',
      status: 'Available'
    });
    
    if (response.code === 200) {
      // 转换API数据格式为组件期望的格式
      templateList.value = response.data.Templates.map(template => ({
        id: template.TemplateId,
        name: template.Name,
        description: template.Status === 'Available' ? '可用模板' : '处理中模板',
        icon: 'el-icon-magic-stick'
      }));
      
      // 设置默认选中的模板
      if (templateList.value.length > 0 && !selectedTemplate.value) {
        selectedTemplate.value = templateList.value[0].id;
      }
      console.log('✅ 模板列表获取成功:', templateList.value.length, '个模板');
    } else {
      ElMessage.error(`获取模板列表失败: ${response.msg}`);
    }
  } catch (error) {
    console.error('❌ 获取模板列表失败:', error);
    ElMessage.error('获取模板列表时发生网络错误');
  } finally {
    templateLoading.value = false;
  }
};

const fetchMediaList = async () => {
  mediaLoading.value = true;
  try {
    console.log('✅ 媒体列表加载成功:', myMediaList.value.length, '个媒体');
  } catch (error) {
    console.error('❌ 获取媒体列表失败:', error);
  } finally {
    mediaLoading.value = false;
  }
};

// 工具方法
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

// 重置表单
const resetForm = () => {
  taskInfo.value = { taskName: '', taskDescription: '' };
  mediaGroups.value = [{ name: '媒体组1', media: [] }];
  textConfig.value = { titles: [''], speechTexts: [''] };
  editingConfig.value = {
    videoVolume: 50,
    speechVolume: 80,
    enableBGM: true,
    bgmVolume: 30,
    enableSubtitle: true,
    enableTransition: true,
    enableSpeechSync: false,
    enableSmartCrop: false
  };
  outputConfig.value = {
    count: 10,
    maxDuration: 15,
    resolution: '1080x1920',
    quality: 23
  };
  selectedTemplate.value = '';
  console.log('表单已重置');
};

// 保存为模板
const saveTemplate = () => {
  if (!taskInfo.value.taskName?.trim()) {
    ElMessage.warning('请先输入任务名称');
    return;
  }
  
  const templateData = {
    name: `${taskInfo.value.taskName}_模板`,
    description: taskInfo.value.taskDescription,
    editingConfig: editingConfig.value,
    outputConfig: outputConfig.value,
    textConfig: textConfig.value
  };
  
  console.log('保存模板数据:', templateData);
  ElMessage.success('模板保存成功！');
};

const submitTask = async () => {
  submitting.value = true;
  try {
    // 数据验证
    if (!taskInfo.value.taskName?.trim()) {
      ElMessage.warning('请输入任务名称');
      return;
    }
    
    if (mediaGroups.value.every(group => group.media.length === 0)) {
      ElMessage.warning('请至少添加一个媒体文件');
      return;
    }
    
    if (!selectedTemplate.value) {
      ElMessage.warning('请选择一个模板');
      return;
    }

    // 构建API请求参数
    const requestData = {
      InputConfig: JSON.stringify(buildInputConfig()),
      EditingConfig: JSON.stringify(buildEditingConfig()),
      OutputConfig: JSON.stringify(buildOutputConfig()),
      TemplateConfig: JSON.stringify(buildTemplateConfig()),
      UserData: JSON.stringify(buildUserData())
    };
    
    console.log('提交任务数据:', requestData);
    
    // 调用真实的API
    const response = await submitBatchMediaProducingJob(requestData);
    
    // 处理成功响应
    console.log('任务提交成功:', response);
    ElMessage.success(`批量成片任务提交成功！任务ID: ${response.JobId}`);
    
    // 可以在这里添加跳转到任务列表或任务详情页面的逻辑
    // router.push(`/matrix-mix/batch-jobs/${response.JobId}`);
    
  } catch (error) {
    console.error('任务提交失败:', error);
    ElMessage.error('任务提交失败，请重试');
  } finally {
    submitting.value = false;
  }
};

// 构建API参数的辅助方法
const buildInputConfig = () => {
  return {
    MediaGroupArray: mediaGroups.value.map(group => ({
      GroupName: group.name,
      MediaArray: group.media.map(media => media.id),
      Volume: editingConfig.value.videoVolume / 100,
      SplitMode: 'NoSplit' // 默认不拆条
    })),
    TitleArray: textConfig.value.titles.filter(title => title.trim()),
    SpeechTextArray: textConfig.value.speechTexts.filter(speech => speech.trim())
  };
};

const buildEditingConfig = () => {
  return {
    MediaConfig: {
      Volume: editingConfig.value.videoVolume / 100
    },
    SpeechConfig: {
      Volume: editingConfig.value.speechVolume / 100,
      SpeechRate: 1.0, // 默认语速
      PitchRate: 1.0   // 默认音调
    },
    BackgroundMusicConfig: {
      Volume: editingConfig.value.enableBGM ? editingConfig.value.bgmVolume / 100 : 0
    },
    AdvancedConfig: {
      EnableSubtitle: editingConfig.value.enableSubtitle,
      EnableTransition: editingConfig.value.enableTransition,
      EnableSmartCrop: editingConfig.value.enableSmartCrop,
      EnableSpeechSync: editingConfig.value.enableSpeechSync
    }
  };
};

const buildOutputConfig = () => {
  const [width, height] = outputConfig.value.resolution.split('x').map(Number);
  return {
    Count: outputConfig.value.count,
    MaxDuration: outputConfig.value.maxDuration,
    Width: width,
    Height: height,
    Video: {
      Crf: outputConfig.value.quality,
      Codec: 'H.264'
    },
    Audio: {
      Codec: 'AAC',
      Bitrate: 128
    },
    Format: 'mp4'
  };
};

const buildTemplateConfig = () => {
  return {
    TemplateId: selectedTemplate.value,
    TaskName: taskInfo.value.taskName || `批量成片任务_${new Date().toLocaleDateString()}`,
    TaskDescription: taskInfo.value.taskDescription || '批量智能成片任务'
  };
};

// 构建用户数据配置
const buildUserData = () => {
  return {
    TaskName: taskInfo.value.taskName,
    TaskDescription: taskInfo.value.taskDescription,
    CreateTime: new Date().toISOString(),
    MediaCount: mediaGroups.value.reduce((total, group) => total + group.media.length, 0),
    GroupCount: mediaGroups.value.length,
    TitleCount: textConfig.value.titles.filter(title => title.trim()).length,
    SpeechCount: textConfig.value.speechTexts.filter(speech => speech.trim()).length
  };
};

// 子组件事件处理函数
const handleMediaAdded = (data: { groupIndex: number; media: any[] }) => {
  console.log('媒体添加:', data);
  // 可以在这里添加额外的逻辑，比如验证文件、统计信息等
};

const handleMediaRemoved = (data: { groupIndex: number; mediaIndex: number; media: any }) => {
  console.log('媒体移除:', data);
  // 可以在这里添加额外的逻辑
};

const handleTitlesChanged = (titles: string[]) => {
  console.log('标题变更:', titles);
  // 可以在这里验证标题内容
};

const handleSpeechesChanged = (speeches: string[]) => {
  console.log('旁白变更:', speeches);
  // 可以在这里验证旁白内容
};

const handleEditingConfigChanged = (config: any) => {
  console.log('剪辑配置变更:', config);
  // 可以在这里根据配置调整其他设置
};

const handleOutputConfigChanged = (config: any) => {
  console.log('输出配置变更:', config);
  // 可以在这里验证输出参数
};

const handleTemplateChanged = (templateId: string) => {
  console.log('模板变更:', templateId);
  // 可以在这里根据模板调整其他配置
  
  // 根据选中的模板可能需要调整某些配置
  const template = templateList.value.find(t => t.id === templateId);
  if (template) {
    console.log('选中模板:', template.name);
  }
};

// 生命周期
onMounted(async () => {
  console.log('🚀 批量成片页面初始化...');
  
  // 并行加载数据
  await Promise.all([
    fetchTemplateList(),
    fetchMediaList()
  ]);
  
  console.log('✅ 页面初始化完成');
});
</script>

<style scoped>
.batch-media-producing {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px 0;
  background: #f5f7fa;
  /* 不要设置height: 100vh; 不要设置overflow; 不要用flex布局 */
  box-sizing: border-box;
}

.batch-media-producing > .el-card {
  margin-bottom: 24px;
}

.el-card {
  box-sizing: border-box;
  width: 100%;
}

.action-panel {
  margin-bottom: 24px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-bottom: 16px;
}

.status-info {
  display: flex;
  justify-content: center;
  gap: 24px;
  font-size: 14px;
  color: #666;
}

.media-count,
.text-count,
.estimated-time {
  padding: 4px 8px;
  background: #f0f2f5;
  border-radius: 4px;
}

.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 20px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.header-left .title {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-left .subtitle {
  margin: 5px 0 0 0;
  color: #7f8c8d;
  font-size: 14px;
}

.main-content {
  flex: 1;
  display: flex;
  gap: 20px;
  padding: 20px;
  overflow: hidden;
}

.left-panel {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
}

.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
}

.section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.section-title {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
}

.add-btn {
  margin-left: auto;
  color: #409eff;
}

.media-groups {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.media-group {
  border: 2px dashed #e1e8ed;
  border-radius: 8px;
  padding: 15px;
  transition: all 0.3s ease;
}

.media-group:hover {
  border-color: #409eff;
  background: rgba(64, 158, 255, 0.05);
}

.group-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.group-name {
  flex: 1;
}

.media-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.media-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
}

.media-preview {
  width: 60px;
  height: 40px;
  border-radius: 4px;
  overflow: hidden;
  background: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
}

.media-preview img,
.media-preview video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.audio-preview {
  color: #6c757d;
  font-size: 20px;
}

.media-info {
  flex: 1;
}

.media-name {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 2px;
}

.media-meta {
  font-size: 12px;
  color: #6c757d;
}

.add-media-btn {
  width: 100%;
  height: 60px;
  border: 2px dashed #d1d5db;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  transition: all 0.3s ease;
}

.add-media-btn:hover {
  border-color: #409eff;
  color: #409eff;
}

.text-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.text-item {
  display: flex;
  gap: 10px;
  align-items: flex-start;
}

.text-item .el-input {
  flex: 1;
}

.add-text-btn {
  width: 100%;
  height: 50px;
  border: 2px dashed #d1d5db;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  transition: all 0.3s ease;
}

.add-text-btn:hover {
  border-color: #409eff;
  color: #409eff;
}

.template-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 10px;
}

.template-card {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.template-card:hover {
  border-color: #409eff;
  background: rgba(64, 158, 255, 0.05);
}

.template-card.active {
  border-color: #409eff;
  background: rgba(64, 158, 255, 0.1);
}

.template-icon {
  width: 40px;
  height: 40px;
  background: #409eff;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.template-info {
  flex: 1;
}

.template-name {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px;
}

.template-desc {
  font-size: 12px;
  color: #6c757d;
}

.template-check {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 20px;
  height: 20px;
  background: #409eff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
}

.form-tip {
  margin-left: 10px;
  color: #6c757d;
  font-size: 12px;
}

.quality-tips {
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
  font-size: 12px;
  color: #6c757d;
}

.bgm-slider {
  margin-top: 10px;
}

.footer {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 15px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 -2px 20px rgba(0, 0, 0, 0.1);
}

.status-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.status-text {
  color: #27ae60;
  font-weight: 500;
}

.media-count {
  color: #6c757d;
  font-size: 14px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.media-dialog {
  .media-selector {
    .media-tabs {
      .media-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
        max-height: 400px;
        overflow-y: auto;
      }
      
      .media-item-selectable {
        border: 2px solid #e1e8ed;
        border-radius: 8px;
        padding: 10px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
        
        &:hover {
          border-color: #409eff;
        }
        
        &.selected {
          border-color: #409eff;
          background: rgba(64, 158, 255, 0.1);
        }
        
        .media-thumbnail {
          width: 100%;
          height: 80px;
          border-radius: 4px;
          overflow: hidden;
          background: #f8f9fa;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 8px;
          
          img, video {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
          
          .audio-thumbnail {
            color: #6c757d;
            font-size: 24px;
          }
        }
        
        .media-name {
          font-size: 12px;
          color: #2c3e50;
          word-break: break-all;
        }
      }
    }
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}

/* 提交区域样式 */
.submit-section {
  margin-top: 24px;
}

.submit-card {
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.submit-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 20px;
}

.submit-actions .el-button {
  min-width: 120px;
  height: 44px;
  font-size: 16px;
  border-radius: 8px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
  }
  
  .left-panel,
  .right-panel {
    flex: none;
  }
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .footer {
    flex-direction: column;
    gap: 15px;
  }
  
  .action-buttons {
    width: 100%;
    justify-content: center;
  }
}
</style>
