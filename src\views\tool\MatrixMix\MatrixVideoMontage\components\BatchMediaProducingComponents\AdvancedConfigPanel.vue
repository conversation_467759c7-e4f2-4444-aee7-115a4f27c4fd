<template>
  <div class="advanced-config-panel">
    <!-- 剪辑配置 -->
    <el-card class="config-card" shadow="never">
      <template #header>
        <div class="card-header">
          <i class="el-icon-video-camera"></i>
          <span>剪辑配置</span>
        </div>
      </template>
      
      <el-form :model="editingConfig" label-width="120px" size="default">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="视频音量">
              <el-slider 
                v-model="editingConfig.videoVolume"
                :min="0"
                :max="100"
                show-input
                @change="handleEditingConfigChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="语音音量">
              <el-slider 
                v-model="editingConfig.speechVolume"
                :min="0"
                :max="100"
                show-input
                @change="handleEditingConfigChange"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="启用背景音乐">
              <el-switch 
                v-model="editingConfig.enableBGM"
                @change="handleEditingConfigChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="背景音乐音量" v-if="editingConfig.enableBGM">
              <el-slider 
                v-model="editingConfig.bgmVolume"
                :min="0"
                :max="100"
                show-input
                @change="handleEditingConfigChange"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="启用字幕">
              <el-switch 
                v-model="editingConfig.enableSubtitle"
                @change="handleEditingConfigChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="启用转场">
              <el-switch 
                v-model="editingConfig.enableTransition"
                @change="handleEditingConfigChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="智能裁剪">
              <el-switch 
                v-model="editingConfig.enableSmartCrop"
                @change="handleEditingConfigChange"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="语音同步">
              <el-switch 
                v-model="editingConfig.enableSpeechSync"
                @change="handleEditingConfigChange"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 输出配置 -->
    <el-card class="config-card" shadow="never">
      <template #header>
        <div class="card-header">
          <i class="el-icon-upload"></i>
          <span>输出配置</span>
        </div>
      </template>
      
      <el-form :model="outputConfig" label-width="120px" size="default">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="输出模式">
              <el-select
                v-model="outputConfig.outputMode"
                placeholder="请选择输出模式"
                @change="handleOutputConfigChange"
              >
                <el-option label="OSS存储" value="oss" />
                <el-option label="VOD媒资库" value="vod" disabled />
              </el-select>
              <div class="form-tip">VOD功能开发中</div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="输出数量">
              <el-input-number
                v-model="outputConfig.count"
                :min="1"
                :max="100"
                @change="handleOutputConfigChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="最大时长(秒)">
              <el-input-number
                v-model="outputConfig.maxDuration"
                :min="5"
                :max="300"
                @change="handleOutputConfigChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="分辨率">
              <el-select
                v-model="outputConfig.resolution"
                placeholder="请选择分辨率"
                @change="handleOutputConfigChange"
              >
                <el-option label="1080x1920 (竖屏)" value="1080x1920" />
                <el-option label="1920x1080 (横屏)" value="1920x1080" />
                <el-option label="1080x1080 (方屏)" value="1080x1080" />
                <el-option label="720x1280 (竖屏)" value="720x1280" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="视频质量">
              <div class="quality-selector">
                <!-- 质量选择下拉框 -->
                <el-select
                  v-model="qualityPreset"
                  placeholder="请选择视频质量"
                  @change="handlePresetChange"
                  style="width: 200px;"
                >
                  <el-option label="高质量 (CRF 18)" value="high" />
                  <el-option label="标准质量 (CRF 23)" value="medium" />
                  <el-option label="压缩质量 (CRF 28)" value="low" />
                  <el-option label="自定义" value="custom" />
                </el-select>

                <!-- 自定义滑动条 -->
                <div v-if="qualityPreset === 'custom'" class="custom-quality-control">
                  <el-slider
                    v-model="outputConfig.quality"
                    :min="1"
                    :max="51"
                    :step="1"
                    show-tooltip
                    :format-tooltip="formatQualityTooltip"
                    @change="handleOutputConfigChange"
                    style="flex: 1; margin-right: 12px;"
                  />
                  <el-input-number
                    v-model="outputConfig.quality"
                    :min="1"
                    :max="51"
                    :step="1"
                    size="small"
                    style="width: 80px;"
                    @change="handleOutputConfigChange"
                  />
                </div>
              </div>

              <div class="quality-description">
                <span class="quality-text">{{ getQualityDescription(outputConfig.quality) }}</span>
                <span class="quality-hint">CRF值越小质量越高，文件越大</span>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, ref, watch } from 'vue';
import type { UIEditingConfig, UIOutputConfig } from '../../../types/batchProducing';

// Props定义
interface Props {
  editingConfig: UIEditingConfig;
  outputConfig: UIOutputConfig;
}

// Emits定义
interface Emits {
  (e: 'update:editingConfig', value: UIEditingConfig): void;
  (e: 'update:outputConfig', value: UIOutputConfig): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 质量预设状态
const qualityPreset = ref<'high' | 'medium' | 'low' | 'custom'>('medium');

// 质量预设映射
const qualityPresets = {
  high: 18,
  medium: 23,
  low: 28
};

// 初始化预设状态
const initQualityPreset = () => {
  const currentQuality = props.outputConfig.quality;
  if (currentQuality === qualityPresets.high) {
    qualityPreset.value = 'high';
  } else if (currentQuality === qualityPresets.medium) {
    qualityPreset.value = 'medium';
  } else if (currentQuality === qualityPresets.low) {
    qualityPreset.value = 'low';
  } else {
    qualityPreset.value = 'custom';
  }
};

// 监听质量变化，更新预设状态（但不在自定义模式下触发）
watch(() => props.outputConfig.quality, () => {
  // 如果当前是自定义模式，不要重新检测预设
  if (qualityPreset.value !== 'custom') {
    initQualityPreset();
  }
});

// 初始化
initQualityPreset();

// 剪辑配置变更处理
const handleEditingConfigChange = () => {
  emit('update:editingConfig', { ...props.editingConfig });
};

// 输出配置变更处理
const handleOutputConfigChange = () => {
  emit('update:outputConfig', { ...props.outputConfig });
};

// 预设变更处理
const handlePresetChange = (preset: 'high' | 'medium' | 'low' | 'custom') => {
  if (preset !== 'custom') {
    // 更新质量值
    const newConfig = { ...props.outputConfig };
    newConfig.quality = qualityPresets[preset];
    emit('update:outputConfig', newConfig);
  }
};

// 视频质量相关方法
const formatQualityTooltip = (value: number): string => {
  return `CRF ${value} - ${getQualityDescription(value)}`;
};

const getQualityDescription = (crf: number): string => {
  if (crf <= 18) return '极高质量';
  if (crf <= 23) return '高质量';
  if (crf <= 28) return '标准质量';
  if (crf <= 35) return '中等质量';
  if (crf <= 40) return '低质量';
  return '极低质量';
};
</script>

<style scoped>
.advanced-config-panel {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.config-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-card__header) {
  background-color: #fafafa;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-slider) {
  margin-right: 20px;
}

.quality-control {
  display: flex;
  align-items: center;
  gap: 16px;
}

.quality-description {
  margin-top: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quality-text {
  font-weight: 500;
  color: #409eff;
}

.quality-hint {
  font-size: 12px;
  color: #909399;
}

.quality-selector {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.custom-quality-control {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 12px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}
</style>
