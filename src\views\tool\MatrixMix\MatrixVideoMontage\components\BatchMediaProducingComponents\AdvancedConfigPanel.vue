<template>
  <div class="advanced-config-panel">
    <!-- 剪辑配置 -->
    <el-card class="config-card" shadow="never">
      <template #header>
        <div class="card-header">
          <i class="el-icon-video-camera"></i>
          <span>剪辑配置</span>
        </div>
      </template>
      
      <el-form :model="editingConfig" label-width="120px" size="default">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="视频音量">
              <el-slider 
                v-model="editingConfig.videoVolume"
                :min="0"
                :max="100"
                show-input
                @change="handleEditingConfigChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="语音音量">
              <el-slider 
                v-model="editingConfig.speechVolume"
                :min="0"
                :max="100"
                show-input
                @change="handleEditingConfigChange"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="启用背景音乐">
              <el-switch 
                v-model="editingConfig.enableBGM"
                @change="handleEditingConfigChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="背景音乐音量" v-if="editingConfig.enableBGM">
              <el-slider 
                v-model="editingConfig.bgmVolume"
                :min="0"
                :max="100"
                show-input
                @change="handleEditingConfigChange"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="启用字幕">
              <el-switch 
                v-model="editingConfig.enableSubtitle"
                @change="handleEditingConfigChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="启用转场">
              <el-switch 
                v-model="editingConfig.enableTransition"
                @change="handleEditingConfigChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="智能裁剪">
              <el-switch 
                v-model="editingConfig.enableSmartCrop"
                @change="handleEditingConfigChange"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="语音同步">
              <el-switch 
                v-model="editingConfig.enableSpeechSync"
                @change="handleEditingConfigChange"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 输出配置 -->
    <el-card class="config-card" shadow="never">
      <template #header>
        <div class="card-header">
          <i class="el-icon-upload"></i>
          <span>输出配置</span>
        </div>
      </template>
      
      <el-form :model="outputConfig" label-width="120px" size="default">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="输出数量">
              <el-input-number
                v-model="outputConfig.count"
                :min="1"
                :max="100"
                @change="handleOutputConfigChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最大时长(秒)">
              <el-input-number
                v-model="outputConfig.maxDuration"
                :min="5"
                :max="300"
                @change="handleOutputConfigChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="分辨率">
              <el-select
                v-model="outputConfig.resolution"
                placeholder="请选择分辨率"
                @change="handleOutputConfigChange"
              >
                <el-option label="1080x1920 (竖屏)" value="1080x1920" />
                <el-option label="1920x1080 (横屏)" value="1920x1080" />
                <el-option label="1080x1080 (方屏)" value="1080x1080" />
                <el-option label="720x1280 (竖屏)" value="720x1280" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="视频质量">
              <el-select
                v-model="outputConfig.quality"
                placeholder="请选择视频质量"
                @change="handleOutputConfigChange"
              >
                <el-option label="高质量 (CRF 18)" :value="18" />
                <el-option label="标准质量 (CRF 23)" :value="23" />
                <el-option label="压缩质量 (CRF 28)" :value="28" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';
import type { UIEditingConfig, UIOutputConfig } from '../../../types/batchProducing';

// Props定义
interface Props {
  editingConfig: UIEditingConfig;
  outputConfig: UIOutputConfig;
}

// Emits定义
interface Emits {
  (e: 'update:editingConfig', value: UIEditingConfig): void;
  (e: 'update:outputConfig', value: UIOutputConfig): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 剪辑配置变更处理
const handleEditingConfigChange = () => {
  emit('update:editingConfig', { ...props.editingConfig });
};

// 输出配置变更处理
const handleOutputConfigChange = () => {
  emit('update:outputConfig', { ...props.outputConfig });
};
</script>

<style scoped>
.advanced-config-panel {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.config-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-card__header) {
  background-color: #fafafa;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-slider) {
  margin-right: 20px;
}
</style>
