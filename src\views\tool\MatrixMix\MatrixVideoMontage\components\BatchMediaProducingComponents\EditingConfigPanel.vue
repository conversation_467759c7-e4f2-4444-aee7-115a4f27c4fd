<template>
  <el-card class="editing-config-panel">
    <h3 class="section-title">
      <i class="el-icon-setting"></i>
      剪辑配置
    </h3>
    <el-form :model="form" label-width="120px">
      <el-form-item label="视频音量">
        <el-slider v-model="form.videoVolume" :min="0" :max="100" show-input input-size="small" />
      </el-form-item>
      <el-form-item label="旁白音量">
        <el-slider v-model="form.speechVolume" :min="0" :max="100" show-input input-size="small" />
      </el-form-item>
      <el-form-item label="背景音乐">
        <el-switch v-model="form.enableBGM" />
        <el-slider v-if="form.enableBGM" v-model="form.bgmVolume" :min="0" :max="100" show-input input-size="small" class="bgm-slider" />
      </el-form-item>
      <el-form-item label="AI字幕">
        <el-switch v-model="form.enableSubtitle" />
      </el-form-item>
      <el-form-item label="镜头切换特效">
        <el-switch v-model="form.enableTransition" />
      </el-form-item>
      <el-form-item label="语音识别同步字幕">
        <el-switch v-model="form.enableSpeechSync" />
      </el-form-item>
      <el-form-item label="智能裁剪">
        <el-switch v-model="form.enableSmartCrop" />
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script setup lang="ts">
import { ref, watch, defineProps, defineEmits } from 'vue';

const props = defineProps<{
  modelValue: {
    videoVolume: number;
    speechVolume: number;
    enableBGM: boolean;
    bgmVolume: number;
    enableSubtitle: boolean;
    enableTransition: boolean;
    enableSpeechSync: boolean;
    enableSmartCrop: boolean;
  }
}>();
const emit = defineEmits(['update:modelValue', 'config-changed']);

const form = ref({ ...props.modelValue });

watch(
  () => props.modelValue,
  (val) => {
    form.value = { ...val };
  }
);
watch(
  form,
  (val) => {
    emit('update:modelValue', val);
    emit('config-changed', val);
  },
  { deep: true }
);
</script>

<style scoped>
.editing-config-panel {
  margin-bottom: 20px;
}
.section-title {
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
}
.bgm-slider {
  margin-top: 10px;
}
</style> 