<template>
  <el-card class="media-group-manager">
    <h3 class="section-title">
      <i class="el-icon-picture"></i>
      媒体素材
      <el-button type="text" @click="addMediaGroup" class="add-btn">
        <i class="el-icon-plus"></i>
        添加媒体组
      </el-button>
    </h3>
    <div class="media-groups">
      <div v-for="(group, groupIndex) in groups" :key="groupIndex" class="media-group">
        <div class="group-header">
          <el-input v-model="group.name" placeholder="媒体组名称" class="group-name" />
          <el-button type="danger" size="small" @click="removeMediaGroup(groupIndex)" icon="el-icon-delete"
            v-if="groups.length > 1" />
        </div>
        <div class="media-list">
          <div v-for="(media, mediaIndex) in group.media" :key="media.id" class="media-item">
            <div class="media-preview">
              <img v-if="media.type === 'image'" :src="media.thumbnail" alt="图片预览" />
              <video v-else-if="media.type === 'video'" :src="media.url" muted />
              <div v-else class="audio-preview">
                <i class="el-icon-headset"></i>
              </div>
            </div>
            <div class="media-info">
              <div class="media-name">{{ media.name }}</div>
              <div class="media-meta">{{ media.duration }}s | {{ media.size }}</div>
            </div>
            <el-button type="danger" size="mini" @click="removeMedia(groupIndex, mediaIndex)" icon="el-icon-delete" />
            <el-button type="primary" size="mini" @click="moveMedia(groupIndex, mediaIndex, -1)" icon="el-icon-arrow-up"
              :disabled="mediaIndex === 0" />
            <el-button type="primary" size="mini" @click="moveMedia(groupIndex, mediaIndex, 1)"
              icon="el-icon-arrow-down" :disabled="mediaIndex === group.media.length - 1" />
          </div>
          <el-button type="dashed" @click="openMediaSelector(groupIndex)" class="add-media-btn">
            <i class="el-icon-plus"></i>
            添加素材
          </el-button>
        </div>
      </div>
    </div>
    <MediaSelectorDialog
      v-model:visible="mediaSelectorVisible"
      :available-media="mediaLibrary"
      :loading="mediaLoading"
      @confirm="onMediaSelected"
      @refresh="loadMediaLibrary"
    />
  </el-card>
</template>

<script setup lang="ts">
import { ref, watch, computed, defineProps, defineEmits, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import MediaSelectorDialog from '../MediaSelectorDialog.vue';
import { listMediaBasicInfo } from '../../../api/media';
import type { MediaItem, MediaGroupUI, MediaGroup } from '../../../types/batchProducing';

const props = defineProps<{
  modelValue: MediaGroupUI[];
  availableMedia?: MediaItem[];
}>();
const emit = defineEmits([
  'update:modelValue',
  'api-config-changed',
  'media-added',
  'media-removed',
  'group-added',
  'group-removed'
]);

const groups = ref<MediaGroupUI[]>(JSON.parse(JSON.stringify(props.modelValue)));
const mediaLibrary = ref<MediaItem[]>([]);
const mediaLoading = ref(false);

// 计算API所需的MediaGroup格式
const apiMediaGroups = computed<MediaGroup[]>(() => {
  return groups.value
    .filter(group => group.media.length > 0) // 过滤空组
    .map(group => ({
      GroupName: group.name,
      MediaArray: group.media.map(media => media.id), // 只传递媒体ID
      SplitMode: 'NoSplit' as const, // 默认不拆条
      Volume: 1.0 // 默认音量
    }));
});

// 监听数据变化，同时更新UI和API配置
watch(
  () => props.modelValue,
  (val) => {
    groups.value = JSON.parse(JSON.stringify(val));
  }
);

watch(
  groups,
  (val) => {
    const uiGroups = JSON.parse(JSON.stringify(val));
    emit('update:modelValue', uiGroups);
    emit('api-config-changed', apiMediaGroups.value);
  },
  { deep: true }
);

// 监听API配置变化
watch(
  apiMediaGroups,
  (val) => {
    emit('api-config-changed', val);
  },
  { deep: true }
);

// 加载媒体库
const loadMediaLibrary = async () => {
  mediaLoading.value = true;
  try {
    const response = await listMediaBasicInfo({
      MaxResults: 100,
      Status: 'Normal',
      IncludeFileBasicInfo: true
    });

    if (response.RequestId) {
      mediaLibrary.value = (response.MediaInfos || []).map(media => {
        const fileInfo = media.FileInfoList?.[0];
        const basicInfo = fileInfo?.FileBasicInfo;

        // 根据文件扩展名判断媒体类型
        const getMediaType = (fileName: string): 'video' | 'image' | 'audio' => {
          const ext = fileName.toLowerCase().split('.').pop() || '';
          if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'].includes(ext)) return 'video';
          if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(ext)) return 'image';
          if (['mp3', 'wav', 'aac', 'flac', 'ogg'].includes(ext)) return 'audio';
          return 'video';
        };

        return {
          id: media.MediaId,
          name: basicInfo?.FileName || media.MediaId,
          type: getMediaType(basicInfo?.FileName || ''),
          url: basicInfo?.FileUrl || '',
          thumbnail: basicInfo?.FileUrl || '',
          duration: parseFloat(basicInfo?.Duration || '0') || 0,
          size: formatBytes(parseInt(basicInfo?.FileSize || '0') || 0)
        };
      });

      console.log('✅ 媒体库加载成功:', mediaLibrary.value.length, '个媒体');
    }
  } catch (error) {
    console.error('❌ 加载媒体库失败:', error);
    ElMessage.error('加载媒体库失败');
  } finally {
    mediaLoading.value = false;
  }
};

// 格式化文件大小
const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

// 组件挂载时加载媒体库
onMounted(() => {
  loadMediaLibrary();
});

const addMediaGroup = () => {
  groups.value.push({ name: `媒体组${groups.value.length + 1}`, media: [] });
  emit('group-added', groups.value.length - 1);
};
const removeMediaGroup = (index: number) => {
  if (groups.value.length > 1) {
    groups.value.splice(index, 1);
    emit('group-removed', index);
  }
};
const removeMedia = (groupIndex: number, mediaIndex: number) => {
  const removedMedia = groups.value[groupIndex].media[mediaIndex];
  groups.value[groupIndex].media.splice(mediaIndex, 1);
  emit('media-removed', { groupIndex, mediaIndex, media: removedMedia });
};
const moveMedia = (groupIndex: number, mediaIndex: number, direction: number) => {
  const arr = groups.value[groupIndex].media;
  const target = mediaIndex + direction;
  if (target < 0 || target >= arr.length) return;
  [arr[mediaIndex], arr[target]] = [arr[target], arr[mediaIndex]];
};

const mediaSelectorVisible = ref(false);
const currentGroupIndex = ref(0);
const openMediaSelector = (groupIndex: number) => {
  currentGroupIndex.value = groupIndex;
  mediaSelectorVisible.value = true;
};
const onMediaSelected = (selected: MediaItem[]) => {
  groups.value[currentGroupIndex.value].media.push(...selected);
  emit('media-added', { 
    groupIndex: currentGroupIndex.value, 
    media: selected 
  });
  mediaSelectorVisible.value = false;
};
</script>

<style scoped>
.media-group-manager {
  margin-bottom: 20px;
}

.section-title {
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
}

.add-btn {
  margin-left: auto;
  color: #409eff;
}

.media-groups {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.media-group {
  border: 2px dashed #e1e8ed;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
}

.group-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.group-name {
  flex: 1;
}

.media-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.media-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
}

.media-preview {
  width: 60px;
  height: 40px;
  border-radius: 4px;
  overflow: hidden;
  background: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
}

.media-preview img,
.media-preview video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.audio-preview {
  color: #6c757d;
  font-size: 20px;
}

.media-info {
  flex: 1;
}

.media-name {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 2px;
}

.media-meta {
  font-size: 12px;
  color: #6c757d;
}

.add-media-btn {
  width: 100%;
  height: 60px;
  border: 2px dashed #d1d5db;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  transition: all 0.3s ease;
}

.add-media-btn:hover {
  border-color: #409eff;
  color: #409eff;
}
</style>