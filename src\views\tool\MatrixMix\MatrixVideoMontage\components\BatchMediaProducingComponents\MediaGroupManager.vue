<template>
  <el-card class="media-group-manager">
    <h3 class="section-title">
      <i class="el-icon-picture"></i>
      媒体素材
      <el-button type="text" @click="addMediaGroup" class="add-btn">
        <i class="el-icon-plus"></i>
        添加媒体组
      </el-button>
    </h3>
    <div class="media-groups">
      <div v-for="(group, groupIndex) in groups" :key="groupIndex" class="media-group">
        <div class="group-header">
          <el-input v-model="group.name" placeholder="媒体组名称" class="group-name" />
          <el-button type="danger" size="small" @click="removeMediaGroup(groupIndex)" icon="el-icon-delete"
            v-if="groups.length > 1" />
        </div>
        <div class="media-list">
          <div v-for="(media, mediaIndex) in group.media" :key="media.id" class="media-item">
            <div class="media-preview">
              <img v-if="media.type === 'image'" :src="media.thumbnail" alt="图片预览" />
              <video v-else-if="media.type === 'video'" :src="media.url" muted />
              <div v-else class="audio-preview">
                <i class="el-icon-headset"></i>
              </div>
            </div>
            <div class="media-info">
              <div class="media-name">{{ media.name }}</div>
              <div class="media-meta">{{ media.duration }}s | {{ media.size }}</div>
            </div>
            <el-button type="danger" size="mini" @click="removeMedia(groupIndex, mediaIndex)" icon="el-icon-delete" />
            <el-button type="primary" size="mini" @click="moveMedia(groupIndex, mediaIndex, -1)" icon="el-icon-arrow-up"
              :disabled="mediaIndex === 0" />
            <el-button type="primary" size="mini" @click="moveMedia(groupIndex, mediaIndex, 1)"
              icon="el-icon-arrow-down" :disabled="mediaIndex === group.media.length - 1" />
          </div>
          <el-button type="dashed" @click="openMediaSelector(groupIndex)" class="add-media-btn">
            <i class="el-icon-plus"></i>
            添加素材
          </el-button>
        </div>
      </div>
    </div>
    <MediaSelectorDialog
      :visible="mediaSelectorVisible"
      @update:visible="mediaSelectorVisible = $event"
      @confirm="onMediaSelected"
    />
  </el-card>
</template>

<script setup lang="ts">
import { ref, watch, defineProps, defineEmits } from 'vue';
import MediaSelectorDialog from '../MediaSelectorDialog.vue';
import type { MediaItem, MediaGroupUI } from '../../../types/batchProducing';

const props = defineProps<{
  modelValue: MediaGroupUI[];
  availableMedia?: MediaItem[];
}>();
const emit = defineEmits([
  'update:modelValue',
  'api-config-changed',
  'media-added',
  'media-removed',
  'group-added',
  'group-removed'
]);

const groups = ref<MediaGroupUI[]>(JSON.parse(JSON.stringify(props.modelValue)));



// 监听props变化，更新本地数据
watch(
  () => props.modelValue,
  (val) => {
    groups.value = JSON.parse(JSON.stringify(val));
  },
  { deep: true }
);

// 监听本地数据变化，更新父组件
watch(
  groups,
  (val) => {
    const uiGroups = JSON.parse(JSON.stringify(val));
    emit('update:modelValue', uiGroups);
    // 直接计算API配置，避免重复监听
    const apiConfig = val
      .filter(group => group.media.length > 0)
      .map(group => ({
        GroupName: group.name,
        MediaArray: group.media.map(media => media.id),
        SplitMode: 'NoSplit' as const,
        Volume: 1.0
      }));
    emit('api-config-changed', apiConfig);
  },
  { deep: true }
);

// MediaSelectorDialog现在会自己处理媒体库加载

const addMediaGroup = () => {
  groups.value.push({ name: `媒体组${groups.value.length + 1}`, media: [] });
  emit('group-added', groups.value.length - 1);
};
const removeMediaGroup = (index: number) => {
  if (groups.value.length > 1) {
    groups.value.splice(index, 1);
    emit('group-removed', index);
  }
};
const removeMedia = (groupIndex: number, mediaIndex: number) => {
  const removedMedia = groups.value[groupIndex].media[mediaIndex];
  groups.value[groupIndex].media.splice(mediaIndex, 1);
  emit('media-removed', { groupIndex, mediaIndex, media: removedMedia });
};
const moveMedia = (groupIndex: number, mediaIndex: number, direction: number) => {
  const arr = groups.value[groupIndex].media;
  const target = mediaIndex + direction;
  if (target < 0 || target >= arr.length) return;
  [arr[mediaIndex], arr[target]] = [arr[target], arr[mediaIndex]];
};

const mediaSelectorVisible = ref(false);
const currentGroupIndex = ref(0);
const isOpening = ref(false); // 防止重复打开

const openMediaSelector = async (groupIndex: number) => {
  // 防止重复打开
  if (isOpening.value || mediaSelectorVisible.value) {
    console.log('⚠️ 弹窗正在打开或已打开，忽略重复请求');
    return;
  }

  isOpening.value = true;
  console.log('🔍 打开素材选择器，组索引:', groupIndex);

  currentGroupIndex.value = groupIndex;
  mediaSelectorVisible.value = true;

  console.log('📱 弹窗状态设置为:', mediaSelectorVisible.value);

  // 短暂延迟后重置防抖标志
  setTimeout(() => {
    isOpening.value = false;
  }, 500);
};
const onMediaSelected = (selected: MediaItem[]) => {
  console.log('📥 接收到选中的素材:', selected);
  console.log('📍 当前组索引:', currentGroupIndex.value);

  // 添加素材到指定组
  groups.value[currentGroupIndex.value].media.push(...selected);

  console.log('📊 更新后的组数据:', groups.value[currentGroupIndex.value]);

  // 发送事件通知
  emit('media-added', {
    groupIndex: currentGroupIndex.value,
    media: selected
  });

  // 关闭弹窗
  mediaSelectorVisible.value = false;
  console.log('🔒 弹窗已关闭');
};
</script>

<style scoped>
.media-group-manager {
  margin-bottom: 20px;
}

.section-title {
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
}

.add-btn {
  margin-left: auto;
  color: #409eff;
}

.media-groups {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.media-group {
  border: 2px dashed #e1e8ed;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
}

.group-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.group-name {
  flex: 1;
}

.media-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.media-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
}

.media-preview {
  width: 60px;
  height: 40px;
  border-radius: 4px;
  overflow: hidden;
  background: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
}

.media-preview img,
.media-preview video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.audio-preview {
  color: #6c757d;
  font-size: 20px;
}

.media-info {
  flex: 1;
}

.media-name {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 2px;
}

.media-meta {
  font-size: 12px;
  color: #6c757d;
}

.add-media-btn {
  width: 100%;
  height: 60px;
  border: 2px dashed #d1d5db;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  transition: all 0.3s ease;
}

.add-media-btn:hover {
  border-color: #409eff;
  color: #409eff;
}
</style>