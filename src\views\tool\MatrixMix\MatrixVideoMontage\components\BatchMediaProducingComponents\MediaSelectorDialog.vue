<template>
  <el-dialog :model-value="visible" title="选择素材" width="800px" @close="handleClose">
    <div class="media-selector-toolbar">
      <el-input v-model="search" placeholder="搜索素材名称" clearable class="search-input" />
      <el-select v-model="typeFilter" placeholder="筛选类型" class="type-select" clearable>
        <el-option label="全部" value="" />
        <el-option label="视频" value="video" />
        <el-option label="图片" value="image" />
        <el-option label="音频" value="audio" />
      </el-select>
    </div>
    
    <div class="media-grid">
      <div 
        v-for="media in filteredMediaList" 
        :key="media.id"
        class="media-item"
        :class="{ selected: selectedMediaIds.includes(media.id) }"
        @click="toggleSelection(media.id)"
      >
        <div class="media-preview">
          <img v-if="media.type === 'image'" :src="media.thumbnail" :alt="media.name" />
          <video v-else-if="media.type === 'video'" :src="media.url" muted />
          <div v-else class="audio-icon">
            <i class="el-icon-headset"></i>
          </div>
        </div>
        <div class="media-info">
          <div class="media-name">{{ media.name }}</div>
          <div class="media-meta">{{ media.duration }}s | {{ media.size }}</div>
        </div>
        <div class="selection-indicator" v-if="selectedMediaIds.includes(media.id)">
          <i class="el-icon-check"></i>
        </div>
      </div>
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="confirmSelect" :disabled="selectedMediaIds.length === 0">
          确定选择 ({{ selectedMediaIds.length }})
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits } from 'vue';

interface MediaItem {
  id: string;
  name: string;
  type: 'video' | 'image' | 'audio';
  url: string;
  thumbnail: string;
  duration: number;
  size: string;
}

const props = defineProps<{
  visible: boolean;
  availableMedia?: MediaItem[];
}>();

const emit = defineEmits<{
  'update:visible': [value: boolean];
  'confirm': [selected: MediaItem[]];
}>();

const search = ref('');
const typeFilter = ref('');
const selectedMediaIds = ref<string[]>([]);

// 使用传入的素材或默认素材
const mediaList = computed(() => props.availableMedia || [
  {
    id: 'media1',
    name: '产品展示视频.mp4',
    type: 'video' as const,
    url: 'https://example.com/video1.mp4',
    thumbnail: 'https://example.com/thumb1.jpg',
    duration: 15,
    size: '2.5MB'
  },
  {
    id: 'media2',
    name: '产品图片.jpg',
    type: 'image' as const,
    url: 'https://example.com/image1.jpg',
    thumbnail: 'https://example.com/thumb2.jpg',
    duration: 0,
    size: '500KB'
  },
  {
    id: 'media3',
    name: '背景音乐.mp3',
    type: 'audio' as const,
    url: 'https://example.com/audio1.mp3',
    thumbnail: '',
    duration: 30,
    size: '1.2MB'
  }
]);

const filteredMediaList = computed(() => {
  let list = mediaList.value;
  
  if (search.value) {
    list = list.filter(item => 
      item.name.toLowerCase().includes(search.value.toLowerCase())
    );
  }
  
  if (typeFilter.value) {
    list = list.filter(item => item.type === typeFilter.value);
  }
  
  return list;
});

const toggleSelection = (mediaId: string) => {
  const index = selectedMediaIds.value.indexOf(mediaId);
  if (index > -1) {
    selectedMediaIds.value.splice(index, 1);
  } else {
    selectedMediaIds.value.push(mediaId);
  }
};

const handleClose = () => {
  selectedMediaIds.value = [];
  emit('update:visible', false);
};

const confirmSelect = () => {
  const selectedMedia = mediaList.value.filter(media => 
    selectedMediaIds.value.includes(media.id)
  );
  emit('confirm', selectedMedia);
  selectedMediaIds.value = [];
  emit('update:visible', false);
};
</script>

<style scoped>
.media-selector-toolbar {
  display: flex;
  gap: 10px;
  margin-bottom: 16px;
  align-items: center;
}

.search-input {
  flex: 1;
}

.type-select {
  width: 120px;
}

.media-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 16px;
  max-height: 400px;
  overflow-y: auto;
  padding: 8px;
}

.media-item {
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  background: white;
}

.media-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.media-item.selected {
  border-color: #409eff;
  background: rgba(64, 158, 255, 0.1);
}

.media-preview {
  width: 100%;
  height: 80px;
  border-radius: 4px;
  overflow: hidden;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.media-preview img,
.media-preview video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.audio-icon {
  color: #6c757d;
  font-size: 24px;
}

.media-info {
  text-align: center;
}

.media-name {
  font-size: 12px;
  color: #2c3e50;
  margin-bottom: 4px;
  word-break: break-all;
  line-height: 1.3;
}

.media-meta {
  font-size: 10px;
  color: #6c757d;
}

.selection-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 20px;
  height: 20px;
  background: #409eff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 