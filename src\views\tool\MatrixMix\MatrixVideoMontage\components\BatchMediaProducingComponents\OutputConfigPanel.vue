<template>
  <el-card class="output-config-panel">
    <h3 class="section-title">
      <i class="el-icon-s-operation"></i>
      输出配置
    </h3>
    <el-form :model="form" label-width="120px">
      <el-form-item label="生成数量">
        <el-input-number v-model="form.count" :min="1" :max="100" controls-position="right" />
        <span class="form-tip">个视频</span>
      </el-form-item>
      <el-form-item label="视频时长">
        <el-input-number v-model="form.maxDuration" :min="5" :max="300" controls-position="right" />
        <span class="form-tip">秒</span>
      </el-form-item>
      <el-form-item label="分辨率">
        <el-select v-model="form.resolution" placeholder="选择分辨率">
          <el-option label="1080x1920 (竖屏)" value="1080x1920" />
          <el-option label="1920x1080 (横屏)" value="1920x1080" />
          <el-option label="720x1280 (竖屏)" value="720x1280" />
          <el-option label="1280x720 (横屏)" value="1280x720" />
        </el-select>
      </el-form-item>
      <el-form-item label="视频质量(CRF)">
        <el-slider v-model="form.quality" :min="1" :max="51" :step="1" show-input input-size="small" />
        <div class="quality-tips">
          <span>高质量 (1-20)</span>
          <span>标准 (21-30)</span>
          <span>压缩 (31-51)</span>
        </div>
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script setup lang="ts">
import { computed, defineProps, defineEmits } from 'vue';

const props = defineProps<{
  modelValue: {
    count: number;
    maxDuration: number;
    resolution: string;
    quality: number;
  }
}>();
const emit = defineEmits(['update:modelValue', 'config-changed', 'api-config-changed']);

// 直接使用计算属性进行双向绑定，避免数据回弹
const form = computed({
  get: () => props.modelValue,
  set: (val) => {
    emit('update:modelValue', val);
    emit('config-changed', val);

    // 同时发送API格式的配置
    const apiConfig = buildApiOutputConfig(val);
    emit('api-config-changed', apiConfig);
  }
});

// 构建API所需的OutputConfig格式
const buildApiOutputConfig = (config: typeof props.modelValue) => {
  // 解析分辨率
  const [width, height] = config.resolution.split('x').map(Number);

  // 生成时间戳作为文件名前缀
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

  return {
    Count: config.count,
    MaxDuration: config.maxDuration,
    Width: width,
    Height: height,
    Video: {
      Crf: config.quality
    },
    GeneratePreviewOnly: false
  };
};
</script>

<style scoped>
.output-config-panel {
  margin-bottom: 20px;
}
.section-title {
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
}
.form-tip {
  margin-left: 10px;
  color: #6c757d;
  font-size: 12px;
}
.quality-tips {
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
  font-size: 12px;
  color: #6c757d;
}
</style> 