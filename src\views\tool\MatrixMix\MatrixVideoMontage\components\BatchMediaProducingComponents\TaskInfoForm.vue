<template>
  <el-card class="task-info-form">
    <h3 class="section-title">
      <i class="el-icon-document"></i>
      任务信息
    </h3>
    <el-form :model="form" label-width="100px">
      <el-form-item label="任务名称" required>
        <el-input v-model="form.taskName" placeholder="请输入任务名称，如：盒马鲜生开业宣传" clearable />
      </el-form-item>
      <el-form-item label="任务描述">
        <el-input v-model="form.taskDescription" type="textarea" :rows="2" placeholder="可选：描述任务用途" />
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script setup lang="ts">
import { computed, defineProps, defineEmits } from 'vue';

const props = defineProps<{
  modelValue: {
    taskName: string;
    taskDescription: string;
  }
}>();
const emit = defineEmits(['update:modelValue', 'task-name-changed', 'task-description-changed']);

// 直接使用计算属性进行双向绑定，避免数据回弹
const form = computed({
  get: () => props.modelValue,
  set: (val) => {
    emit('update:modelValue', val);
    emit('task-name-changed', val.taskName);
    emit('task-description-changed', val.taskDescription);
  }
});
</script>

<style scoped>
.task-info-form {
  margin-bottom: 20px;
}
.section-title {
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
}
</style> 