<template>
  <el-card class="template-selector">
    <h3 class="section-title">
      <i class="el-icon-magic-stick"></i>
      模板选择
    </h3>
    <div v-loading="loading" class="template-grid">
      <div 
        v-for="template in templates" 
        :key="template.id"
        class="template-card"
        :class="{ active: modelValue === template.id }"
        @click="selectTemplate(template.id)"
      >
        <div class="template-icon">
          <i :class="template.icon"></i>
        </div>
        <div class="template-info">
          <div class="template-name">{{ template.name }}</div>
          <div class="template-desc">{{ template.description }}</div>
        </div>
        <div class="template-check" v-if="modelValue === template.id">
          <i class="el-icon-check"></i>
        </div>
      </div>
      <div v-if="!loading && templates.length === 0" class="empty-state">
        <i class="el-icon-folder-opened"></i>
        <p>暂无可用模板</p>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';
import type { TemplateInfo } from '../../../types/batchProducing';

const props = defineProps<{
  modelValue: string;
  templates: TemplateInfo[];
  loading?: boolean;
}>();
const emit = defineEmits(['update:modelValue', 'template-changed']);

const selectTemplate = (id: string) => {
  emit('update:modelValue', id);
  emit('template-changed', id);
};
</script>

<style scoped>
.template-selector {
  margin-bottom: 20px;
}
.section-title {
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
}
.template-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 10px;
  min-height: 100px;
}
.template-card {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}
.template-card:hover {
  border-color: #409eff;
  background: rgba(64, 158, 255, 0.05);
}
.template-card.active {
  border-color: #409eff;
  background: rgba(64, 158, 255, 0.1);
}
.template-icon {
  width: 40px;
  height: 40px;
  background: #409eff;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}
.template-info {
  flex: 1;
}
.template-name {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px;
}
.template-desc {
  font-size: 12px;
  color: #6c757d;
}
.template-check {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 20px;
  height: 20px;
  background: #409eff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
}
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #909399;
  text-align: center;
}
.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}
.empty-state p {
  margin: 0;
  font-size: 14px;
}
</style> 