<template>
  <el-card class="text-config-panel">
    <h3 class="section-title">
      <i class="el-icon-edit"></i>
      文案配置
    </h3>
    <el-tabs v-model="activeTab" type="card">
      <el-tab-pane label="标题文案" name="titles">
        <div class="text-list">
          <div v-for="(title, index) in localTitles" :key="index" class="text-item">
            <el-input v-model="localTitles[index]" placeholder="请输入标题文案" type="textarea" :rows="2" @input="updateParent" />
            <el-button type="danger" size="mini" @click="removeTitle(index)" icon="el-icon-delete" v-if="localTitles.length > 1" />
          </div>
          <el-button type="dashed" @click="addTitle" class="add-text-btn">
            <i class="el-icon-plus"></i>
            添加标题
          </el-button>
        </div>
      </el-tab-pane>
      <el-tab-pane label="旁白文案" name="speech">
        <div class="text-list">
          <div v-for="(speech, index) in localSpeechTexts" :key="index" class="text-item">
            <el-input v-model="localSpeechTexts[index]" placeholder="请输入旁白文案" type="textarea" :rows="3" @input="updateParent" />
            <el-button type="danger" size="mini" @click="removeSpeech(index)" icon="el-icon-delete" v-if="localSpeechTexts.length > 1" />
          </div>
          <el-button type="dashed" @click="addSpeech" class="add-text-btn">
            <i class="el-icon-plus"></i>
            添加旁白
          </el-button>
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-card>
</template>

<script setup lang="ts">
import { ref, watch, defineProps, defineEmits } from 'vue';

const props = defineProps<{
  modelValue: {
    titles: string[];
    speechTexts: string[];
  }
}>();
const emit = defineEmits(['update:modelValue', 'titles-changed', 'speeches-changed']);

const activeTab = ref('titles');
const localTitles = ref<string[]>([]);
const localSpeechTexts = ref<string[]>([]);

// 初始化本地数据
const initLocalData = () => {
  localTitles.value = [...props.modelValue.titles];
  localSpeechTexts.value = [...props.modelValue.speechTexts];
};

// 初始化
initLocalData();

// 监听props变化，更新本地数据
watch(
  () => props.modelValue,
  (newVal) => {
    localTitles.value = [...newVal.titles];
    localSpeechTexts.value = [...newVal.speechTexts];
  }
);

// 更新父组件数据
const updateParent = () => {
  const data = {
    titles: [...localTitles.value],
    speechTexts: [...localSpeechTexts.value]
  };
  emit('update:modelValue', data);
  emit('titles-changed', data.titles);
  emit('speeches-changed', data.speechTexts);
};

const addTitle = () => {
  localTitles.value.push('');
  updateParent();
};

const removeTitle = (index: number) => {
  if (localTitles.value.length > 1) {
    localTitles.value.splice(index, 1);
    updateParent();
  }
};

const addSpeech = () => {
  localSpeechTexts.value.push('');
  updateParent();
};

const removeSpeech = (index: number) => {
  if (localSpeechTexts.value.length > 1) {
    localSpeechTexts.value.splice(index, 1);
    updateParent();
  }
};
</script>

<style scoped>
.text-config-panel {
  margin-bottom: 20px;
}
.section-title {
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
}
.text-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.text-item {
  display: flex;
  gap: 10px;
  align-items: flex-start;
}
.text-item .el-input {
  flex: 1;
}
.add-text-btn {
  width: 100%;
  height: 50px;
  border: 2px dashed #d1d5db;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  transition: all 0.3s ease;
}
.add-text-btn:hover {
  border-color: #409eff;
  color: #409eff;
}
</style> 