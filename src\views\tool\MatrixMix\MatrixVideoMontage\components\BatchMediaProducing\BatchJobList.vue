<template>
  <div class="batch-job-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">
          <i class="el-icon-video-camera"></i>
          批量成片任务列表
        </h2>
        <p class="page-subtitle">管理和监控批量智能一键成片任务</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="createNewJob" icon="el-icon-plus">
          新建任务
        </el-button>
        <el-button @click="refreshList" icon="el-icon-refresh" :loading="loading">
          刷新
        </el-button>
      </div>
    </div>

    <!-- 查询条件 -->
    <el-card class="query-card" shadow="never">
      <el-form :model="queryForm" :inline="true" label-width="80px">
        <el-form-item label="任务ID">
          <el-input 
            v-model="queryForm.jobId" 
            placeholder="请输入任务ID" 
            clearable 
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="任务类型">
          <el-select v-model="queryForm.jobType" placeholder="请选择任务类型" clearable style="width: 150px">
            <el-option label="脚本化" value="Script" />
            <el-option label="智能混剪" value="Smart_Mix" />
          </el-select>
        </el-form-item>
        <el-form-item label="任务状态">
          <el-select v-model="queryForm.status" placeholder="请选择状态" clearable style="width: 150px">
            <el-option label="已完成" value="Finished" />
            <el-option label="初始化" value="Init" />
            <el-option label="处理中" value="Processing" />
            <el-option label="失败" value="Failed" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DDTHH:mm:ssZ"
            style="width: 350px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery" icon="el-icon-search">
            查询
          </el-button>
          <el-button @click="resetQuery" icon="el-icon-refresh-left">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 任务列表 -->
    <el-card class="list-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>任务列表</span>
          <div class="header-actions">
            <el-select v-model="sortBy" placeholder="排序方式" style="width: 120px" @change="handleSortChange">
              <el-option label="最新优先" value="desc" />
              <el-option label="最早优先" value="asc" />
            </el-select>
            <span class="total-count">共 {{ totalCount }} 条记录</span>
          </div>
        </div>
      </template>

      <el-table
        :data="jobList"
        v-loading="loading"
        style="width: 100%"
        @row-click="handleRowClick"
        :row-class-name="getRowClassName"
        :default-sort="{ prop: 'createTime', order: 'descending' }"
      >
        <el-table-column label="任务名称" width="280">
          <template #default="{ row }">
            <div class="task-info-cell">
              <div class="task-name">
                <el-link type="primary" @click.stop="viewJobDetail(row)">
                  {{ row.taskName }}
                </el-link>
              </div>
              <div class="task-description">{{ row.taskDescription || '无描述' }}</div>
              <div class="task-stats">
                <el-tag size="small" type="info">{{ row.mediaCount }}个素材</el-tag>
                <el-tag size="small" type="success">{{ row.groupCount }}个组</el-tag>
                <el-tag size="small" type="warning">{{ row.outputCount }}个输出</el-tag>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="jobType" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getJobTypeTag(row.jobType)">
              {{ getJobTypeText(row.jobType) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="120">
          <template #default="{ row }">
            <div class="status-cell">
              <el-tag :type="getStatusTag(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
              <div v-if="row.status === 'Failed' && row.errorMessage" class="error-info">
                <el-tooltip :content="row.errorMessage" placement="top">
                  <i class="el-icon-warning" style="color: #f56c6c; margin-left: 5px;"></i>
                </el-tooltip>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="配置信息" width="150">
          <template #default="{ row }">
            <div class="config-info-cell">
              <div class="resolution">{{ row.resolution }}</div>
              <div class="counts">
                <span v-if="row.titleCount > 0">{{ row.titleCount }}个标题</span>
                <span v-if="row.speechCount > 0">{{ row.speechCount }}个旁白</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="createTime" label="创建时间" width="160" sortable>
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>

        <el-table-column prop="completeTime" label="完成时间" width="160">
          <template #default="{ row }">
            <span v-if="row.completeTime" class="complete-time">
              {{ formatDateTime(row.completeTime) }}
            </span>
            <span v-else class="no-complete-time">-</span>
          </template>
        </el-table-column>

        <el-table-column prop="progress" label="进度" width="120">
          <template #default="{ row }">
            <el-progress
              :percentage="row.progress"
              :status="getProgressStatus(row)"
              :stroke-width="8"
            />
          </template>
        </el-table-column>

        <el-table-column label="操作" width="100" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click.stop="viewJobDetail(row)" icon="el-icon-view">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalCount"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 任务详情弹窗 -->
    <el-dialog 
      v-model="detailDialogVisible" 
      title="任务详情" 
      width="800px"
      :close-on-click-modal="false"
    >
      <JobDetailDialog 
        v-if="detailDialogVisible"
        :job-id="selectedJobId"
        @close="detailDialogVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
import JobDetailDialog from './JobDetailDialog.vue';
import {
  listBatchMediaProducingJobs
} from '../../../api/batchJob';

// 类型定义
interface QueryForm {
  jobId: string;
  jobType: string;
  status: string;
  startTime: string;
  endTime: string;
}

interface ParsedBatchJob {
  jobId: string;
  jobType: string;
  status: string;
  createTime: string;
  updateTime: string;
  completeTime?: string;
  taskName: string;
  taskDescription: string;
  mediaCount: number;
  groupCount: number;
  titleCount: number;
  speechCount: number;
  outputCount: number;
  resolution: string;
  errorCode?: string;
  errorMessage?: string;
  configSnapshot: any;
  progress: number;
}

interface ListBatchMediaProducingJobsRequest {
  maxResults: number;
  sortBy: 'asc' | 'desc';
  jobId?: string;
  jobType?: 'Script' | 'Smart_Mix';
  status?: 'Finished' | 'Init' | 'Failed' | 'Processing';
  startTime?: string;
  endTime?: string;
  nextToken?: string;
}

// 响应式数据
const router = useRouter();
const loading = ref(false);
const jobList = ref<ParsedBatchJob[]>([]);
const totalCount = ref(0);
const currentPage = ref(1);
const pageSize = ref(20);
const sortBy = ref('desc');
const dateRange = ref<[string, string] | null>(null);
const nextToken = ref('');
const detailDialogVisible = ref(false);
const selectedJobId = ref('');

// 查询表单
const queryForm = reactive<QueryForm>({
  jobId: '',
  jobType: '',
  status: '',
  startTime: '',
  endTime: ''
});

// 计算属性

// 方法定义
const fetchJobList = async (isRefresh = false) => {
  loading.value = true;
  try {
    // 构建查询参数
    const params: ListBatchMediaProducingJobsRequest = {
      maxResults: pageSize.value,
      sortBy: sortBy.value as 'asc' | 'desc'
    };

    // 添加查询条件
    if (queryForm.jobId) params.jobId = queryForm.jobId;
    if (queryForm.jobType) params.jobType = queryForm.jobType as 'Script' | 'Smart_Mix';
    if (queryForm.status) params.status = queryForm.status as 'Finished' | 'Init' | 'Failed' | 'Processing';
    if (dateRange.value) {
      params.startTime = dateRange.value[0];
      params.endTime = dateRange.value[1];
    }

    // 如果不是刷新，且有关键字，则使用分页token
    if (!isRefresh && nextToken.value) {
      params.nextToken = nextToken.value;
    }

    // 调用API
    const response = await listBatchMediaProducingJobs(
      params.startTime,
      params.endTime,
      params.status,
      20,
      params.nextToken
    );

    console.log('📥 API响应数据:', response);

    // 解析API返回的数据
    const parsedJobs = parseJobListData(response);

    if (isRefresh) {
      jobList.value = parsedJobs;
    } else {
      jobList.value = [...jobList.value, ...parsedJobs];
    }

    totalCount.value = parsedJobs.length;
    nextToken.value = response.NextToken || '';

    console.log('✅ 任务列表获取成功:', jobList.value.length, '条记录');
  } catch (error) {
    console.error('❌ 获取任务列表失败:', error);
    ElMessage.error('获取任务列表时发生网络错误');
    totalCount.value = jobList.value.length;
  } finally {
    loading.value = false;
  }
};

// 解析API返回的任务列表数据
const parseJobListData = (response: any) => {
  // 支持直接传入EditingBatchJobList数组或包含data的响应对象
  const jobList = response.EditingBatchJobList || response.data?.EditingBatchJobList || response;
  if (!jobList || !Array.isArray(jobList)) return [];

  return jobList.map((job: any) => {
    // 解析UserData JSON
    let userData: any = {};
    try {
      userData = JSON.parse(job.UserData || '{}');
    } catch (e) {
      console.warn('解析UserData失败:', job.UserData);
    }

    // 解析OutputConfig JSON
    let outputConfig: any = {};
    try {
      outputConfig = JSON.parse(job.OutputConfig || '{}');
    } catch (e) {
      console.warn('解析OutputConfig失败:', job.OutputConfig);
    }

    // 解析Extend JSON
    let extend: any = {};
    try {
      extend = JSON.parse(job.Extend || '{}');
    } catch (e) {
      console.warn('解析Extend失败:', job.Extend);
    }

    return {
      jobId: job.JobId,
      jobType: job.JobType,
      status: job.Status,
      createTime: job.CreateTime,
      updateTime: job.ModifiedTime,
      completeTime: job.CompleteTime,
      taskName: userData.TaskName || '未命名任务',
      taskDescription: userData.TaskDescription || '',
      mediaCount: userData.MediaCount || 0,
      groupCount: userData.GroupCount || 0,
      titleCount: userData.TitleCount || 0,
      speechCount: userData.SpeechCount || 0,
      outputCount: outputConfig.count || 1,
      resolution: `${outputConfig.width || 1080}x${outputConfig.height || 1920}`,
      errorCode: extend.ErrorCode,
      errorMessage: extend.ErrorMessage,
      configSnapshot: userData.ConfigSnapshot || {},
      progress: getJobProgress(job.Status)
    };
  });
};

// 根据状态计算进度
const getJobProgress = (status: string) => {
  switch (status) {
    case 'Finished': return 100;
    case 'Failed': return 0;
    case 'Init': return 10;
    case 'Processing': return 50;
    default: return 0;
  }
};

const handleQuery = () => {
  currentPage.value = 1;
  nextToken.value = '';
  fetchJobList(true);
};

const resetQuery = () => {
  Object.assign(queryForm, {
    jobId: '',
    jobType: '',
    status: '',
    startTime: '',
    endTime: ''
  });
  dateRange.value = null;
  currentPage.value = 1;
  nextToken.value = '';
  fetchJobList(true);
};

const refreshList = () => {
  fetchJobList(true);
};

const handleSortChange = () => {
  fetchJobList(true);
};

const handleSizeChange = (size: number) => {
  pageSize.value = size;
  fetchJobList(true);
};

const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  // 这里可以根据需要实现分页逻辑
};

const createNewJob = () => {
  router.push('/tool/matrix-mix/batch-producing');
};

const viewJobDetail = (job: ParsedBatchJob) => {
  selectedJobId.value = job.jobId;
  detailDialogVisible.value = true;
};



const handleRowClick = (row: ParsedBatchJob) => {
  viewJobDetail(row);
};

// 工具方法
const getJobTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    'Script': '脚本化',
    'Smart_Mix': '智能混剪'
  };
  return typeMap[type] || type;
};

const getJobTypeTag = (type: string) => {
  const tagMap: Record<string, string> = {
    'Script': 'primary',
    'Smart_Mix': 'success'
  };
  return tagMap[type] || 'info';
};

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'Finished': '已完成',
    'Init': '初始化',
    'Processing': '处理中',
    'Failed': '失败'
  };
  return statusMap[status] || status;
};

const getStatusTag = (status: string) => {
  const tagMap: Record<string, string> = {
    'Finished': 'success',
    'Init': 'info',
    'Processing': 'warning',
    'Failed': 'danger'
  };
  return tagMap[status] || 'info';
};

const getProgressStatus = (job: ParsedBatchJob) => {
  if (job.status === 'Failed') return 'exception';
  if (job.status === 'Finished') return 'success';
  return undefined;
};

const getRowClassName = ({ row }: { row: ParsedBatchJob }) => {
  if (row.status === 'Failed') return 'failed-row';
  if (row.status === 'Processing') return 'processing-row';
  return '';
};

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  try {
    return new Date(dateTime).toLocaleString('zh-CN');
  } catch {
    return dateTime;
  }
};




// 生命周期
onMounted(() => {
  fetchJobList(true);
});
</script>

<style scoped>
.batch-job-list {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left .page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-left .page-subtitle {
  margin: 0;
  color: #7f8c8d;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 10px;
}

.query-card {
  margin-bottom: 20px;
}

.list-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.total-count {
  color: #7f8c8d;
  font-size: 14px;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 表格单元格样式 */
.job-id-cell {
  display: flex;
  align-items: center;
}

.task-info-cell {
  padding: 8px 0;
}

.task-name {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
  font-size: 14px;
}

.task-description {
  color: #7f8c8d;
  font-size: 12px;
  margin-bottom: 6px;
  line-height: 1.4;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.task-stats {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.task-stats .el-tag {
  font-size: 11px;
  height: 20px;
  line-height: 18px;
}

.status-cell {
  display: flex;
  align-items: center;
}

.error-info {
  margin-left: 5px;
}

.config-info-cell {
  font-size: 12px;
}

.resolution {
  font-weight: 600;
  color: #409eff;
  margin-bottom: 4px;
}

.counts {
  color: #7f8c8d;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.time-cell {
  font-size: 12px;
}

.complete-time {
  color: #67c23a;
}

.no-complete-time {
  color: #c0c4cc;
}

/* 表格行样式 */
:deep(.failed-row) {
  background-color: #fef0f0;
}

:deep(.processing-row) {
  background-color: #f0f9ff;
}

:deep(.el-table__row:hover) {
  cursor: pointer;
}

/* 表格头部样式 */
:deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

/* 进度条样式 */
:deep(.el-progress-bar__outer) {
  border-radius: 4px;
}

:deep(.el-progress-bar__inner) {
  border-radius: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 15px;
  }
  
  .header-right {
    width: 100%;
    justify-content: flex-end;
  }
  
  .el-form--inline .el-form-item {
    margin-right: 10px;
    margin-bottom: 10px;
  }
}
</style> 