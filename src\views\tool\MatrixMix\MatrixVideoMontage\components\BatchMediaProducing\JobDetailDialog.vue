<template>
  <div class="job-detail-dialog">
    <div v-loading="loading" class="detail-content">
      <!-- 基本信息 -->
      <el-descriptions title="基本信息" :column="2" border>
        <el-descriptions-item label="任务ID">{{ jobDetail.jobId }}</el-descriptions-item>
        <el-descriptions-item label="任务类型">
          <el-tag :type="getJobTypeTag(jobDetail.jobType)">
            {{ getJobTypeText(jobDetail.jobType) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="任务状态">
          <el-tag :type="getStatusTag(jobDetail.status)">
            {{ getStatusText(jobDetail.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatDateTime(jobDetail.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="开始时间">{{ formatDateTime(jobDetail.startTime) }}</el-descriptions-item>
        <el-descriptions-item label="完成时间">{{ formatDateTime(jobDetail.finishTime) }}</el-descriptions-item>
      </el-descriptions>

      <!-- 进度信息 -->
      <div class="progress-section">
        <h4>处理进度</h4>
        <el-progress 
          :percentage="jobDetail.progress || 0" 
          :status="getProgressStatus(jobDetail.status)"
          :stroke-width="12"
        />
        <div class="progress-info">
          <span>已处理: {{ jobDetail.processedCount || 0 }} / {{ jobDetail.totalCount || 0 }}</span>
          <span v-if="jobDetail.estimatedTime">预计剩余时间: {{ jobDetail.estimatedTime }}</span>
        </div>
      </div>

      <!-- 配置信息 -->
      <el-descriptions title="配置信息" :column="2" border>
        <el-descriptions-item label="生成数量">{{ jobDetail.outputCount }}</el-descriptions-item>
        <el-descriptions-item label="最大时长">{{ jobDetail.maxDuration }}秒</el-descriptions-item>
        <el-descriptions-item label="分辨率">{{ jobDetail.resolution }}</el-descriptions-item>
        <el-descriptions-item label="视频质量">CRF {{ jobDetail.quality }}</el-descriptions-item>
        <el-descriptions-item label="媒体组数量">{{ jobDetail.mediaGroupCount }}</el-descriptions-item>
        <el-descriptions-item label="文案数量">{{ jobDetail.textCount }}</el-descriptions-item>
      </el-descriptions>

      <!-- 错误信息 -->
      <div v-if="jobDetail.errorMessage" class="error-section">
        <h4>错误信息</h4>
        <el-alert 
          :title="jobDetail.errorMessage" 
          type="error" 
          :closable="false"
          show-icon
        />
      </div>

      <!-- 输出结果 -->
      <div v-if="jobDetail.status === 'Finished' && jobDetail.outputFiles" class="output-section">
        <h4>输出结果</h4>
        <el-table :data="jobDetail.outputFiles" style="width: 100%">
          <el-table-column prop="index" label="序号" width="80" />
          <el-table-column prop="fileName" label="文件名" />
          <el-table-column prop="fileSize" label="文件大小" width="120" />
          <el-table-column prop="duration" label="时长" width="100" />
          <el-table-column label="操作" width="150">
            <template #default="{ row }">
              <el-button size="small" @click="downloadFile(row)" icon="el-icon-download">
                下载
              </el-button>
              <el-button size="small" @click="previewFile(row)" icon="el-icon-view">
                预览
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 操作按钮 -->
      <div class="dialog-footer">
        <el-button @click="$emit('close')">关闭</el-button>
        <el-button 
          v-if="jobDetail.status === 'Failed'" 
          type="primary" 
          @click="retryJob"
          :loading="retryLoading"
        >
          重试任务
        </el-button>
        <el-button 
          v-if="jobDetail.status === 'Processing'" 
          type="warning" 
          @click="cancelJob"
          :loading="cancelLoading"
        >
          取消任务
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';

// 类型定义
interface JobDetail {
  jobId: string;
  jobType: 'Script' | 'Smart_Mix';
  status: 'Finished' | 'Init' | 'Failed' | 'Processing';
  createTime: string;
  startTime: string;
  finishTime: string;
  progress: number;
  processedCount: number;
  totalCount: number;
  estimatedTime: string;
  outputCount: number;
  maxDuration: number;
  resolution: string;
  quality: number;
  mediaGroupCount: number;
  textCount: number;
  errorMessage?: string;
  outputFiles?: Array<{
    index: number;
    fileName: string;
    fileSize: string;
    duration: number;
    url: string;
  }>;
}

// Props
const props = defineProps<{
  jobId: string;
}>();

// Emits
const emit = defineEmits<{
  (e: 'close'): void;
}>();

// 响应式数据
const loading = ref(false);
const retryLoading = ref(false);
const cancelLoading = ref(false);
const jobDetail = ref<JobDetail>({} as JobDetail);

// 方法定义
const fetchJobDetail = async () => {
  loading.value = true;
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 模拟数据
    jobDetail.value = getMockJobDetail(props.jobId);
    
    console.log('✅ 任务详情获取成功:', jobDetail.value);
  } catch (error) {
    console.error('❌ 获取任务详情失败:', error);
    ElMessage.error('获取任务详情失败');
  } finally {
    loading.value = false;
  }
};

const retryJob = async () => {
  retryLoading.value = true;
  try {
    // 模拟重试API调用
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    ElMessage.success('任务重试已提交');
    emit('close');
  } catch (error) {
    ElMessage.error('重试任务失败');
  } finally {
    retryLoading.value = false;
  }
};

const cancelJob = async () => {
  try {
    // 模拟取消API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    ElMessage.success('任务已取消');
    emit('close');
  } catch (error) {
    ElMessage.error('取消任务失败');
  } finally {
    cancelLoading.value = false;
  }
};

const downloadFile = (file: any) => {
  // 模拟下载
  const link = document.createElement('a');
  link.href = file.url;
  link.download = file.fileName;
  link.click();
  ElMessage.success('开始下载文件');
};

const previewFile = (file: any) => {
  // 模拟预览
  window.open(file.url, '_blank');
};

// 工具方法
const getJobTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    'Script': '脚本化',
    'Smart_Mix': '智能混剪'
  };
  return typeMap[type] || type;
};

const getJobTypeTag = (type: string) => {
  const tagMap: Record<string, string> = {
    'Script': 'primary',
    'Smart_Mix': 'success'
  };
  return tagMap[type] || 'info';
};

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'Finished': '已完成',
    'Init': '初始化',
    'Processing': '处理中',
    'Failed': '失败'
  };
  return statusMap[status] || status;
};

const getStatusTag = (status: string) => {
  const tagMap: Record<string, string> = {
    'Finished': 'success',
    'Init': 'info',
    'Processing': 'warning',
    'Failed': 'danger'
  };
  return tagMap[status] || 'info';
};

const getProgressStatus = (status: string) => {
  if (status === 'Failed') return 'exception';
  if (status === 'Finished') return 'success';
  return undefined;
};

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  try {
    return new Date(dateTime).toLocaleString('zh-CN');
  } catch {
    return dateTime;
  }
};

// 模拟数据
const getMockJobDetail = (jobId: string): JobDetail => {
  const mockData: Record<string, JobDetail> = {
    'job_20250112_001': {
      jobId: 'job_20250112_001',
      jobType: 'Script',
      status: 'Finished',
      createTime: '2025-01-12T10:00:00Z',
      startTime: '2025-01-12T10:01:00Z',
      finishTime: '2025-01-12T10:15:00Z',
      progress: 100,
      processedCount: 10,
      totalCount: 10,
      estimatedTime: '0分钟',
      outputCount: 10,
      maxDuration: 15,
      resolution: '1080x1920',
      quality: 23,
      mediaGroupCount: 2,
      textCount: 5,
      outputFiles: [
        { index: 1, fileName: 'output_001.mp4', fileSize: '15.2MB', duration: 12, url: '#' },
        { index: 2, fileName: 'output_002.mp4', fileSize: '14.8MB', duration: 13, url: '#' },
        { index: 3, fileName: 'output_003.mp4', fileSize: '16.1MB', duration: 11, url: '#' }
      ]
    },
    'job_20250112_002': {
      jobId: 'job_20250112_002',
      jobType: 'Smart_Mix',
      status: 'Processing',
      createTime: '2025-01-12T11:00:00Z',
      startTime: '2025-01-12T11:01:00Z',
      finishTime: '',
      progress: 65,
      processedCount: 7,
      totalCount: 10,
      estimatedTime: '3分钟',
      outputCount: 10,
      maxDuration: 20,
      resolution: '1920x1080',
      quality: 25,
      mediaGroupCount: 3,
      textCount: 8
    },
    'job_20250112_003': {
      jobId: 'job_20250112_003',
      jobType: 'Script',
      status: 'Failed',
      createTime: '2025-01-12T12:00:00Z',
      startTime: '2025-01-12T12:01:00Z',
      finishTime: '2025-01-12T12:10:00Z',
      progress: 0,
      processedCount: 0,
      totalCount: 10,
      estimatedTime: '0分钟',
      outputCount: 10,
      maxDuration: 15,
      resolution: '1080x1920',
      quality: 23,
      mediaGroupCount: 1,
      textCount: 3,
      errorMessage: '媒体文件不存在或已被删除'
    }
  };
  
  return mockData[jobId] || {

  };
};

// 生命周期
onMounted(() => {
  fetchJobDetail();
});
</script>

<style scoped>
.job-detail-dialog {
  padding: 20px;
}

.detail-content {
  max-height: 600px;
  overflow-y: auto;
}

.progress-section,
.error-section,
.output-section {
  margin: 20px 0;
}

.progress-section h4,
.error-section h4,
.output-section h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  color: #7f8c8d;
  font-size: 14px;
}

.dialog-footer {
  margin-top: 30px;
  text-align: right;
  border-top: 1px solid #e4e7ed;
  padding-top: 20px;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .progress-info {
    flex-direction: column;
    gap: 5px;
  }
  
  .dialog-footer {
    text-align: center;
  }
  
  .dialog-footer .el-button {
    margin: 5px;
  }
}
</style> 