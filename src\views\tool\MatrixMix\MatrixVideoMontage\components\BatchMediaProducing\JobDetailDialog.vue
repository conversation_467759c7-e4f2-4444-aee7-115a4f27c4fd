<template>
  <el-dialog
    v-model="dialogVisible"
    title="任务详情"
    width="80%"
    :before-close="handleClose"
  >
    <div v-loading="loading" class="job-detail-dialog">
      <div v-if="jobDetail" class="detail-content">
        <!-- 基本信息 -->
        <el-descriptions title="基本信息" :column="2" border>
          <el-descriptions-item label="任务ID">{{ jobDetail.JobId }}</el-descriptions-item>
          <el-descriptions-item label="任务类型">
            <el-tag :type="getJobTypeTag(jobDetail.JobType)">
              {{ getJobTypeText(jobDetail.JobType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="任务状态">
            <el-tag :type="getStatusTag(jobDetail.Status)">
              {{ getStatusText(jobDetail.Status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(jobDetail.CreateTime) }}</el-descriptions-item>
          <el-descriptions-item label="修改时间">{{ formatDateTime(jobDetail.ModifiedTime) }}</el-descriptions-item>
          <el-descriptions-item label="完成时间">{{ formatDateTime(jobDetail.CompleteTime) }}</el-descriptions-item>
        </el-descriptions>

        <!-- 错误信息 -->
        <div v-if="jobDetail.Status === 'Failed' && jobDetail.Extend?.ErrorMessage" class="error-section">
          <h4>错误信息</h4>
          <el-alert 
            :title="jobDetail.Extend.ErrorMessage" 
            type="error" 
            show-icon 
            :closable="false"
          />
        </div>

        <!-- 配置状态监控 -->
        <div class="config-monitor-section">
          <h4>配置状态监控</h4>
          <div class="monitor-grid">
            <div class="monitor-item">
              <div class="monitor-label">视频音量:</div>
              <div class="monitor-value">{{ parsedOutputConfig.videoQuality || '50%' }}</div>
            </div>
            <div class="monitor-item">
              <div class="monitor-label">音频音量:</div>
              <div class="monitor-value">{{ parsedEditingConfig.audioVolume || '50' }}%</div>
            </div>
            <div class="monitor-item">
              <div class="monitor-label">背景音乐:</div>
              <div class="monitor-value">{{ parsedEditingConfig.backgroundMusic || '默认' }}</div>
            </div>
            <div class="monitor-item">
              <div class="monitor-label">生成数量:</div>
              <div class="monitor-value">{{ parsedOutputConfig.count || '10' }}个</div>
            </div>
            <div class="monitor-item">
              <div class="monitor-label">视频时长:</div>
              <div class="monitor-value">{{ parsedOutputConfig.maxDuration || '15' }}秒</div>
            </div>
            <div class="monitor-item">
              <div class="monitor-label">分辨率:</div>
              <div class="monitor-value">{{ parsedOutputConfig.resolution || '1080x1920' }}</div>
            </div>
            <div class="monitor-item">
              <div class="monitor-label">视频质量:</div>
              <div class="monitor-value">CRF {{ parsedOutputConfig.crf || '23' }}</div>
            </div>
          </div>
        </div>

        <!-- 模板信息 -->
        <div v-if="parsedUserData.templateName" class="template-section">
          <h4>选择模板</h4>
          <div class="template-info">
            <div class="template-name">{{ parsedUserData.templateName }}</div>
            <div class="template-description">{{ parsedUserData.templateDescription || '暂无描述' }}</div>
          </div>
        </div>

        <!-- 详细配置信息 -->
        <div class="config-section">
          <h4>详细配置信息</h4>
          <el-tabs>
            <el-tab-pane label="输入配置" name="input">
              <div class="config-parsed">
                <!-- 媒体组配置 - 只有配置了才显示 -->
                <div v-if="parsedInputConfig.MediaGroupArray && parsedInputConfig.MediaGroupArray.length > 0" class="config-group">
                  <h5>媒体组配置 ({{ parsedInputConfig.MediaGroupArray.length }}组)</h5>
                  <div v-loading="mediaLoading" element-loading-text="加载媒资信息中...">
                    <div v-for="(group, index) in parsedInputConfig.MediaGroupArray" :key="index" class="media-group">
                      <div class="group-header">
                        <span class="group-name">{{ group.GroupName || `第${index + 1}组` }}</span>
                        <span class="group-count">({{ group.MediaArray?.length || 0 }}个文件)</span>
                      </div>
                      <div v-if="group.MediaArray && group.MediaArray.length > 0" class="media-list">
                        <div v-for="(mediaId, mIndex) in group.MediaArray" :key="mIndex" class="media-item-preview">
                          <div class="media-preview">
                            <template v-if="getMediaInfo(mediaId)">
                              <img
                                v-if="getMediaInfo(mediaId).FileBasicInfo?.CoverURL"
                                :src="getMediaInfo(mediaId).FileBasicInfo.CoverURL"
                                :alt="getMediaInfo(mediaId).FileBasicInfo?.Title || '媒资预览'"
                                class="media-thumbnail"
                              />
                              <div v-else class="media-placeholder">
                                <i class="el-icon-picture-outline"></i>
                              </div>
                              <div class="media-info">
                                <div class="media-title">{{ getMediaInfo(mediaId).FileBasicInfo?.Title || '未命名' }}</div>
                                <div class="media-type">{{ getMediaInfo(mediaId).FileBasicInfo?.MediaType || '未知类型' }}</div>
                              </div>
                            </template>
                            <template v-else>
                              <div class="media-placeholder">
                                <i class="el-icon-loading"></i>
                              </div>
                              <div class="media-info">
                                <div class="media-title">{{ mediaId }}</div>
                                <div class="media-type">加载中...</div>
                              </div>
                            </template>
                          </div>
                        </div>
                      </div>
                      <!-- 分组配置信息 -->
                      <div v-if="group.SpeechTextArray || group.Duration || group.SplitMode || group.Volume" class="group-config">
                        <el-descriptions :column="2" size="small" border>
                          <el-descriptions-item v-if="group.SpeechTextArray" label="口播文案">{{ group.SpeechTextArray.length }}条</el-descriptions-item>
                          <el-descriptions-item v-if="group.Duration" label="时长">{{ group.Duration }}秒</el-descriptions-item>
                          <el-descriptions-item v-if="group.SplitMode" label="拆条模式">{{ group.SplitMode === 'NoSplit' ? '不拆条' : '自动拆条' }}</el-descriptions-item>
                          <el-descriptions-item v-if="group.Volume" label="音量">{{ group.Volume }}</el-descriptions-item>
                        </el-descriptions>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 标题配置 - 只有配置了才显示 -->
                <div v-if="parsedInputConfig.TitleArray && parsedInputConfig.TitleArray.length > 0" class="config-group">
                  <h5>标题配置 ({{ parsedInputConfig.TitleArray.length }}个)</h5>
                  <div class="title-list">
                    <div v-for="(title, index) in parsedInputConfig.TitleArray" :key="index" class="title-item">
                      <el-tag>{{ title }}</el-tag>
                    </div>
                  </div>
                </div>

                <!-- 副标题配置 -->
                <div v-if="parsedInputConfig.SubHeadingArray && parsedInputConfig.SubHeadingArray.length > 0" class="config-group">
                  <h5>副标题配置</h5>
                  <div v-for="(subHeading, index) in parsedInputConfig.SubHeadingArray" :key="index" class="sub-heading-group">
                    <div class="sub-heading-level">{{ subHeading.Level }}级副标题 ({{ subHeading.TitleArray?.length || 0 }}个)</div>
                    <div class="title-list">
                      <div v-for="(title, tIndex) in subHeading.TitleArray" :key="tIndex" class="title-item">
                        <el-tag type="info">{{ title }}</el-tag>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 语音文本配置 - 只有配置了才显示 -->
                <div v-if="parsedInputConfig.SpeechTextArray && parsedInputConfig.SpeechTextArray.length > 0" class="config-group">
                  <h5>语音文本配置 ({{ parsedInputConfig.SpeechTextArray.length }}个)</h5>
                  <div class="speech-list">
                    <div v-for="(speech, index) in parsedInputConfig.SpeechTextArray" :key="index" class="speech-item">
                      <div class="speech-index">文案{{ index + 1 }}</div>
                      <div class="speech-content">{{ speech }}</div>
                    </div>
                  </div>
                </div>

                <!-- 贴纸配置 -->
                <div v-if="parsedInputConfig.StickerArray && parsedInputConfig.StickerArray.length > 0" class="config-group">
                  <h5>贴纸配置 ({{ parsedInputConfig.StickerArray.length }}个)</h5>
                  <div class="sticker-list">
                    <div v-for="(sticker, index) in parsedInputConfig.StickerArray" :key="index" class="sticker-item">
                      <el-descriptions :column="2" size="small" border>
                        <el-descriptions-item label="位置">X:{{ sticker.X }}, Y:{{ sticker.Y }}</el-descriptions-item>
                        <el-descriptions-item label="尺寸">{{ sticker.Width }}×{{ sticker.Height }}</el-descriptions-item>
                        <el-descriptions-item v-if="sticker.Opacity" label="透明度">{{ sticker.Opacity }}</el-descriptions-item>
                      </el-descriptions>
                    </div>
                  </div>
                </div>

                <!-- 背景音乐配置 -->
                <div v-if="parsedInputConfig.BackgroundMusicArray && parsedInputConfig.BackgroundMusicArray.length > 0" class="config-group">
                  <h5>背景音乐配置 ({{ parsedInputConfig.BackgroundMusicArray.length }}个)</h5>
                  <div class="background-music-list">
                    <div v-for="(music, index) in parsedInputConfig.BackgroundMusicArray" :key="index" class="music-item">
                      <el-tag type="success">音乐{{ index + 1 }}</el-tag>
                    </div>
                  </div>
                </div>

                <!-- 背景图片配置 -->
                <div v-if="parsedInputConfig.BackgroundImageArray && parsedInputConfig.BackgroundImageArray.length > 0" class="config-group">
                  <h5>背景图片配置 ({{ parsedInputConfig.BackgroundImageArray.length }}个)</h5>
                  <div class="background-image-list">
                    <div v-for="(image, index) in parsedInputConfig.BackgroundImageArray" :key="index" class="image-item">
                      <el-tag type="warning">图片{{ index + 1 }}</el-tag>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="剪辑配置" name="editing">
              <div class="config-parsed">
                <!-- 音频配置 -->
                <div class="config-group">
                  <h5>音频配置</h5>
                  <el-descriptions :column="2" border>
                    <el-descriptions-item label="音频音量">{{ parsedEditingConfig.audioVolume }}%</el-descriptions-item>
                    <el-descriptions-item label="背景音乐">{{ parsedEditingConfig.backgroundMusic }}</el-descriptions-item>
                    <el-descriptions-item label="语音合成">{{ parsedEditingConfig.speechSynthesis }}</el-descriptions-item>
                  </el-descriptions>
                </div>
                <!-- 处理配置 -->
                <div class="config-group">
                  <h5>处理配置</h5>
                  <el-descriptions :column="2" border>
                    <el-descriptions-item label="智能裁剪">{{ parsedEditingConfig.smartCrop }}</el-descriptions-item>
                    <el-descriptions-item label="自动调色">{{ parsedEditingConfig.autoColor }}</el-descriptions-item>
                    <el-descriptions-item label="转场效果">{{ parsedEditingConfig.transition }}</el-descriptions-item>
                  </el-descriptions>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="输出配置" name="output">
              <div class="config-parsed">
                <div class="config-group">
                  <h5>输出参数</h5>
                  <el-descriptions :column="2" border>
                    <el-descriptions-item label="输出数量">{{ parsedOutputConfig.count }}个</el-descriptions-item>
                    <el-descriptions-item label="最大时长">{{ parsedOutputConfig.maxDuration }}秒</el-descriptions-item>
                    <el-descriptions-item label="视频宽度">{{ parsedOutputConfig.width }}px</el-descriptions-item>
                    <el-descriptions-item label="视频高度">{{ parsedOutputConfig.height }}px</el-descriptions-item>
                    <el-descriptions-item label="宽高比">{{ parsedOutputConfig.widthHeightRatio }}</el-descriptions-item>
                    <el-descriptions-item label="视频质量">CRF {{ parsedOutputConfig.crf }}</el-descriptions-item>
                  </el-descriptions>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="用户数据" name="userData" v-if="jobDetail.UserData">
              <div class="config-parsed">
                <div class="config-group">
                  <h5>任务信息</h5>
                  <el-descriptions :column="2" border>
                    <el-descriptions-item label="任务名称">{{ parsedUserData.taskName || '未命名' }}</el-descriptions-item>
                    <el-descriptions-item label="任务描述">{{ parsedUserData.taskDescription || '无描述' }}</el-descriptions-item>
                    <el-descriptions-item label="创建用户">{{ parsedUserData.username || '未知' }}</el-descriptions-item>
                    <el-descriptions-item label="用户角色">{{ parsedUserData.userRole || '未知' }}</el-descriptions-item>
                    <el-descriptions-item label="用户部门">{{ parsedUserData.userDept || '未知' }}</el-descriptions-item>
                  </el-descriptions>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>

        <!-- 输出文件列表 -->
        <div v-if="jobDetail.SubJobList && jobDetail.SubJobList.length > 0" class="output-files-section">
          <h4>输出文件 ({{ jobDetail.SubJobList.length }})</h4>
          <el-table :data="jobDetail.SubJobList" border>
            <el-table-column prop="JobId" label="子任务ID" width="200" />
            <el-table-column prop="MediaId" label="媒资ID" width="200" />
            <el-table-column prop="Status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getSubJobStatusTag(row.Status)">
                  {{ getSubJobStatusText(row.Status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="Duration" label="时长" width="80">
              <template #default="{ row }">
                {{ row.Duration ? `${row.Duration}s` : '-' }}
              </template>
            </el-table-column>
            <!-- 文件地址已移除，不对用户显示 -->
            <el-table-column label="操作" width="100">
              <template #default="{ row }">
                <el-button
                  v-if="row.MediaURL && row.Status === 'Success'"
                  size="small"
                  type="primary"
                  @click="downloadFile(row.MediaURL)"
                >
                  下载
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <div v-else class="no-data">
        <el-empty description="暂无数据" />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="refreshDetail">刷新</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { getBatchMediaProducingJob } from '../../../api/batchProducing';
import type { BatchMediaProducingJobDetail } from '../../../types/batchProducing';
import { batchGetMediaInfosSimple } from '../../../api/media';

// Props
const props = defineProps<{
  visible: boolean;
  jobId: string;
}>();

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
}>();

// 响应式数据
const loading = ref(false);
const jobDetail = ref<BatchMediaProducingJobDetail | null>(null);
const mediaInfos = ref<any[]>([]);
const mediaLoading = ref(false);

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
});

// 解析输入配置 - 只返回实际配置的字段
const parsedInputConfig = computed(() => {
  if (!jobDetail.value?.InputConfig) return {};
  try {
    const config = JSON.parse(jobDetail.value.InputConfig);
    console.log('🔍 原始InputConfig:', config);

    // 只返回实际存在且有内容的字段
    const result: any = {};

    if (config.MediaGroupArray && config.MediaGroupArray.length > 0) {
      result.MediaGroupArray = config.MediaGroupArray;
    }
    if (config.TitleArray && config.TitleArray.length > 0) {
      result.TitleArray = config.TitleArray;
    }
    if (config.SubHeadingArray && config.SubHeadingArray.length > 0) {
      result.SubHeadingArray = config.SubHeadingArray;
    }
    if (config.SpeechTextArray && config.SpeechTextArray.length > 0) {
      result.SpeechTextArray = config.SpeechTextArray;
    }
    if (config.StickerArray && config.StickerArray.length > 0) {
      result.StickerArray = config.StickerArray;
    }
    if (config.BackgroundMusicArray && config.BackgroundMusicArray.length > 0) {
      result.BackgroundMusicArray = config.BackgroundMusicArray;
    }
    if (config.BackgroundImageArray && config.BackgroundImageArray.length > 0) {
      result.BackgroundImageArray = config.BackgroundImageArray;
    }

    console.log('✅ 解析后的InputConfig:', result);
    return result;
  } catch (error) {
    console.error('❌ 解析输入配置失败:', error);
    return {};
  }
});

// 解析媒资ID并获取预览信息
const parsedMediaIds = computed(() => {
  const mediaIds: string[] = [];
  if (parsedInputConfig.value.MediaGroupArray) {
    parsedInputConfig.value.MediaGroupArray.forEach((group: any) => {
      if (group.MediaArray && Array.isArray(group.MediaArray)) {
        group.MediaArray.forEach((id: string) => {
          // 只处理媒资ID，跳过OSS URL
          if (id && !id.startsWith('http') && !mediaIds.includes(id)) {
            mediaIds.push(id);
          }
        });
      }
    });
  }
  return mediaIds;
});

// 获取媒资信息
const fetchMediaInfos = async () => {
  if (parsedMediaIds.value.length === 0) {
    mediaInfos.value = [];
    return;
  }

  try {
    mediaLoading.value = true;
    const response = await batchGetMediaInfosSimple(parsedMediaIds.value, ['FileBasicInfo']);
    mediaInfos.value = response.MediaInfos || [];
  } catch (error) {
    console.error('获取媒资信息失败:', error);
    ElMessage.error('获取媒资信息失败');
    mediaInfos.value = [];
  } finally {
    mediaLoading.value = false;
  }
};

// 根据媒资ID获取媒资信息
const getMediaInfo = (mediaId: string) => {
  return mediaInfos.value.find(info => info.MediaId === mediaId);
};

// 解析剪辑配置 - 显示所有用户可配置的字段
const parsedEditingConfig = computed(() => {
  if (!jobDetail.value?.EditingConfig) return {};
  try {
    const config = JSON.parse(jobDetail.value.EditingConfig);

    return {
      // 音频配置 - 显示所有可配置项
      audioVolume: config.MediaConfig?.Volume !== undefined ? (config.MediaConfig.Volume * 100).toFixed(0) : '50',
      backgroundMusic: config.MediaConfig?.BackgroundMusic || '默认',
      speechSynthesis: config.SpeechConfig !== undefined ? (config.SpeechConfig ? '启用' : '禁用') : '禁用',

      // 处理配置 - 显示所有可配置项
      smartCrop: config.ProcessConfig?.SmartCrop !== undefined ? (config.ProcessConfig.SmartCrop ? '启用' : '禁用') : '禁用',
      autoColor: config.ProcessConfig?.AutoColor !== undefined ? (config.ProcessConfig.AutoColor ? '启用' : '禁用') : '禁用',
      transition: config.ProcessConfig?.Transition !== undefined ? (config.ProcessConfig.Transition ? '启用' : '禁用') : '禁用'
    };
  } catch (error) {
    console.error('解析剪辑配置失败:', error);
    return {
      audioVolume: '50',
      backgroundMusic: '默认',
      speechSynthesis: '禁用',
      smartCrop: '禁用',
      autoColor: '禁用',
      transition: '禁用'
    };
  }
});

// 解析输出配置 - 显示所有用户可配置的字段
const parsedOutputConfig = computed(() => {
  if (!jobDetail.value?.OutputConfig) return {};
  try {
    const config = JSON.parse(jobDetail.value.OutputConfig);

    return {
      count: config.count || 1,
      maxDuration: config.maxDuration || 15,
      width: config.width || 1080,
      height: config.height || 1920,
      widthHeightRatio: config.widthHeightRatio || 0.5625,
      crf: config.videoConfig?.Crf || 23
    };
  } catch (error) {
    console.error('解析输出配置失败:', error);
    return {
      count: 1,
      maxDuration: 15,
      width: 1080,
      height: 1920,
      widthHeightRatio: 0.5625,
      crf: 23
    };
  }
});

// 解析用户数据
const parsedUserData = computed(() => {
  if (!jobDetail.value?.UserData) return {};
  try {
    const userData = JSON.parse(jobDetail.value.UserData);
    return {
      taskName: userData.TaskName || userData.taskName,
      taskDescription: userData.TaskDescription || userData.taskDescription,
      username: userData.Username || userData.username,
      userRole: userData.UserRole || userData.userRole,
      userDept: userData.UserDept || userData.userDept,
      notifyAddress: userData.NotifyAddress || userData.notifyAddress,
      templateName: userData.TemplateName || userData.templateName,
      templateDescription: userData.TemplateDescription || userData.templateDescription
    };
  } catch (error) {
    console.error('解析用户数据失败:', error);
    return {};
  }
});

// 方法定义
const fetchJobDetail = async () => {
  if (!props.jobId) return;
  
  loading.value = true;
  try {
    const response = await getBatchMediaProducingJob(props.jobId);
    jobDetail.value = response.EditingBatchJob;
    console.log('✅ 任务详情获取成功:', jobDetail.value);
    console.log('🔍 InputConfig原始数据:', jobDetail.value?.InputConfig);
    // 获取任务详情后，立即获取媒资信息
    await fetchMediaInfos();
  } catch (error) {
    console.error('❌ 获取任务详情失败:', error);
    ElMessage.error('获取任务详情失败');
  } finally {
    loading.value = false;
  }
};

const refreshDetail = () => {
  fetchJobDetail();
};

const handleClose = () => {
  dialogVisible.value = false;
};

const downloadFile = (url: string) => {
  const link = document.createElement('a');
  link.href = url;
  link.target = '_blank';
  link.download = getFileName(url);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// 监听jobId变化，重新获取详情
watch(() => props.jobId, (newJobId) => {
  if (newJobId && props.visible) {
    fetchJobDetail();
  }
});

// 监听对话框显示状态
watch(() => props.visible, (visible) => {
  if (visible && props.jobId) {
    fetchJobDetail();
  }
});

// 工具方法
const getJobTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    'Script': '脚本化',
    'Smart_Mix': '智能混剪',
    'Smart_Producing': '智能成片',
    'Template_Producing': '模板成片',
    'Batch_Editing': '批量剪辑',
    'Auto_Producing': '自动成片'
  };
  return typeMap[type] || type;
};

const getJobTypeTag = (type: string) => {
  const tagMap: Record<string, string> = {
    'Script': 'primary',
    'Smart_Mix': 'success',
    'Smart_Producing': 'info',
    'Template_Producing': 'warning',
    'Batch_Editing': 'danger',
    'Auto_Producing': 'primary'
  };
  return tagMap[type] || 'info';
};

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'Finished': '已完成',
    'Init': '初始化',
    'Processing': '处理中',
    'Failed': '失败'
  };
  return statusMap[status] || status;
};

const getStatusTag = (status: string) => {
  const tagMap: Record<string, string> = {
    'Finished': 'success',
    'Init': 'info',
    'Processing': 'warning',
    'Failed': 'danger'
  };
  return tagMap[status] || 'info';
};

const getSubJobStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'Success': '成功',
    'Init': '初始化',
    'Processing': '处理中',
    'Failed': '失败'
  };
  return statusMap[status] || status;
};

const getSubJobStatusTag = (status: string) => {
  const tagMap: Record<string, string> = {
    'Success': 'success',
    'Init': 'info',
    'Processing': 'warning',
    'Failed': 'danger'
  };
  return tagMap[status] || 'info';
};

const formatDateTime = (dateTime?: string) => {
  if (!dateTime) return '-';
  try {
    return new Date(dateTime).toLocaleString('zh-CN');
  } catch {
    return dateTime;
  }
};

const getFileName = (url: string) => {
  return url.split('/').pop() || url;
};
</script>

<style scoped>
.job-detail-dialog {
  min-height: 400px;
}

.detail-content {
  padding: 20px 0;
}

/* 配置监控样式 */
.config-monitor-section {
  margin-top: 20px;
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.config-monitor-section h4 {
  margin-bottom: 16px;
  color: #303133;
  font-weight: 600;
  font-size: 16px;
}

.monitor-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.monitor-item {
  background: white;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.monitor-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.monitor-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
  font-weight: 500;
}

.monitor-value {
  font-size: 18px;
  font-weight: 600;
  color: #409eff;
}

/* 模板信息样式 */
.template-section {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #67c23a;
}

.template-section h4 {
  margin-bottom: 12px;
  color: #303133;
  font-weight: 600;
}

.template-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.template-name {
  font-size: 16px;
  font-weight: 600;
  color: #67c23a;
}

.template-description {
  font-size: 14px;
  color: #606266;
}

/* 配置解析样式 */
.config-parsed {
  margin-bottom: 16px;
}

.config-group {
  margin-bottom: 20px;
}

.config-group h5 {
  margin-bottom: 12px;
  color: #409eff;
  font-weight: 600;
  font-size: 14px;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 8px;
}

.media-group {
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.group-header {
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
  font-size: 13px;
}

.media-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
  margin-top: 12px;
}

.media-item-preview {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
  transition: all 0.3s ease;
}

.media-item-preview:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.media-preview {
  display: flex;
  flex-direction: column;
}

.media-thumbnail {
  width: 100%;
  height: 120px;
  object-fit: cover;
  background: #f5f7fa;
}

.media-placeholder {
  width: 100%;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  color: #c0c4cc;
  font-size: 24px;
}

.media-info {
  padding: 8px 12px;
}

.media-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.media-type {
  font-size: 12px;
  color: #909399;
}

/* 输入配置相关样式 */
.group-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  padding: 8px 12px;
  background: #f5f7fa;
  border-radius: 6px;
}

.group-name {
  font-weight: 600;
  color: #303133;
}

.group-count {
  color: #909399;
  font-size: 12px;
}

.group-config {
  margin-top: 12px;
  padding: 8px;
  background: #fafbfc;
  border-radius: 4px;
}

.title-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.title-item {
  margin-bottom: 4px;
}

.sub-heading-group {
  margin-bottom: 16px;
}

.sub-heading-level {
  font-weight: 600;
  color: #606266;
  margin-bottom: 8px;
}

.speech-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 8px;
}

.speech-item {
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #409eff;
}

.speech-index {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.speech-content {
  color: #303133;
  line-height: 1.5;
}

.sticker-list, .background-music-list, .background-image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.sticker-item {
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.music-item, .image-item {
  margin-bottom: 4px;
}

/* 移除了empty-config样式，因为不显示空状态 */

.media-item {
  background: white;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  font-size: 12px;
  color: #606266;
}

.title-list,
.speech-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.title-item,
.speech-item {
  padding: 8px 12px;
  background: white;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  font-size: 13px;
  color: #303133;
}

.output-url {
  padding: 12px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #606266;
  word-break: break-all;
}

.error-section,
.config-section,
.output-files-section,
.template-section {
  margin-top: 20px;
}

.error-section h4,
.config-section h4,
.output-files-section h4 {
  margin-bottom: 12px;
  color: #303133;
  font-weight: 600;
}

.file-url {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.no-data {
  text-align: center;
  padding: 60px 0;
}

.dialog-footer {
  text-align: right;
}
</style>
