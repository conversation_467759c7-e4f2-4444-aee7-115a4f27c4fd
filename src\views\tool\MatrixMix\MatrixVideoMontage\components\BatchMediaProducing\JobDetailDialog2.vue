<template>
  <el-dialog
    v-model="dialogVisible"
    title="任务详情"
    width="80%"
    :before-close="handleClose"
  >
    <div v-loading="loading" class="job-detail-dialog">
      <div v-if="jobDetail" class="detail-content">
        <!-- 基本信息 -->
        <el-descriptions title="基本信息" :column="2" border>
          <el-descriptions-item label="任务ID">{{ jobDetail.JobId }}</el-descriptions-item>
          <el-descriptions-item label="任务类型">
            <el-tag :type="getJobTypeTag(jobDetail.JobType)">
              {{ getJobTypeText(jobDetail.JobType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="任务状态">
            <el-tag :type="getStatusTag(jobDetail.Status)">
              {{ getStatusText(jobDetail.Status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(jobDetail.CreateTime) }}</el-descriptions-item>
          <el-descriptions-item label="修改时间">{{ formatDateTime(jobDetail.ModifiedTime) }}</el-descriptions-item>
          <el-descriptions-item label="完成时间">{{ formatDateTime(jobDetail.CompleteTime) }}</el-descriptions-item>
        </el-descriptions>

        <!-- 错误信息 -->
        <div v-if="jobDetail.Status === 'Failed' && jobDetail.Extend?.ErrorMessage" class="error-section">
          <h4>错误信息</h4>
          <el-alert 
            :title="jobDetail.Extend.ErrorMessage" 
            type="error" 
            show-icon 
            :closable="false"
          />
        </div>

        <!-- 配置信息 -->
        <div class="config-section">
          <h4>配置信息</h4>
          <el-tabs>
            <el-tab-pane label="输入配置" name="input">
              <el-input
                v-model="jobDetail.InputConfig"
                type="textarea"
                :rows="8"
                readonly
                placeholder="输入配置信息"
              />
            </el-tab-pane>
            <el-tab-pane label="剪辑配置" name="editing">
              <el-input
                v-model="jobDetail.EditingConfig"
                type="textarea"
                :rows="8"
                readonly
                placeholder="剪辑配置信息"
              />
            </el-tab-pane>
            <el-tab-pane label="输出配置" name="output">
              <el-input
                v-model="jobDetail.OutputConfig"
                type="textarea"
                :rows="8"
                readonly
                placeholder="输出配置信息"
              />
            </el-tab-pane>
            <el-tab-pane label="用户数据" name="userData" v-if="jobDetail.UserData">
              <el-input
                v-model="jobDetail.UserData"
                type="textarea"
                :rows="8"
                readonly
                placeholder="用户数据"
              />
            </el-tab-pane>
          </el-tabs>
        </div>

        <!-- 输出文件列表 -->
        <div v-if="jobDetail.SubJobList && jobDetail.SubJobList.length > 0" class="output-files-section">
          <h4>输出文件 ({{ jobDetail.SubJobList.length }})</h4>
          <el-table :data="jobDetail.SubJobList" border>
            <el-table-column prop="JobId" label="子任务ID" width="200" />
            <el-table-column prop="MediaId" label="媒资ID" width="200" />
            <el-table-column prop="Status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getSubJobStatusTag(row.Status)">
                  {{ getSubJobStatusText(row.Status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="Duration" label="时长" width="80">
              <template #default="{ row }">
                {{ row.Duration ? `${row.Duration}s` : '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="MediaURL" label="文件地址" min-width="200">
              <template #default="{ row }">
                <div v-if="row.MediaURL" class="file-url">
                  <el-link :href="row.MediaURL" target="_blank" type="primary">
                    {{ getFileName(row.MediaURL) }}
                  </el-link>
                </div>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="{ row }">
                <el-button
                  v-if="row.MediaURL && row.Status === 'Success'"
                  size="small"
                  type="primary"
                  @click="downloadFile(row.MediaURL)"
                >
                  下载
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <div v-else class="no-data">
        <el-empty description="暂无数据" />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="refreshDetail">刷新</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { getBatchMediaProducingJob } from '../../../api/batchProducing';
import type { BatchMediaProducingJobDetail } from '../../../api/batchProducing';

// Props
const props = defineProps<{
  visible: boolean;
  jobId: string;
}>();

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
}>();

// 响应式数据
const loading = ref(false);
const jobDetail = ref<BatchMediaProducingJobDetail | null>(null);

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
});

// 方法定义
const fetchJobDetail = async () => {
  if (!props.jobId) return;
  
  loading.value = true;
  try {
    const response = await getBatchMediaProducingJob(props.jobId);
    jobDetail.value = response.EditingBatchJob;
    console.log('✅ 任务详情获取成功:', jobDetail.value);
  } catch (error) {
    console.error('❌ 获取任务详情失败:', error);
    ElMessage.error('获取任务详情失败');
  } finally {
    loading.value = false;
  }
};

const refreshDetail = () => {
  fetchJobDetail();
};

const handleClose = () => {
  dialogVisible.value = false;
};

const downloadFile = (url: string) => {
  const link = document.createElement('a');
  link.href = url;
  link.target = '_blank';
  link.download = getFileName(url);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// 监听jobId变化，重新获取详情
watch(() => props.jobId, (newJobId) => {
  if (newJobId && props.visible) {
    fetchJobDetail();
  }
});

// 监听对话框显示状态
watch(() => props.visible, (visible) => {
  if (visible && props.jobId) {
    fetchJobDetail();
  }
});

// 工具方法
const getJobTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    'Script': '脚本化',
    'Smart_Mix': '智能混剪',
    'Smart_Producing': '智能成片',
    'Template_Producing': '模板成片',
    'Batch_Editing': '批量剪辑',
    'Auto_Producing': '自动成片'
  };
  return typeMap[type] || type;
};

const getJobTypeTag = (type: string) => {
  const tagMap: Record<string, string> = {
    'Script': 'primary',
    'Smart_Mix': 'success',
    'Smart_Producing': 'info',
    'Template_Producing': 'warning',
    'Batch_Editing': 'danger',
    'Auto_Producing': 'primary'
  };
  return tagMap[type] || 'info';
};

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'Finished': '已完成',
    'Init': '初始化',
    'Processing': '处理中',
    'Failed': '失败'
  };
  return statusMap[status] || status;
};

const getStatusTag = (status: string) => {
  const tagMap: Record<string, string> = {
    'Finished': 'success',
    'Init': 'info',
    'Processing': 'warning',
    'Failed': 'danger'
  };
  return tagMap[status] || 'info';
};

const getSubJobStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'Success': '成功',
    'Init': '初始化',
    'Processing': '处理中',
    'Failed': '失败'
  };
  return statusMap[status] || status;
};

const getSubJobStatusTag = (status: string) => {
  const tagMap: Record<string, string> = {
    'Success': 'success',
    'Init': 'info',
    'Processing': 'warning',
    'Failed': 'danger'
  };
  return tagMap[status] || 'info';
};

const formatDateTime = (dateTime?: string) => {
  if (!dateTime) return '-';
  try {
    return new Date(dateTime).toLocaleString('zh-CN');
  } catch {
    return dateTime;
  }
};

const getFileName = (url: string) => {
  return url.split('/').pop() || url;
};
</script>

<style scoped>
.job-detail-dialog {
  min-height: 400px;
}

.detail-content {
  padding: 20px 0;
}

.error-section,
.config-section,
.output-files-section {
  margin-top: 20px;
}

.error-section h4,
.config-section h4,
.output-files-section h4 {
  margin-bottom: 12px;
  color: #303133;
  font-weight: 600;
}

.file-url {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.no-data {
  text-align: center;
  padding: 60px 0;
}

.dialog-footer {
  text-align: right;
}
</style>
