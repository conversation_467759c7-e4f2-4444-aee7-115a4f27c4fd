<template>
  <el-dialog v-model="localVisible" title="检索素材库" width="800px" @close="closeDialog">
    <div class="media-selector-toolbar">
      <el-select v-model="typeFilter" placeholder="类型" class="type-select" clearable>
        <el-option label="全部" value="" />
        <el-option label="视频" value="video" />
        <el-option label="图片" value="image" />
        <el-option label="音频" value="audio" />
      </el-select>
      <el-button @click="refreshMedia" icon="el-icon-refresh" :loading="loading">刷新</el-button>
      <el-button type="primary" @click="onUpload" icon="el-icon-upload">上传素材</el-button>
    </div>
    <el-table :data="filteredList" style="width: 100%" @selection-change="onSelectChange" :row-key="(row: MediaItem) => row.id" ref="tableRef" :height="400">
      <el-table-column type="selection" width="55" />
      <el-table-column label="预览" width="80">
        <template #default="{ row }: { row: MediaItem }">
          <img v-if="row.type === 'image'" :src="row.thumbnail" style="width:60px;height:40px;object-fit:cover;" />
          <video v-else-if="row.type === 'video'" :src="row.url" style="width:60px;height:40px;object-fit:cover;" muted />
          <i v-else class="el-icon-headset" style="font-size:24px;color:#888;"></i>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="名称" min-width="120" />
      <el-table-column prop="type" label="类型" width="60">
        <template #default="{ row }: { row: MediaItem }">
          <span v-if="row.type==='video'">视频</span>
          <span v-else-if="row.type==='image'">图片</span>
          <span v-else>音频</span>
        </template>
      </el-table-column>
      <el-table-column prop="duration" label="时长(s)" width="80" />
      <el-table-column prop="size" label="大小" width="80" />
    </el-table>
    <div class="pagination-bar">
      <el-pagination
        background
        layout="prev, pager, next"
        :total="filteredList.length"
        :page-size="pageSize"
        v-model:current-page="page"
      />
    </div>
    <template #footer>
      <el-button @click="closeDialog">取消</el-button>
      <el-button type="primary" @click="confirmSelect">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits } from 'vue';
import type { MediaItem } from '../../types/batchProducing';

const props = defineProps<{
  visible: boolean;
  availableMedia?: MediaItem[];
  loading?: boolean;
}>();
const emit = defineEmits(['update:visible', 'close', 'confirm', 'refresh']);

// 使用computed进行双向绑定
const localVisible = computed({
  get: () => {
    console.log('📱 MediaSelectorDialog visible状态:', props.visible);
    return props.visible;
  },
  set: (val) => {
    console.log('📱 MediaSelectorDialog 设置visible状态:', val);
    emit('update:visible', val);
  }
});

const closeDialog = () => {
  emit('update:visible', false);
  emit('close');
};

const search = ref('');
const typeFilter = ref('');
const page = ref(1);
const pageSize = 8;
const selected = ref<MediaItem[]>([]);

// 使用外部传入的媒体数据
const allMedia = computed(() => {
  return props.availableMedia || [];
});

const filteredList = computed(() => {
  let list = allMedia.value;
  if (search.value) {
    list = list.filter(item => item.name.includes(search.value));
  }
  if (typeFilter.value) {
    list = list.filter(item => item.type === typeFilter.value);
  }
  // 分页
  const start = (page.value - 1) * pageSize;
  return list.slice(start, start + pageSize);
});

const onSelectChange = (rows: MediaItem[]) => {
  selected.value = rows;
};

const confirmSelect = () => {
  emit('confirm', selected.value);
  closeDialog();
};

const refreshMedia = () => {
  emit('refresh');
};

const onUpload = () => {
  // 这里可以集成上传逻辑或跳转到素材管理页
  alert('上传功能可集成OSS/自定义素材管理页');
};
</script>

<style scoped>
.media-selector-toolbar {
  display: flex;
  gap: 10px;
  margin-bottom: 12px;
  align-items: center;
}
.search-input {
  flex: 2;
}
.type-select {
  width: 120px;
}
.pagination-bar {
  margin: 16px 0 0 0;
  text-align: right;
}
</style> 