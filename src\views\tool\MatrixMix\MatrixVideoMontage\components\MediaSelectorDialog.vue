<template>
  <el-dialog v-model="localVisible" title="检索素材库" width="900px" @close="closeDialog">
    <div class="media-selector-toolbar">
      <el-select v-model="mediaType" placeholder="类型" class="type-select" clearable @change="handleTypeChange">
        <el-option label="全部" value="" />
        <el-option label="视频" value="video" />
        <el-option label="图片" value="image" />
        <el-option label="音频" value="audio" />
        <el-option label="字体" value="font" />
      </el-select>
      <el-button @click="loadMediaList(1)" icon="el-icon-refresh" :loading="loading">刷新</el-button>
      <el-button type="primary" @click="onUpload" icon="el-icon-upload">上传素材</el-button>
    </div>

    <el-table
      v-loading="loading"
      :data="mediaList"
      style="width: 100%"
      @selection-change="onSelectChange"
      :row-key="(row: MediaItem) => row.id"
      ref="tableRef"
      :height="400"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="预览" width="80">
        <template #default="{ row }">
          <img v-if="row.type === 'image'" :src="row.thumbnail" style="width:60px;height:40px;object-fit:cover;" />
          <video v-else-if="row.type === 'video'" :src="row.url" style="width:60px;height:40px;object-fit:cover;" muted />
          <i v-else-if="row.type === 'audio'" class="el-icon-headset" style="font-size:24px;color:#888;"></i>
          <i v-else-if="row.type === 'font'" class="el-icon-document" style="font-size:24px;color:#888;"></i>
          <i v-else class="el-icon-document" style="font-size:24px;color:#888;"></i>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="名称" min-width="150" show-overflow-tooltip />
      <el-table-column label="类型" width="80">
        <template #default="{ row }">
          <el-tag :type="getTypeTagType(row.type)" size="small">
            {{ getTypeLabel(row.type) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="duration" label="时长(s)" width="80">
        <template #default="{ row }">
          {{ row.duration > 0 ? row.duration.toFixed(1) : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="size" label="大小" width="80" />
    </el-table>

    <div class="pagination-bar">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[8, 16, 24, 32]"
        :total="total"
        layout="total, sizes, prev, pager, next"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>

    <template #footer>
      <span class="selected-info">已选择 {{ selected.length }} 个素材</span>
      <el-button @click="closeDialog">取消</el-button>
      <el-button type="primary" @click="confirmSelect" :disabled="selected.length === 0">
        确定选择
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { listMediaBasicInfo } from '../../api/media';
import type { MediaListQueryParams } from '../../types/media';
import type { MediaItem, MediaType } from '../../types/batchProducing';

const props = defineProps<{
  visible: boolean;
}>();
const emit = defineEmits(['update:visible', 'close', 'confirm']);

// 使用computed进行双向绑定
const localVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 响应式数据
const loading = ref(false);
const mediaType = ref('');
const currentPage = ref(1);
const pageSize = ref(8); // 每页显示8个
const total = ref(0);
const nextToken = ref('');
const mediaList = ref<MediaItem[]>([]);
const selected = ref<MediaItem[]>([]);
const tableRef = ref();

// 将前端媒体类型转换为API媒体类型
const convertToApiMediaType = (frontendType: string): 'image' | 'video' | 'audio' | 'text' | undefined => {
  const typeMap: Record<string, 'image' | 'video' | 'audio' | 'text'> = {
    'video': 'video',
    'image': 'image',
    'audio': 'audio',
    'font': 'text' // 字体类型映射为text
  };
  return frontendType ? typeMap[frontendType] : undefined;
};

// 获取媒资列表
const loadMediaList = async (page = 1) => {
  loading.value = true;
  try {
    const query: MediaListQueryParams = {
      MediaType: convertToApiMediaType(mediaType.value),
      MaxResults: pageSize.value,
      NextToken: page === 1 ? '' : nextToken.value,
      SortBy: 'desc',
      IncludeFileBasicInfo: true
    };

    console.log('📚 加载媒体库，参数:', query);
    console.log('📚 请求每页数量:', pageSize.value);
    const response = await listMediaBasicInfo(query);
    console.log('📚 媒体库API响应:', response);
    console.log('📚 API返回数据量:', response.MediaInfos?.length);

    if (response.RequestId) {
      // 处理媒体数据
      const processedMedia = (response.MediaInfos || []).map(media => {
        const fileInfo = media.FileInfoList?.[0];
        const basicInfo = fileInfo?.FileBasicInfo;

        return {
          id: media.MediaId,
          name: basicInfo?.FileName || media.MediaBasicInfo?.Title || media.MediaId,
          type: getMediaTypeFromAPI(media.MediaBasicInfo?.MediaType || ''),
          url: basicInfo?.FileUrl || '',
          thumbnail: basicInfo?.FileUrl || media.MediaBasicInfo?.CoverURL || '',
          duration: parseFloat(basicInfo?.Duration || '0') || 0,
          size: formatBytes(parseInt(basicInfo?.FileSize || '0') || 0)
        };
      });

      // 强制限制每页显示数量
      const limitedMedia = processedMedia.slice(0, pageSize.value);

      mediaList.value = limitedMedia;
      total.value = response.TotalCount || limitedMedia.length;
      nextToken.value = response.NextToken || '';
      currentPage.value = page;

      console.log('✅ 媒体库加载成功:', processedMedia.length, '个媒体，显示:', limitedMedia.length, '个');
      console.log('📊 分页信息 - 当前页:', currentPage.value, '每页:', pageSize.value, '总数:', total.value);
    }
  } catch (error) {
    console.error('❌ 加载媒体库失败:', error);
    ElMessage.error('加载媒体库失败');
    mediaList.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 将API返回的媒体类型转换为前端使用的类型
const getMediaTypeFromAPI = (apiType: string): MediaType => {
  const typeMap: Record<string, MediaType> = {
    'video': 'video',
    'image': 'image',
    'audio': 'audio',
    'text': 'font' // API的text类型映射为前端的font类型
  };
  return typeMap[apiType.toLowerCase()] || 'video';
};

// 格式化文件大小
const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

// 获取类型标签样式
const getTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    'video': 'primary',
    'image': 'success',
    'audio': 'warning',
    'font': 'info'
  };
  return typeMap[type] || 'info';
};

// 获取类型显示标签
const getTypeLabel = (type: string) => {
  const labelMap: Record<string, string> = {
    'video': '视频',
    'image': '图片',
    'audio': '音频',
    'font': '字体'
  };
  return labelMap[type] || type;
};

// 事件处理方法
const handleTypeChange = () => {
  // 类型变化时重新加载第一页
  currentPage.value = 1;
  loadMediaList(1);
};

const handlePageChange = (page: number) => {
  loadMediaList(page);
};

const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  loadMediaList(1);
};

const onSelectChange = (rows: MediaItem[]) => {
  selected.value = rows;
};

const confirmSelect = () => {
  if (selected.value.length === 0) {
    ElMessage.warning('请选择至少一个素材');
    return;
  }

  console.log('✅ 确认选择素材:', selected.value);
  console.log('🔄 准备关闭弹窗...');

  // 先发送确认事件
  emit('confirm', selected.value);

  // 立即关闭弹窗
  closeDialog();

  console.log('🔒 弹窗关闭完成');
};

const closeDialog = () => {
  console.log('🔄 开始关闭弹窗...');
  console.log('📊 关闭前状态 - visible:', props.visible, 'selected:', selected.value.length);

  // 发送关闭事件
  emit('update:visible', false);
  emit('close');

  // 清空选择状态
  selected.value = [];

  // 清空表格选择状态
  if (tableRef.value) {
    tableRef.value.clearSelection();
    console.log('🧹 表格选择状态已清空');
  }

  console.log('✅ 弹窗关闭事件已发送');
};

const onUpload = () => {
  ElMessage.info('上传功能可集成OSS/自定义素材管理页');
};

// 监听弹窗显示状态，显示时加载数据
watch(() => props.visible, (visible, oldVisible) => {
  if (visible && !oldVisible) {
    console.log('🔄 弹窗打开，重新加载媒体列表');
    // 重置状态
    currentPage.value = 1;
    selected.value = [];
    if (tableRef.value) {
      tableRef.value.clearSelection();
    }
    loadMediaList(1);
  }
});
</script>

<style scoped>
.media-selector-toolbar {
  display: flex;
  gap: 10px;
  margin-bottom: 12px;
  align-items: center;
}
.type-select {
  width: 120px;
}
.pagination-bar {
  margin: 16px 0 0 0;
  text-align: right;
}
.selected-info {
  color: #606266;
  font-size: 14px;
  margin-right: 16px;
}
</style>