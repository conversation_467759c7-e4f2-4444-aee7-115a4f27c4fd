<template>
  <el-dialog v-model="localVisible" title="检索素材库" width="900px" @close="closeDialog">
    <div class="media-selector-toolbar">
      <el-select v-model="mediaType" placeholder="类型" class="type-select" clearable @change="handleTypeChange">
        <el-option label="全部" value="" />
        <el-option label="视频" value="video" />
        <el-option label="图片" value="image" />
        <el-option label="音频" value="audio" />
        <el-option label="字体" value="font" />
      </el-select>
      <el-button @click="loadMediaList(1)" icon="el-icon-refresh" :loading="loading">刷新</el-button>
      <el-button type="primary" @click="onUpload" icon="el-icon-upload">上传素材</el-button>
    </div>

    <el-table
      v-loading="loading"
      :data="mediaList"
      style="width: 100%"
      @selection-change="onSelectChange"
      :row-key="(row: MediaItem) => row.id"
      ref="tableRef"
      :height="400"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="预览" width="80">
        <template #default="{ row }">
          <div class="preview-container">
            <!-- 图片预览 -->
            <img
              v-if="row.type === 'image'"
              :src="row.thumbnail"
              style="width:60px;height:40px;object-fit:cover;border-radius:4px;"
              @error="handleImageError"
              :alt="row.name"
            />
            <!-- 视频预览 - 显示缩略图 -->
            <div v-else-if="row.type === 'video'" class="video-preview">
              <img
                :src="row.thumbnail"
                style="width:60px;height:40px;object-fit:cover;border-radius:4px;"
                @error="handleImageError"
                :alt="row.name"
              />
              <div class="video-overlay">
                <i class="el-icon-video-play" style="font-size:16px;color:white;"></i>
              </div>
            </div>
            <!-- 音频图标 -->
            <div v-else-if="row.type === 'audio'" class="media-icon">
              <i class="el-icon-headset" style="font-size:24px;color:#409eff;"></i>
            </div>
            <!-- 字体图标 -->
            <div v-else-if="row.type === 'font'" class="media-icon">
              <i class="el-icon-document" style="font-size:24px;color:#67c23a;"></i>
            </div>
            <!-- 默认图标 -->
            <div v-else class="media-icon">
              <i class="el-icon-document" style="font-size:24px;color:#909399;"></i>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="名称" min-width="150" show-overflow-tooltip />
      <el-table-column label="类型" width="80">
        <template #default="{ row }">
          <el-tag :type="getTypeTagType(row.type)" size="small">
            {{ getTypeLabel(row.type) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="duration" label="时长(s)" width="80">
        <template #default="{ row }">
          {{ row.duration > 0 ? row.duration.toFixed(1) : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="size" label="大小" width="80" />
    </el-table>

    <div class="pagination-bar">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[8, 16, 24, 32]"
        :total="total"
        layout="total, sizes, prev, pager, next"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>

    <template #footer>
      <span class="selected-info">已选择 {{ selected.length }} 个素材</span>
      <el-button @click="closeDialog">取消</el-button>
      <el-button type="primary" @click="confirmSelect" :disabled="selected.length === 0">
        确定选择
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { listMediaBasicInfo } from '../../api/media';
import type { MediaListQueryParams } from '../../types/media';
import type { MediaItem, MediaType } from '../../types/batchProducing';

const props = defineProps<{
  visible: boolean;
}>();
const emit = defineEmits(['update:visible', 'close', 'confirm']);

// 使用computed进行双向绑定
const localVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 响应式数据
const loading = ref(false);
const mediaType = ref('');
const currentPage = ref(1);
const pageSize = ref(8); // 每页显示8个
const total = ref(0);
const nextToken = ref('');
const mediaList = ref<MediaItem[]>([]);
const selected = ref<MediaItem[]>([]);
const tableRef = ref();

// 将前端媒体类型转换为API媒体类型
const convertToApiMediaType = (frontendType: string): 'image' | 'video' | 'audio' | 'text' | undefined => {
  const typeMap: Record<string, 'image' | 'video' | 'audio' | 'text'> = {
    'video': 'video',
    'image': 'image',
    'audio': 'audio',
    'font': 'text' // 字体类型映射为text
  };
  return frontendType ? typeMap[frontendType] : undefined;
};

// 获取媒资列表
const loadMediaList = async (page = 1) => {
  loading.value = true;
  try {
    const query: MediaListQueryParams = {
      MediaType: convertToApiMediaType(mediaType.value),
      MaxResults: pageSize.value,
      NextToken: page === 1 ? '' : nextToken.value,
      SortBy: 'desc',
      IncludeFileBasicInfo: true
    };

    console.log('📚 加载媒体库，参数:', query);
    const response = await listMediaBasicInfo(query);
    console.log('📚 媒体库API响应:', response);

    // 直接使用response.MediaInfos，参考你提供的代码
    const mediaInfos = response.MediaInfos || [];
    console.log('📚 API返回数据量:', mediaInfos.length);

    // 处理媒体数据，正确解析API返回的结构
    const processedMedia = mediaInfos.map(media => {
      // 获取文件信息 - 使用FileInfoList数组的第一个元素
      const fileInfo = media.FileInfoList?.[0];
      const basicInfo = fileInfo?.FileBasicInfo;

      // 获取媒体基本信息
      const mediaBasicInfo = media.MediaBasicInfo || {};

      return {
        id: media.MediaId,
        name: basicInfo?.FileName || mediaBasicInfo.Title || media.MediaId,
        type: getMediaTypeFromAPI(mediaBasicInfo.MediaType || ''),
        url: basicInfo?.FileUrl || '',
        // 使用完整的预览图获取逻辑
        thumbnail: getPreviewImage(media),
        duration: parseFloat(basicInfo?.Duration || '0') || 0,
        size: formatBytes(parseInt(basicInfo?.FileSize || '0') || 0),
        // 添加更多信息用于调试
        width: basicInfo?.Width || 0,
        height: basicInfo?.Height || 0,
        createTime: mediaBasicInfo.CreateTime || '',
        // 保存原始媒体数据用于调试
        originalMedia: media
      };
    });

    mediaList.value = processedMedia;
    total.value = response.TotalCount || processedMedia.length;
    nextToken.value = response.NextToken || '';
    currentPage.value = page;

    console.log('✅ 媒体库加载成功:', processedMedia.length, '个媒体');
    console.log('📊 分页信息 - 当前页:', currentPage.value, '每页:', pageSize.value, '总数:', total.value);

    // 详细调试第一个媒体的信息
    if (processedMedia.length > 0) {
      const firstMedia = processedMedia[0];
      const firstOriginal = mediaInfos[0];
      const firstFileInfo = firstOriginal.FileInfoList?.[0];
      const firstBasicInfo = firstFileInfo?.FileBasicInfo;
      const firstMediaBasicInfo = firstOriginal.MediaBasicInfo || {};

      console.log('🖼️ 第一个媒体详细信息:', {
        id: firstMedia.id,
        name: firstMedia.name,
        type: firstMedia.type,
        url: firstMedia.url,
        thumbnail: firstMedia.thumbnail,
        hasFileInfo: !!firstFileInfo,
        hasBasicInfo: !!firstBasicInfo,
        hasMediaBasicInfo: !!firstMediaBasicInfo,
        coverURL: firstMediaBasicInfo.CoverURL,
        fileUrl: firstBasicInfo?.FileUrl,
        originalMedia: firstOriginal
      });
    }

  } catch (error) {
    console.error('❌ 加载媒体库失败:', error);
    ElMessage.error('加载媒体库失败');
    mediaList.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 将API返回的媒体类型转换为前端使用的类型
const getMediaTypeFromAPI = (apiType: string): MediaType => {
  const typeMap: Record<string, MediaType> = {
    'video': 'video',
    'image': 'image',
    'audio': 'audio',
    'text': 'font' // API的text类型映射为前端的font类型
  };
  return typeMap[apiType.toLowerCase()] || 'video';
};

// 格式化文件大小
const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

// 获取类型标签样式
const getTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    'video': 'primary',
    'image': 'success',
    'audio': 'warning',
    'font': 'info'
  };
  return typeMap[type] || 'info';
};

// 获取类型显示标签
const getTypeLabel = (type: string) => {
  const labelMap: Record<string, string> = {
    'video': '视频',
    'image': '图片',
    'audio': '音频',
    'font': '字体'
  };
  return labelMap[type] || type;
};

// 事件处理方法
const handleTypeChange = () => {
  // 类型变化时重新加载第一页
  currentPage.value = 1;
  loadMediaList(1);
};

const handlePageChange = (page: number) => {
  loadMediaList(page);
};

const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  loadMediaList(1);
};

const onSelectChange = (rows: MediaItem[]) => {
  selected.value = rows;
};

const confirmSelect = () => {
  if (selected.value.length === 0) {
    ElMessage.warning('请选择至少一个素材');
    return;
  }

  console.log('✅ 确认选择素材:', selected.value);
  console.log('🔄 准备关闭弹窗...');

  // 先发送确认事件
  emit('confirm', selected.value);

  // 立即关闭弹窗
  closeDialog();

  console.log('🔒 弹窗关闭完成');
};

const closeDialog = () => {
  console.log('🔄 开始关闭弹窗...');
  console.log('📊 关闭前状态 - visible:', props.visible, 'selected:', selected.value.length);

  // 发送关闭事件
  emit('update:visible', false);
  emit('close');

  // 清空选择状态
  selected.value = [];

  // 清空表格选择状态
  if (tableRef.value) {
    tableRef.value.clearSelection();
    console.log('🧹 表格选择状态已清空');
  }

  console.log('✅ 弹窗关闭事件已发送');
};

const onUpload = () => {
  ElMessage.info('上传功能可集成OSS/自定义素材管理页');
};

// 获取媒体预览图（参考VideoLibrary的实现）
const getPreviewImage = (media: any): string => {
  const info = media.MediaBasicInfo || {};

  // 1. 优先使用CoverURL
  if (info.CoverURL) return info.CoverURL;

  // 2. 检查Snapshots（可能为字符串或数组）
  if (info.Snapshots) {
    try {
      if (Array.isArray(info.Snapshots)) {
        if (info.Snapshots.length > 0) return info.Snapshots[0];
      } else if (typeof info.Snapshots === 'string') {
        // 可能是逗号分隔或JSON数组
        if (info.Snapshots.startsWith('[')) {
          const arr = JSON.parse(info.Snapshots);
          if (Array.isArray(arr) && arr.length > 0) return arr[0];
        } else if (info.Snapshots.includes(',')) {
          return info.Snapshots.split(',')[0];
        } else {
          return info.Snapshots;
        }
      }
    } catch {}
  }

  // 3. 检查SpriteImages
  if (info.SpriteImages) {
    try {
      if (typeof info.SpriteImages === 'string') {
        if (info.SpriteImages.startsWith('[')) {
          const arr = JSON.parse(info.SpriteImages);
          if (Array.isArray(arr) && arr.length > 0) return arr[0];
        } else if (info.SpriteImages.includes(',')) {
          return info.SpriteImages.split(',')[0];
        } else {
          return info.SpriteImages;
        }
      }
    } catch {}
  }

  // 4. 使用文件URL作为备选
  const fileInfo = media.FileInfoList?.[0];
  const basicInfo = fileInfo?.FileBasicInfo;
  if (basicInfo?.FileUrl) return basicInfo.FileUrl;

  // 5. 返回占位图
  return '/placeholder-video.jpg';
};

// 图片加载错误处理
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement;
  if (img) {
    img.src = '/placeholder-video.jpg';
  }
};

// 视频加载错误处理
const handleVideoError = (event: Event) => {
  const video = event.target as HTMLVideoElement;
  if (video) {
    // 视频加载失败时，可以显示一个默认的视频图标
    video.style.display = 'none';
    const parent = video.parentElement;
    if (parent) {
      parent.innerHTML = '<i class="el-icon-video-camera" style="font-size:24px;color:#888;"></i>';
    }
  }
};

// 监听弹窗显示状态，显示时加载数据
watch(() => props.visible, (visible, oldVisible) => {
  if (visible && !oldVisible) {
    console.log('🔄 弹窗打开，重新加载媒体列表');
    // 重置状态
    currentPage.value = 1;
    selected.value = [];
    if (tableRef.value) {
      tableRef.value.clearSelection();
    }
    loadMediaList(1);
  }
});
</script>

<style scoped>
.media-selector-toolbar {
  display: flex;
  gap: 10px;
  margin-bottom: 12px;
  align-items: center;
}
.type-select {
  width: 120px;
}
.pagination-bar {
  margin: 16px 0 0 0;
  text-align: right;
}
.selected-info {
  color: #606266;
  font-size: 14px;
  margin-right: 16px;
}

.preview-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 40px;
  background-color: #f5f7fa;
  border-radius: 4px;
  overflow: hidden;
}

.preview-container img,
.preview-container video {
  max-width: 100%;
  max-height: 100%;
  object-fit: cover;
}

.media-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.video-preview {
  position: relative;
  width: 60px;
  height: 40px;
  border-radius: 4px;
  overflow: hidden;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
  opacity: 0;
  transition: opacity 0.2s;
}

.video-preview:hover .video-overlay {
  opacity: 1;
}
</style>