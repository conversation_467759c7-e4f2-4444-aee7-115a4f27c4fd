<template>
  <div class="media-library">
    <!-- 顶部标签栏 -->
    <div class="header-tabs">
      <div class="tab-item" :class="{ active: activeTab === 'batchMediaProducing' }"
        @click="switchTab('batchMediaProducing')">
        <el-icon>
          <VideoPlay />
        </el-icon>
        <span>AI批量剪辑</span>
      </div>
      <div class="tab-item" :class="{ active: activeTab === 'batchJobList' }" @click="switchTab('batchJobList')">
        <el-icon>
          <Microphone />
        </el-icon>
        <span>批量智能一键成片任务列表</span>
      </div>
      <div class="tab-item" :class="{ active: activeTab === 'nothing' }" @click="switchTab('nothing')">
        <el-icon>
          <Microphone />
        </el-icon>
        <span>待制作</span>
      </div>
      <div class="tab-item" :class="{ active: activeTab === 'nothing' }" @click="switchTab('nothing')">
        <el-icon>
          <Picture />
        </el-icon>
        <span>待制作</span>
      </div>
      <div class="tab-item" :class="{ active: activeTab === 'nothing' }" @click="switchTab('nothing')">
        <el-icon>
          <EditPen />
        </el-icon>
        <span>待制作</span>
      </div>
    </div>

    <!-- 内容区 -->
    <div class="content-area">
      <component :is="currentComponent" :key="activeTab" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import BatchMediaProducing from './components/BatchMediaProducing.vue';
import BatchJobList from './components/BatchMediaProducing/BatchJobList.vue';

const activeTab = ref('batchMediaProducing');

const componentMap = {
  batchMediaProducing: BatchMediaProducing,
  batchJobList: BatchJobList,
};

// 当前组件
const currentComponent = computed(() => componentMap[activeTab.value as keyof typeof componentMap])

// 切换标签页
const switchTab = (tab: string) => {
  activeTab.value = tab
}


const goBack = () => {
  console.log('返回上一页');
};
</script>

<style lang="scss" scoped>
.media-library {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

.header-tabs {
  display: flex;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  .tab-item {
    display: flex;
    align-items: center;
    padding: 15px 25px;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    border-bottom: 3px solid transparent;
    white-space: nowrap;
    position: relative;

    .el-icon {
      margin-right: 8px;
      font-size: 16px;
    }

    span {
      font-weight: 400;
    }

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
      color: #ffffff;
    }

    &.active {
      background-color: rgba(255, 255, 255, 0.15);
      color: #ffffff;
      border-bottom-color: #ffffff;
      font-weight: 500;

      span {
        font-weight: 500;
      }
    }

    &:not(:last-child) {
      border-right: 1px solid rgba(255, 255, 255, 0.1);
    }
  }
}

.content-area {
  flex: 1;
  background-color: #ffffff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-tabs {
    overflow-x: auto;
    white-space: nowrap;

    .tab-item {
      flex-shrink: 0;
      padding: 12px 20px;
      font-size: 13px;

      .el-icon {
        margin-right: 6px;
        font-size: 14px;
      }
    }
  }
}

@media (max-width: 480px) {
  .header-tabs {
    .tab-item {
      padding: 10px 15px;
      font-size: 12px;

      span {
        display: none;
      }

      .el-icon {
        margin-right: 0;
        font-size: 18px;
      }
    }
  }
}
</style>
