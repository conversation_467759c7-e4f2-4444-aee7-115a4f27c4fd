<template>
  <div class="font-library">
    <!-- 操作栏 -->
    <div class="operation-bar">
      <div class="left-actions">
        <el-button type="primary" @click="handleUpload">
          <el-icon><Upload /></el-icon>
          上传字体
        </el-button>
        <el-button @click="handleRefresh">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
      <div class="right-actions">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索字体名称"
          @input="handleSearch"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 字体列表 -->
    <div class="font-content">
      <el-table
        v-loading="loading"
        :data="filteredFontList"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="字体预览" width="200">
          <template #default="scope">
            <div class="font-preview" :style="{ fontFamily: scope.row.fontFamily }">
              {{ scope.row.previewText }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="字体名称" />
        <el-table-column prop="fontFamily" label="字体族" />
        <el-table-column prop="size" label="文件大小" />
        <el-table-column prop="format" label="格式" />
        <el-table-column prop="uploadTime" label="上传时间" />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button type="text" @click="handlePreview(scope.row)">预览</el-button>
            <el-button type="text" @click="handleDownload(scope.row)">下载</el-button>
            <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="text" @click="handleDelete(scope.row)" style="color: #f56c6c">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 上传对话框 -->
    <el-dialog v-model="uploadDialogVisible" title="上传字体文件" width="50%">
      <el-upload
        ref="uploadRef"
        :action="uploadAction"
        :headers="uploadHeaders"
        :auto-upload="false"
        :on-change="handleFileChange"
        :on-remove="handleRemoveFile"
        :before-remove="beforeRemove"
        :limit="10"
        :on-exceed="handleExceed"
        :file-list="fileList"
        accept=".ttf,.otf,.woff,.woff2"
        multiple
      >
        <el-button type="primary">选择字体文件</el-button>
        <template #tip>
          <div class="el-upload__tip">
            支持 TTF、OTF、WOFF、WOFF2 格式，单个文件不超过 10MB
          </div>
        </template>
      </el-upload>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCancelUpload">取消</el-button>
          <el-button type="primary" @click="handleConfirmUpload">确认上传</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 字体预览对话框 -->
    <el-dialog v-model="previewDialogVisible" title="字体预览" width="60%">
      <div v-if="previewFont" class="font-preview-dialog">
        <div class="preview-header">
          <h3>{{ previewFont.name }}</h3>
          <p>字体族：{{ previewFont.fontFamily }}</p>
        </div>
        <div class="preview-samples">
          <div 
            v-for="sample in previewSamples" 
            :key="sample.size"
            class="preview-sample"
            :style="{ 
              fontFamily: previewFont.fontFamily, 
              fontSize: sample.size + 'px' 
            }"
          >
            <span class="sample-label">{{ sample.size }}px:</span>
            {{ sample.text }}
          </div>
        </div>
        <div class="custom-preview">
          <el-input
            v-model="customPreviewText"
            placeholder="输入自定义预览文本"
            @input="updateCustomPreview"
          />
          <div 
            class="custom-text"
            :style="{ 
              fontFamily: previewFont.fontFamily,
              fontSize: '24px'
            }"
          >
            {{ customPreviewText || '自定义预览文本' }}
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="previewDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="previewFont && handleDownload(previewFont)">下载字体</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 编辑对话框 -->
    <el-dialog v-model="editDialogVisible" title="编辑字体信息" width="40%">
      <el-form v-if="editFont" :model="editFont" label-width="80px">
        <el-form-item label="字体名称">
          <el-input v-model="editFont.name" />
        </el-form-item>
        <el-form-item label="字体族">
          <el-input v-model="editFont.fontFamily" />
        </el-form-item>
        <el-form-item label="预览文本">
          <el-input v-model="editFont.previewText" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="editFont.description" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitEdit">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Upload, Refresh, Search } from '@element-plus/icons-vue'

// 定义组件的事件和props
const emit = defineEmits<{
  upload: [uploadParams: { file: File; MediaMetaData?: any }]
}>()

// 定义props
const props = defineProps<{
  uploadHandler?: (uploadParams: { file: File; MediaMetaData?: any }) => Promise<any>
}>()

// 类型定义
interface FontItem {
  id: number
  name: string
  fontFamily: string
  size: string
  format: string
  uploadTime: string
  previewText: string
  description: string
}

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const selectedFonts = ref<FontItem[]>([])

// 对话框状态
const uploadDialogVisible = ref(false)
const previewDialogVisible = ref(false)
const editDialogVisible = ref(false)

// 字体数据
const fontList = ref<FontItem[]>([
  {
    id: 1,
    name: 'Arial Bold',
    fontFamily: 'Arial, sans-serif',
    size: '2.5MB',
    format: 'TTF',
    uploadTime: '2024-01-15 10:30:00',
    previewText: 'The quick brown fox',
    description: '经典无衬线字体'
  },
  {
    id: 2,
    name: 'Times New Roman',
    fontFamily: 'Times New Roman, serif',
    size: '3.2MB',
    format: 'OTF',
    uploadTime: '2024-01-14 15:20:00',
    previewText: 'The quick brown fox',
    description: '经典衬线字体'
  },
  {
    id: 3,
    name: 'Source Code Pro',
    fontFamily: 'Source Code Pro, monospace',
    size: '1.8MB',
    format: 'WOFF2',
    uploadTime: '2024-01-13 09:15:00',
    previewText: 'The quick brown fox',
    description: '编程等宽字体'
  },
  {
    id: 4,
    name: 'Roboto Regular',
    fontFamily: 'Roboto, sans-serif',
    size: '2.1MB',
    format: 'WOFF',
    uploadTime: '2024-01-12 14:45:00',
    previewText: 'The quick brown fox',
    description: 'Google字体'
  },
  {
    id: 5,
    name: 'Noto Sans CJK',
    fontFamily: 'Noto Sans CJK SC, sans-serif',
    size: '15.6MB',
    format: 'TTF',
    uploadTime: '2024-01-11 11:30:00',
    previewText: '快速的棕色狐狸',
    description: '中文字体'
  }
])

// 预览相关
const previewFont = ref<FontItem | null>(null)
const customPreviewText = ref('')
const previewSamples = [
  { size: 12, text: 'The quick brown fox jumps over the lazy dog' },
  { size: 16, text: 'The quick brown fox jumps over the lazy dog' },
  { size: 20, text: 'The quick brown fox jumps over the lazy dog' },
  { size: 24, text: 'The quick brown fox jumps over the lazy dog' },
  { size: 32, text: 'The quick brown fox' }
]

// 编辑相关
const editFont = ref<FontItem | null>(null)

// 上传相关
const uploadAction = ref('#') // 不使用实际的上传地址
const uploadHeaders = ref({
  Authorization: 'Bearer ' + localStorage.getItem('token')
})
const fileList = ref([])
const uploadFiles = ref<File[]>([]) // 添加上传文件列表

// 计算属性
const filteredFontList = computed(() => {
  let filtered = fontList.value
  if (searchKeyword.value) {
    filtered = filtered.filter(font => 
      font.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      font.fontFamily.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }
  total.value = filtered.length
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filtered.slice(start, end)
})

// 方法
const handleSearch = () => {
  currentPage.value = 1
}

const handleRefresh = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('刷新成功')
  }, 1000)
}

const handleSelectionChange = (selection: FontItem[]) => {
  selectedFonts.value = selection
}

const handleUpload = () => {
  uploadDialogVisible.value = true
}

const handlePreview = (font: FontItem) => {
  previewFont.value = font
  customPreviewText.value = ''
  previewDialogVisible.value = true
}

const handleDownload = (font: FontItem) => {
  ElMessage.success(`正在下载字体：${font.name}`)
  // 实际下载逻辑
}

const handleEdit = (font: FontItem) => {
  editFont.value = { ...font }
  editDialogVisible.value = true
}

const handleDelete = async (font: FontItem) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除字体 "${font.name}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = fontList.value.findIndex(item => item.id === font.id)
    if (index > -1) {
      fontList.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  } catch {
    ElMessage.info('已取消删除')
  }
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

// 上传相关方法
const handleFileChange = (file: any, fileList: any[]) => {
  uploadFiles.value = fileList.map(item => item.raw).filter(Boolean)
}

const handlePreviewFile = (file: any) => {
  console.log('预览文件:', file)
}

const handleRemoveFile = (file: any, fileList: any[]) => {
  uploadFiles.value = fileList.map(item => item.raw).filter(Boolean)
}

const beforeRemove = (file: any) => {
  return ElMessageBox.confirm(`确定移除 ${file.name}？`)
}

const handleExceed = (files: any[], fileList: any[]) => {
  ElMessage.warning(`最多只能选择 10 个文件，当前选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
}

const handleConfirmUpload = async () => {
  if (uploadFiles.value.length === 0) {
    ElMessage.warning('请先选择要上传的字体文件')
    return
  }

  let hasErrors = false
  let successCount = 0

  try {
    // 遍历所有文件，逐个调用父组件的上传方法
    for (const file of uploadFiles.value) {
      try {
        const uploadParams = {
          file,
          MediaMetaData: {
            Title: file.name.split('.').slice(0, -1).join('.'),
            Description: `字体文件: ${file.name}`,
            BusinessType: 'font',
            Tags: 'font,text,media'
          }
        }
        // 使用props中的uploadHandler或者emit事件
        if (props.uploadHandler) {
          await props.uploadHandler(uploadParams)
        } else {
          emit('upload', uploadParams)
        }
        successCount++
      } catch (error) {
        console.error(`文件 ${file.name} 上传失败:`, error)
        hasErrors = true
        ElMessage.error(`文件 ${file.name} 上传失败`)
      }
    }

    if (!hasErrors) {
      ElMessage.success(`成功上传 ${successCount} 个字体文件`)
    } else if (successCount > 0) {
      ElMessage.warning(`成功上传 ${successCount} 个文件，${uploadFiles.value.length - successCount} 个文件上传失败`)
    } else {
      ElMessage.error('所有文件上传失败，请检查文件格式和网络连接')
    }
  } catch (error) {
    console.error('上传过程中发生错误:', error)
    ElMessage.error('上传失败，请重试')
    hasErrors = true
  } finally {
    // 清空文件列表并关闭对话框
    uploadFiles.value = []
    fileList.value = []
    uploadDialogVisible.value = false
  }
}

// 处理取消上传
const handleCancelUpload = () => {
  uploadFiles.value = []
  fileList.value = []
  uploadDialogVisible.value = false
}

const submitUpload = () => {
  // 这个方法现在不会被调用，保留用于兼容性
  ElMessage.success('字体上传成功')
  uploadDialogVisible.value = false
  fileList.value = []
}

// 编辑相关方法
const submitEdit = () => {
  if (!editFont.value) return
  
  const index = fontList.value.findIndex(item => item.id === editFont.value!.id)
  if (index > -1) {
    fontList.value[index] = { ...editFont.value }
    ElMessage.success('编辑成功')
    editDialogVisible.value = false
  }
}

const updateCustomPreview = () => {
  // 实时更新自定义预览
}

// 监听上传成功事件
const handleMediaUploaded = (event: Event) => {
  const customEvent = event as CustomEvent
  const { mediaId, category, fileName, fileInfo, mediaInfo } = customEvent.detail
  if (category === 'font') {
    ElMessage.success(`字体上传成功！文件: ${fileName}，媒资ID: ${mediaId}`)
    // 刷新字体列表
    handleRefresh()
  }
}

// 生命周期
onMounted(() => {
  // 初始化数据
  // 监听上传成功事件
  window.addEventListener('mediaUploaded', handleMediaUploaded as EventListener)
})

onUnmounted(() => {
  // 移除事件监听
  window.removeEventListener('mediaUploaded', handleMediaUploaded as EventListener)
})
</script>

<style scoped>
.font-library {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.operation-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 16px;
}

.left-actions {
  display: flex;
  gap: 12px;
}

.right-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.right-actions .el-input {
  width: 240px;
}

.font-content {
  flex: 1;
  overflow: hidden;
}

.font-preview {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f9f9f9;
  font-size: 14px;
  min-height: 30px;
  display: flex;
  align-items: center;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  padding: 20px 0;
  border-top: 1px solid #e4e7ed;
  margin-top: 16px;
}

/* 预览对话框样式 */
.font-preview-dialog {
  padding: 20px;
}

.preview-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.preview-header h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  color: #303133;
}

.preview-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.preview-samples {
  margin-bottom: 24px;
}

.preview-sample {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  line-height: 1.4;
}

.sample-label {
  display: inline-block;
  width: 60px;
  color: #909399;
  font-size: 12px;
  font-family: Arial, sans-serif;
}

.custom-preview {
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 6px;
}

.custom-preview .el-input {
  margin-bottom: 16px;
}

.custom-text {
  padding: 16px;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  min-height: 60px;
  display: flex;
  align-items: center;
  line-height: 1.4;
}

/* 响应式 */
@media (max-width: 768px) {
  .operation-bar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .left-actions,
  .right-actions {
    justify-content: center;
  }
  
  .right-actions .el-input {
    width: 100%;
  }
}
</style>
