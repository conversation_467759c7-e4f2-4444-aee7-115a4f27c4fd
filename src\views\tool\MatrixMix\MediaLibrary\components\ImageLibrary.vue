<template>
  <div class="image-library">
    <!-- 操作栏 -->
    <div class="toolbar">
      <div class="left-actions">
        <el-button type="primary" @click="showUploadDialog">
          <el-icon><Plus /></el-icon>
          上传图片
        </el-button>
        <el-button @click="batchDelete" :disabled="selectedImages.length === 0">
          <el-icon><Delete /></el-icon>
          批量删除
        </el-button>
      </div>
      <div class="right-actions">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索图片..."
          class="search-input"
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 分类筛选 -->
    <div class="category-filter">
      <el-button-group>
        <el-button 
          :type="selectedCategory === 'all' ? 'primary' : 'default'"
          @click="filterByCategory('all')"
        >
          全部
        </el-button>
        <el-button 
          :type="selectedCategory === 'photo' ? 'primary' : 'default'"
          @click="filterByCategory('photo')"
        >
          照片
        </el-button>
        <el-button 
          :type="selectedCategory === 'illustration' ? 'primary' : 'default'"
          @click="filterByCategory('illustration')"
        >
          插画
        </el-button>
        <el-button 
          :type="selectedCategory === 'icon' ? 'primary' : 'default'"
          @click="filterByCategory('icon')"
        >
          图标
        </el-button>
        <el-button 
          :type="selectedCategory === 'background' ? 'primary' : 'default'"
          @click="filterByCategory('background')"
        >
          背景图
        </el-button>
      </el-button-group>
    </div>

    <!-- 图片网格展示 -->
    <div class="image-grid" v-loading="loading">
      <div 
        v-for="image in filteredImages" 
        :key="image.id"
        class="image-card"
        @click="selectImage(image)"
        :class="{ selected: selectedImages.includes(image.id) }"
      >
        <div class="image-container">
          <img :src="image.thumbnail" :alt="image.name" />
          <div class="image-overlay">
            <el-button 
              type="primary" 
              size="small" 
              @click.stop="previewImage(image)"
            >
              <el-icon><View /></el-icon>
            </el-button>
            <el-button 
              type="danger" 
              size="small" 
              @click.stop="deleteImage(image.id)"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
        </div>
        <div class="image-info">
          <div class="image-name" :title="image.name">{{ image.name }}</div>
          <div class="image-meta">
            <span class="resolution">{{ image.width }}×{{ image.height }}</span>
            <span class="size">{{ formatFileSize(image.size) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[20, 40, 60, 100]"
        :total="totalImages"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 上传对话框 -->
    <el-dialog v-model="uploadDialogVisible" title="上传图片" width="600px">
      <el-upload
        ref="uploadRef"
        class="upload-demo"
        drag
        :action="uploadUrl"
        :headers="uploadHeaders"
        :data="uploadData"
        :auto-upload="false"
        multiple
        :file-list="fileList"
        :on-change="handleFileChange"
        :before-upload="beforeUpload"
        :on-error="handleUploadError"
        accept=".jpg,.jpeg,.png,.gif,.webp,.svg"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将图片拖拽到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            支持 JPG/PNG/GIF/WebP/SVG 格式，单个文件不超过 10MB
          </div>
        </template>
      </el-upload>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="uploadDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmUpload">确认上传</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog v-model="previewDialogVisible" title="图片预览" width="80%">
      <div class="image-preview" v-if="previewImageData">
        <img :src="previewImageData.url" :alt="previewImageData.name" />
        <div class="preview-info">
          <h3>{{ previewImageData.name }}</h3>
          <p>分辨率: {{ previewImageData.width }}×{{ previewImageData.height }}</p>
          <p>文件大小: {{ formatFileSize(previewImageData.size) }}</p>
          <p>格式: {{ previewImageData.format }}</p>
          <p>上传时间: {{ formatDateTime(previewImageData.uploadTime) }}</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Delete, Search, View, UploadFilled } from '@element-plus/icons-vue'

// 定义组件的事件
const emit = defineEmits<{
  upload: [uploadParams: { file: File; MediaMetaData?: any }]
}>()

// 数据定义
const loading = ref(false)
const searchKeyword = ref('')
const selectedCategory = ref('all')
const selectedImages = ref<number[]>([])
const currentPage = ref(1)
const pageSize = ref(20)
const totalImages = ref(0)

// 修复：mockImages 未定义导致页面报错
const mockImages = ref<any[]>([])

// 对话框控制
const uploadDialogVisible = ref(false)
const previewDialogVisible = ref(false)
const previewImageData = ref<any>(null)

// 上传相关
const uploadRef = ref()
const fileList = ref([])
const uploadUrl = ref('#') // 不使用实际的上传地址
const uploadHeaders = ref({
  'Authorization': `Bearer ${localStorage.getItem('token')}`
})
const uploadData = ref({
  type: 'image'
})
const uploadFiles = ref<File[]>([]) // 添加上传文件列表


// 计算属性
const filteredImages = computed(() => {
  let result = mockImages.value || []

  // 分类筛选
  if (selectedCategory.value !== 'all') {
    result = result.filter(image => image.category === selectedCategory.value)
  }

  // 搜索筛选
  if (searchKeyword.value) {
    result = result.filter(image => 
      image.name && image.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }

  totalImages.value = result.length

  // 分页
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return result.slice(start, end)
})

// 方法定义
const showUploadDialog = () => {
  uploadDialogVisible.value = true
}

const selectImage = (image: any) => {
  const index = selectedImages.value.indexOf(image.id)
  if (index > -1) {
    selectedImages.value.splice(index, 1)
  } else {
    selectedImages.value.push(image.id)
  }
}

const previewImage = (image: any) => {
  previewImageData.value = image
  previewDialogVisible.value = true
}

const deleteImage = async (imageId: number) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这张图片吗？删除后无法恢复。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    // 这里应该调用删除API
    mockImages.value = mockImages.value.filter(image => image.id !== imageId)
    ElMessage.success('删除成功')
  } catch {
    // 用户取消删除
  }
}

const batchDelete = async () => {
  if (selectedImages.value.length === 0) return
  
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedImages.value.length} 张图片吗？删除后无法恢复。`,
      '批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    // 这里应该调用批量删除API
    mockImages.value = mockImages.value.filter(image => !selectedImages.value.includes(image.id))
    selectedImages.value = []
    ElMessage.success('批量删除成功')
  } catch {
    // 用户取消删除
  }
}

const filterByCategory = (category: string) => {
  selectedCategory.value = category
  currentPage.value = 1
}

const handleSearch = () => {
  currentPage.value = 1
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}

const beforeUpload = (file: File) => {
  const isValidType = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'].includes(file.type)
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isValidType) {
    ElMessage.error('只能上传 JPG/PNG/GIF/WebP/SVG 格式的图片!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('上传图片大小不能超过 10MB!')
    return false
  }
  return true
}

const handleFileChange = (file: any, fileList: any[]) => {
  uploadFiles.value = fileList.map(item => item.raw).filter(Boolean)
}

const handleConfirmUpload = () => {
  if (uploadFiles.value.length === 0) {
    ElMessage.warning('请先选择要上传的图片')
    return
  }

  // 遍历所有文件，逐个调用父组件的上传方法
  uploadFiles.value.forEach(file => {
    const uploadParams = {
      file,
      MediaMetaData: {
        Title: file.name.split('.').slice(0, -1).join('.'),
        Description: `图片文件: ${file.name}`,
        BusinessType: 'image',
        Tags: 'image,picture,media'
      }
    }
    emit('upload', uploadParams)
  })

  // 清空文件列表并关闭对话框
  uploadFiles.value = []
  uploadDialogVisible.value = false
}

const handleUploadSuccess = (response: any, file: any) => {
  ElMessage.success('上传成功')
  // 这里应该刷新图片列表
  loadImages()
}

const handleUploadError = (error: any) => {
  ElMessage.error('上传失败')
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const loadImages = () => {
  // 这里应该调用API加载图片列表
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 500)
}

// 监听上传成功事件
const handleMediaUploaded = (event: Event) => {
  const customEvent = event as CustomEvent
  const { mediaId, category, fileName, fileInfo, mediaInfo } = customEvent.detail
  if (category === 'image') {
    ElMessage.success(`图片上传成功！文件: ${fileName}，媒资ID: ${mediaId}`)
    // 刷新图片列表
    loadImages()
  }
}

onMounted(() => {
  loadImages()
  // 监听上传成功事件
  window.addEventListener('mediaUploaded', handleMediaUploaded as EventListener)
})

onUnmounted(() => {
  // 移除事件监听
  window.removeEventListener('mediaUploaded', handleMediaUploaded as EventListener)
})
</script>

<style scoped>
.image-library {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.left-actions {
  display: flex;
  gap: 12px;
}

.search-input {
  width: 300px;
}

.category-filter {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.image-grid {
  flex: 1;
  padding: 16px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  overflow-y: auto;
}

.image-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s;
  background: white;
}

.image-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.image-card.selected {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.image-container {
  position: relative;
  aspect-ratio: 4/3;
  overflow: hidden;
}

.image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.3s;
}

.image-container:hover .image-overlay {
  opacity: 1;
}

.image-info {
  padding: 12px;
}

.image-name {
  font-weight: 500;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.image-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
}

.pagination-wrapper {
  padding: 16px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #e4e7ed;
}

.upload-demo {
  width: 100%;
}

.image-preview {
  text-align: center;
}

.image-preview img {
  max-width: 100%;
  max-height: 60vh;
  object-fit: contain;
}

.preview-info {
  margin-top: 20px;
  text-align: left;
}

.preview-info h3 {
  margin: 0 0 10px 0;
}

.preview-info p {
  margin: 5px 0;
  color: #666;
}
</style>
