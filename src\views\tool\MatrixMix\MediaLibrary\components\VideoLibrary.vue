<template>
  <div class="video-library">
    <!-- 操作栏 -->
    <div class="toolbar">
      <div class="left-actions">
        <el-button type="primary" @click="handleUpload">
          <el-icon><Plus /></el-icon>
          上传视频
        </el-button>
        <el-button type="danger" @click="handleDelete" :disabled="selectedItems.length === 0">
          <el-icon><Delete /></el-icon>
          删除
        </el-button>
        <el-button @click="handleRefresh">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
      
      <div class="right-actions">
        <!-- 筛选业务类型 -->
        <el-select v-model="businessType" placeholder="业务类型" style="width: 120px; margin-right: 10px;">
          <el-option v-for="item in businessTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <!-- 筛选来源 -->
        <el-select v-model="source" placeholder="来源" style="width: 120px; margin-right: 10px;">
          <el-option v-for="item in sourceOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <!-- 筛选资源状态 -->
        <el-select v-model="status" placeholder="资源状态" style="width: 120px; margin-right: 10px;">
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <!-- 日期筛选 -->
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 240px; margin-right: 10px;"
          @change="handleDateChange"
        />
        <!-- 排序 -->
        <el-select v-model="sortBy" placeholder="排序" style="width: 120px;" >
          <el-option label="升序" value="asc" />
          <el-option label="降序" value="desc" />
        </el-select>
        <el-button type="primary" @click="handleFilter">筛选</el-button>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-wrapper">

      <!-- 右侧视频列表 -->
      <div class="video-panel">
        <div v-if="loading" class="loading-container">
          <el-icon class="is-loading"><Loading /></el-icon>
          <span>加载中...</span>
        </div>
        
        <div v-else-if="mediaList.length === 0" class="empty-state">
          <el-empty description="暂无数据">
            <el-button type="primary" @click="handleUpload">上传视频</el-button>
          </el-empty>
        </div>

        <div v-else class="video-grid">
          <div v-for="video in mediaList" 
               :key="video.MediaId" 
               :class="['video-item', { selected: selectedItems.includes(video.MediaId) }]"
               @click="toggleSelect(video.MediaId)">
            <div class="video-thumbnail">
              <img :src="getPreviewImage(video)" :alt="video.MediaBasicInfo.Title" />
              <div class="video-overlay">
                <el-icon class="play-icon"><VideoPlay /></el-icon>
              </div>
              <!-- 展示时长（如有） -->
              <div v-if="video.FileInfoList?.[0]?.FileBasicInfo?.Duration" class="duration">
                {{ formatDuration(parseFloat(video.FileInfoList[0].FileBasicInfo.Duration) || 0) }}
              </div>
            </div>
            <div class="video-info">
              <h4 class="video-title">{{ video.MediaBasicInfo.Title }}</h4>
              <p class="video-meta">
                来源：{{ SourceMap[video.MediaBasicInfo.Source] || video.MediaBasicInfo.Source }}
                | 业务类型：{{ BusinessTypeMap[video.MediaBasicInfo.BusinessType] || video.MediaBasicInfo.BusinessType }}
                | 状态：{{ StatusMap[video.MediaBasicInfo.Status] || video.MediaBasicInfo.Status }}
              </p>
              <p class="video-meta">
                创建时间：{{ formatDate(video.MediaBasicInfo.CreateTime) }}
              </p>
              <p v-if="video.MediaBasicInfo.Description" class="video-description">
                {{ video.MediaBasicInfo.Description }}
              </p>
            </div>
            <div class="video-actions">
              <el-button size="small" type="primary" @click.stop="handlePreview(video)">预览</el-button>
              <el-button size="small" @click.stop="handleEdit(video)">编辑</el-button>
              <el-button size="small" type="danger" @click.stop="handleDeleteSingle(video.MediaId)">删除</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 上传对话框 -->
    <el-dialog v-model="uploadDialogVisible" title="上传视频" width="600px">
      <el-upload
        ref="uploadRef"
        class="upload-demo"
        drag
        :action="uploadAction"
        :auto-upload="false"
        multiple
        :on-change="handleFileChange"
        :before-upload="beforeUpload"
        :on-error="handleUploadError"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            支持 mp4, avi, mov 格式，单个文件不超过 100MB
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancelUpload">取消</el-button>
          <el-button type="primary" @click="handleConfirmUpload">确认上传</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
// 获取视频预览图（优先级：CoverURL > Snapshots > SpriteImages > 占位图）
function getPreviewImage(video: MediaInfo): string {
  const info = video.MediaBasicInfo
  // 1. CoverURL
  if (info.CoverURL) return info.CoverURL
  // 2. Snapshots（可能为字符串或数组）
  if (info.Snapshots) {
    try {
      if (Array.isArray(info.Snapshots)) {
        if (info.Snapshots.length > 0) return info.Snapshots[0]
      } else if (typeof info.Snapshots === 'string') {
        // 可能是逗号分隔或JSON数组
        if (info.Snapshots.startsWith('[')) {
          const arr = JSON.parse(info.Snapshots)
          if (Array.isArray(arr) && arr.length > 0) return arr[0]
        } else if (info.Snapshots.includes(',')) {
          return info.Snapshots.split(',')[0]
        } else {
          return info.Snapshots
        }
      }
    } catch {}
  }
  // 3. SpriteImages（通常为图片地址或JSON）
  if (info.SpriteImages) {
    try {
      if (typeof info.SpriteImages === 'string') {
        if (info.SpriteImages.startsWith('[')) {
          const arr = JSON.parse(info.SpriteImages)
          if (Array.isArray(arr) && arr.length > 0) return arr[0]
        } else if (info.SpriteImages.includes(',')) {
          return info.SpriteImages.split(',')[0]
        } else {
          return info.SpriteImages
        }
      }
    } catch {}
  }
  // 4. 占位图
  return '/placeholder-video.jpg'
}
import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, 
  FolderAdd, 
  Delete, 
  Refresh, 
  Search, 
  Loading, 
  VideoPlay,
  UploadFilled
} from '@element-plus/icons-vue'
import { listMediaBasicInfo } from '../../api/media';
import type { MediaListQueryParams, MediaInfo } from '../../types/media';
import { formatFileSize, formatDuration } from '../../utils/commonUtils'
import { BusinessTypeMap, StatusMap, SourceMap, mapToOptions } from '../../types/media'

// 定义组件的事件和props
const emit = defineEmits<{
    upload: [uploadParams: { file: File; MediaMetaData?: any }],
    refreshMediaList: [page?: number]
}>()
const callParentRefreshMediaList = (page = 1) => {
  emit('refreshMediaList', page)
}
// 定义props
const props = defineProps<{
  mediaResponse?: any
  uploadHandler?: (uploadParams: { file: File; MediaMetaData?: any }) => Promise<any>
}>()

// 响应式数据
const loading = ref(false)
const selectedItems = ref<string[]>([])
const uploadDialogVisible = ref(false)
const uploadFiles = ref<File[]>([])
const uploadRef = ref()

// 新增筛选相关变量
const businessType = ref('')
const source = ref('')
const status = ref('')
const dateRange = ref<[Date, Date] | null>(null)

// 下拉选项（通过 mapToOptions 工具函数生成，确保类型和数据一致）
const businessTypeOptions = mapToOptions(BusinessTypeMap)
const sourceOptions = mapToOptions(SourceMap)
const statusOptions = mapToOptions(StatusMap)
// 日期筛选处理
const handleDateChange = (val: [Date, Date] | null) => {
  if (val && val.length === 2) {
    startTime.value = val[0].toISOString()
    endTime.value = val[1].toISOString()
  } else {
    startTime.value = ''
    endTime.value = ''
  }
}

// 筛选按钮处理
const handleFilter = () => {
  // 触发父组件刷新并传递筛选参数
  callParentRefreshMediaList(1)
}

// 媒资列表数据
const mediaList = ref<MediaInfo[]>([]);
const total = ref(0);




// 查询参数
const queryParams = ref<MediaListQueryParams>({
  MediaType: 'video',
  SortBy: 'desc',
  IncludeFileBasicInfo: true
});

// 查询筛选字段
const startTime = ref<string>('')
const endTime = ref<string>('')
// 媒体类型和最大结果数与分页和tab绑定
const mediaType = ref<string>('video')
const sortBy = ref<string>('desc')

// 搜索关键词
const searchKeyword = ref('')



const formatDate = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

const uploadAction = computed(() => {
  return '#' // 不使用实际的上传地址
})

// 方法
const handleUpload = () => {
  uploadDialogVisible.value = true
}

const handleDelete = async () => {
  try {
    await ElMessageBox.confirm('确定要删除选中的视频吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    // 执行删除操作
    ElMessage.success('删除成功')
    selectedItems.value = []
  } catch {
    ElMessage.info('已取消删除')
  }
}

const handleRefresh = () => {
  callParentRefreshMediaList()
}

const toggleSelect = (videoId: string) => {
  const index = selectedItems.value.indexOf(videoId)
  if (index > -1) {
    selectedItems.value.splice(index, 1)
  } else {
    selectedItems.value.push(videoId)
  }
}

const handlePreview = (video: any) => {
  ElMessage.info(`预览视频: ${video.MediaBasicInfo.Title}`)
}

const handleEdit = (video: any) => {
  ElMessage.info(`编辑视频: ${video.MediaBasicInfo.Title}`)
}

const handleDeleteSingle = async (videoId: string) => {
  try {
    await ElMessageBox.confirm('确定要删除这个视频吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    ElMessage.success('删除成功')
  } catch {
    ElMessage.info('已取消删除')
  }
}

const beforeUpload = (file: File) => {
  const isVideo = file.type.startsWith('video/')
  const isLt100M = file.size / 1024 / 1024 < 100

  if (!isVideo) {
    ElMessage.error('只能上传视频文件!')
    return false
  }
  if (!isLt100M) {
    ElMessage.error('上传文件大小不能超过 100MB!')
    return false
  }
  return true
}

const handleFileChange = (file: any, fileList: any[]) => {
  uploadFiles.value = fileList.map(item => item.raw).filter(Boolean)
}

const handleConfirmUpload = async () => {
  if (uploadFiles.value.length === 0) {
    ElMessage.warning('请先选择要上传的视频文件')
    return
  }
  let hasErrors = false
  try {
    // 遍历所有文件，逐个调用父组件的上传方法
    for (const file of uploadFiles.value) {
      try {
        const uploadParams = {
          file,
          MediaMetaData: {
            Title: file.name.split('.').slice(0, -1).join('.'),
            Description: `视频文件: ${file.name}`,
            BusinessType: 'video',
            Tags: 'video,media'
          }
        }
        // 使用props中的uploadHandler或者emit事件
        if (props.uploadHandler) {
          await props.uploadHandler(uploadParams)
        } else {
          emit('upload', uploadParams)
        }
      } catch (error) {
        console.error(`文件 ${file.name} 上传失败:`, error)
        hasErrors = true
        ElMessage.error(`文件 ${file.name} 上传失败`)
      }
    }

    if (!hasErrors) {
      ElMessage.success(`成功上传 ${uploadFiles.value.length} 个文件`)
    } else {
      ElMessage.warning('部分文件上传失败，请检查文件格式和网络连接')
    }
  } catch (error) {
    console.error('上传过程中发生错误:', error)
    ElMessage.error('上传失败，请重试')
    hasErrors = true
  } finally {
    // 清空文件列表并关闭对话框
    uploadFiles.value = []
    // 清空Upload组件的文件列表
    if (uploadRef.value) {
      uploadRef.value.clearFiles()
    }
    uploadDialogVisible.value = false
  }
}

const handleUploadError = (error: any, file: any) => {
  console.error('Upload组件上传错误:', error, file)
  ElMessage.error(`文件 ${file.name} 上传失败`)
  // 从文件列表中移除失败的文件
  const index = uploadFiles.value.findIndex(f => f.name === file.name)
  if (index > -1) {
    uploadFiles.value.splice(index, 1)
  }
}

// 处理取消上传
const handleCancelUpload = () => {
  uploadFiles.value = []
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
  uploadDialogVisible.value = false
}

// 监听上传成功事件
const handleMediaUploaded = (event: Event) => {
  const customEvent = event as CustomEvent
  const { mediaId, category, fileName, fileInfo, mediaInfo } = customEvent.detail
  if (category === 'video') {
    ElMessage.success(`视频上传成功！文件: ${fileName}，媒资ID: ${mediaId}`)
    callParentRefreshMediaList()
  }
}


watch(
  () => props.mediaResponse,
  (val) => {
    if (val && Array.isArray(val.MediaInfos)) {
      mediaList.value = val.MediaInfos
    } else {
      mediaList.value = []
    }
  },
  { immediate: true }
)

// 初始化
onMounted(() => {
  callParentRefreshMediaList();
  // 监听上传成功事件
  window.addEventListener('mediaUploaded', handleMediaUploaded as EventListener)
})

onUnmounted(() => {
  // 移除事件监听
  window.removeEventListener('mediaUploaded', handleMediaUploaded as EventListener)
})
</script>

<style lang="scss" scoped>
.video-library {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .left-actions {
    display: flex;
    gap: 10px;
  }

  .right-actions {
    display: flex;
    align-items: center;
    gap: 10px;
  }
}

.content-wrapper {
  display: flex;
  flex: 1;
  gap: 20px;
  min-height: 0;
}


.video-panel {
  flex: 1;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  display: flex;
  flex-direction: column;

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    gap: 10px;
    color: #666;

    .el-icon {
      font-size: 32px;
    }
  }

  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
  }

  .video-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    flex: 1;
    overflow-y: auto;

    .video-item {
      border: 2px solid transparent;
      border-radius: 8px;
      overflow: hidden;
      cursor: pointer;
      transition: all 0.3s;
      background: #f8f9fa;

      &:hover {
        border-color: #409eff;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      &.selected {
        border-color: #409eff;
        background: #ecf5ff;
      }

      .video-thumbnail {
        position: relative;
        width: 100%;
        height: 120px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .video-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.3);
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s;

          .play-icon {
            font-size: 32px;
            color: white;
          }
        }

        &:hover .video-overlay {
          opacity: 1;
        }

        .duration {
          position: absolute;
          bottom: 8px;
          right: 8px;
          background: rgba(0, 0, 0, 0.7);
          color: white;
          padding: 2px 6px;
          border-radius: 4px;
          font-size: 12px;
        }
      }

      .video-info {
        padding: 10px;

        .video-title {
          margin: 0 0 5px 0;
          font-size: 14px;
          font-weight: 500;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .video-meta {
          margin: 0;
          font-size: 12px;
          color: #666;
        }
      }

      .video-actions {
        padding: 10px;
        border-top: 1px solid #eee;
        display: flex;
        gap: 5px;

        .el-button {
          flex: 1;
        }
      }
    }
  }
}

.upload-demo {
  .el-icon--upload {
    font-size: 48px;
    color: #409eff;
  }
}
</style>
