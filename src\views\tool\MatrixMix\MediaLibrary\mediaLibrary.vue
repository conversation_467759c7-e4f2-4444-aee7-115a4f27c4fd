<template>
  <div class="media-library">
    <!-- 顶部标签栏 -->
    <div class="header-tabs">
      <div class="tab-item" 
           :class="{ active: activeTab === 'video' }" 
           @click="switchTab('video')">
        <el-icon><VideoPlay /></el-icon>
        <span>视频库(多媒体)</span>
      </div>
      <div class="tab-item" 
           :class="{ active: activeTab === 'audio' }" 
           @click="switchTab('audio')">
        <el-icon><Microphone /></el-icon>
        <span>音频库</span>
      </div>
      <div class="tab-item" 
           :class="{ active: activeTab === 'image' }" 
           @click="switchTab('image')">
        <el-icon><Picture /></el-icon>
        <span>图片素材库</span>
      </div>
      <div class="tab-item" 
           :class="{ active: activeTab === 'font' }" 
           @click="switchTab('font')">
        <el-icon><EditPen /></el-icon>
        <span>字体素材库</span>
      </div>
    </div>

    <!-- 内容区 -->
    <div class="content-area">
      <component 
        :is="currentComponent" 
        :key="activeTab"
        :upload-handler="handleUpload"
        :media-response="data"
        @upload="handleUpload"
        @refreshMediaList="loadMediaList"
      />
    </div>
            <!-- 分页 -->
        <div class="pagination-wrapper">
                      <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[12, 24, 48, 96]"
              :total="total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handlePageChange"
            />
        </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { VideoPlay, Microphone, Picture, EditPen } from '@element-plus/icons-vue'
import VideoLibrary from './components/VideoLibrary.vue'
import AudioLibrary from './components/AudioLibrary.vue'
import MusicLibrary from './components/MusicLibrary.vue'
import ImageLibrary from './components/ImageLibrary.vue'
import FontLibrary from './components/FontLibrary.vue'
import { uploadAndRegisterMedia,listMediaBasicInfo } from '../api/media'
import type { MediaListQueryParams, MediaInfo } from '../types/media'
import { detectFileCategory } from '../utils/fileUtils'
import { MediaType } from '../types/media'

// 当前活跃的标签页
const activeTab = ref<string>('video')

// 响应时数据
const data = ref<any>(null)
const loading = ref(false)
const mediaList = ref<MediaInfo[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(12)

// 查询筛选字段
const startTime = ref<string>('')
const endTime = ref<string>('')
const mediaType = ref<MediaType>(MediaType.VIDEO)
const maxResults = ref<number>(12)
const nextToken = ref<string>('')
const sortBy = ref<'desc' | 'asc'>('desc')

// 动态组件映射
const componentMap = {
  video: VideoLibrary,
  audio: AudioLibrary,
  image: ImageLibrary,
  font: FontLibrary
}

// 当前组件
const currentComponent = computed(() => componentMap[activeTab.value as keyof typeof componentMap])

// 切换标签页
const switchTab = (tab: string) => {
  activeTab.value = tab
}

// 统一上传逻辑 - 新的一体化上传流程
const handleUpload = async (uploadParams: any) => {
  try {
    // 智能确定分类
    const category = detectFileCategory(uploadParams.file,activeTab.value)
    const currentTab = activeTab.value
    
    // 准备元数据
    const metadata = {
      title: uploadParams.MediaMetaData?.Title || uploadParams.file.name.split('.').slice(0, -1).join('.'),
      description: uploadParams.MediaMetaData?.Description || `${category}文件: ${uploadParams.file.name}`,
      tags: uploadParams.MediaMetaData?.Tags || `${category},media`,
      businessType: uploadParams.MediaMetaData?.BusinessType || category
    }
    
    // 如果检测到的文件类型与当前标签页不匹配，给出友好提示
    if (category !== currentTab) {
      const categoryNames = {
        video: '视频',
        audio: '音频',
        music: '音乐',
        image: '图片',
        font: '字体',
        text: '文本'
      }
      const detectedName = categoryNames[category as keyof typeof categoryNames] || category
      const currentName = categoryNames[currentTab as keyof typeof categoryNames] || currentTab
      
      ElMessage.info(`检测到${detectedName}文件，将按${detectedName}类型上传（当前在${currentName}标签页）`)
    }
    
    // 调用一体化上传接口
    const result = await uploadAndRegisterMedia(uploadParams.file, category, metadata)
    
    console.log('一体化上传成功:', result)
    console.log('响应结构分析:', {
      type: typeof result,
      hasData: 'data' in result,
      dataType: typeof result.data,
      keys: Object.keys(result),
      dataKeys: result.data ? Object.keys(result.data) : '无data字段'
    })
    
    // 安全获取MediaId，兼容不同的响应格式
    const mediaId = result?.data?.MediaId || (result as any)?.MediaId
    const requestId = result?.data?.RequestId || (result as any)?.RequestId
    
    if (!mediaId) {
      console.error('❌ 响应中未找到MediaId:', result)
      throw new Error('上传成功但未返回媒资ID，请检查后端响应格式')
    }
    
    ElMessage.success(`上传成功！文件: ${uploadParams.file.name}，媒资ID: ${mediaId}`)
    
    // 发出刷新事件，通知子组件刷新列表
    const event = new CustomEvent('mediaUploaded', {
      detail: {
        mediaId: mediaId,
        category: category,
        fileName: uploadParams.file.name,
        fileInfo: {
          fileName: result.fileName || uploadParams.file.name,
          fileSize: result.fileSize || uploadParams.file.size,
          ossUrl: result.ossUrl || '',
          filePath: result.filePath || ''
        },
        mediaInfo: {
          mediaId: mediaId,
          requestId: requestId || '',
          category: result.category || category
        }
      }
    })
    window.dispatchEvent(event)
    return result
  } catch (error: any) {
    console.error('一体化上传错误:', error)
    const errorMessage = error.response?.data?.msg || error.message || '上传失败，请重试'
    ElMessage.error(`上传失败: ${errorMessage}`)
    throw error
  }
}

// 获取媒资列表
const loadMediaList = async (page = 1) => {
  loading.value = true
  try {
    const query: MediaListQueryParams = {
      MediaType: mediaType.value,
      MaxResults: maxResults.value,
      NextToken: nextToken.value,
      SortBy: sortBy.value,
      StartTime: startTime.value,
      EndTime: endTime.value,
      IncludeFileBasicInfo:true
      // 其它筛选字段
    }
    const response = await listMediaBasicInfo(query)
    data.value = response
    console.log('分页总数:', total.value, '接口返回:', response.TotalCount)
    mediaList.value = response.MediaInfos
    total.value = response.TotalCount
    nextToken.value = response.NextToken || ''
    currentPage.value = page
  } finally {
    loading.value = false
  }
}

const searchMedia = async (keyword: string) => {
  await loadMediaList(1)
  if (keyword.trim()) {
    mediaList.value = mediaList.value.filter(media =>
      media.MediaBasicInfo.Title.toLowerCase().includes(keyword.toLowerCase())
    )
    total.value = mediaList.value.length // 本地过滤后也要赋值
  }
}

// 分页处理
const handlePageChange = (page: number) => {
  loadMediaList(page)
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadMediaList(1)
}


</script>

<style lang="scss" scoped>
.media-library {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
    .pagination-wrapper {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
}

.header-tabs {
  display: flex;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  .tab-item {
    display: flex;
    align-items: center;
    padding: 15px 25px;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    border-bottom: 3px solid transparent;
    white-space: nowrap;
    position: relative;

    .el-icon {
      margin-right: 8px;
      font-size: 16px;
    }

    span {
      font-weight: 400;
    }

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
      color: #ffffff;
    }

    &.active {
      background-color: rgba(255, 255, 255, 0.15);
      color: #ffffff;
      border-bottom-color: #ffffff;
      font-weight: 500;

      span {
        font-weight: 500;
      }
    }

    &:not(:last-child) {
      border-right: 1px solid rgba(255, 255, 255, 0.1);
    }
  }
}

.content-area {
  flex: 1;
  background-color: #ffffff;
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-tabs {
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;

    .tab-item {
      flex-shrink: 0;
      padding: 12px 20px;
      font-size: 13px;

      .el-icon {
        margin-right: 6px;
        font-size: 14px;
      }
    }
  }
}

@media (max-width: 480px) {
  .header-tabs {
    .tab-item {
      padding: 10px 15px;
      font-size: 12px;

      span {
        display: none;
      }

      .el-icon {
        margin-right: 0;
        font-size: 18px;
      }
    }
  }
}
</style>
