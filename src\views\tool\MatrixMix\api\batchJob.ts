import request from '@/utils/request';
import type {
  SubmitBatchMediaProducingJobRequest,
  SubmitBatchMediaProducingJobResponse,
  InputConfig,
  EditingConfig,
  OutputConfig,
  UserData,
  TemplateConfig
} from '../types/batchProducing';

// ==================== 工具函数 ====================

/**
 * 生成客户端Token，用于保证请求幂等性
 * @returns 客户端Token
 */
export function generateClientToken(): string {
  return `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * 批量成片任务信息
 */
export interface BatchMediaProducingJob {
  jobId: string;
  jobType: 'Script' | 'Smart_Mix';
  status: 'Finished' | 'Init' | 'Failed' | 'Processing';
  createTime: string;
  updateTime: string;
  startTime?: string;
  finishTime?: string;
  progress?: number;
  errorMessage?: string;
}

/**
 * 批量成片任务详情
 */
export interface BatchMediaProducingJobDetail extends BatchMediaProducingJob {
  outputCount: number;
  maxDuration: number;
  resolution: string;
  quality: number;
  mediaGroupCount: number;
  textCount: number;
  processedCount: number;
  totalCount: number;
  estimatedTime?: string;
  outputFiles?: Array<{
    index: number;
    fileName: string;
    fileSize: string;
    duration: number;
    url: string;
  }>;
}

/**
 * 查询批量成片任务列表的请求参数
 */
export interface ListBatchMediaProducingJobsRequest {
  jobId?: string;
  jobType?: 'Script' | 'Smart_Mix';
  status?: 'Finished' | 'Init' | 'Failed' | 'Processing';
  startTime?: string;
  endTime?: string;
  sortBy?: 'asc' | 'desc';
  nextToken?: string;
  maxResults?: number;
}

/**
 * 查询批量成片任务列表的响应
 */
export interface ListBatchMediaProducingJobsResponse {
  RequestId: string;
  EditingBatchJobList: BatchMediaProducingJob[];
  NextToken: string;
  MaxResults: number;
}

/**
 * 获取批量成片任务详情的响应
 */
export interface GetBatchMediaProducingJobResponse {
  RequestId: string;
  Job: BatchMediaProducingJobDetail;
}

/**
 * 提交批量智能一键成片任务
 * 
 * @param params 提交参数
 * @returns 返回任务ID
 */
export function submitBatchMediaProducingJob(
  params: SubmitBatchMediaProducingJobRequest
): Promise<SubmitBatchMediaProducingJobResponse> {
  // 参数验证
  if (!params.InputConfig) {
    throw new Error('InputConfig is required');
  }
  if (!params.OutputConfig) {
    throw new Error('OutputConfig is required');
  }

  // 如果没有提供ClientToken，自动生成一个
  const requestParams: SubmitBatchMediaProducingJobRequest = {
    ...params,
    ClientToken: params.ClientToken || generateClientToken()
  };

  return request<{
    code: number;
    data: SubmitBatchMediaProducingJobResponse;
    msg: string;
  }>({
    url: '/video/batch-producing/submit',
    method: 'post',
    data: requestParams
  }).then(res => {
    if (res.data.code === 200) {
      return res.data.data;
    } else {
      throw new Error(res.data.msg || '提交任务失败');
    }
  }).catch(error => {
    console.error('❌ 提交批量成片任务失败:', error);
    throw error;
  });
}

/**
 * 查询批量智能一键成片任务列表
 * 
 * @param params 查询参数
 * @returns 返回任务列表
 */
export function listBatchMediaProducingJobs(
  params: ListBatchMediaProducingJobsRequest = {}
): Promise<ListBatchMediaProducingJobsResponse> {
  return request<{
    code: number;
    data: ListBatchMediaProducingJobsResponse;
    msg: string;
  }>({
    url: '/video/batch-producing/list',
    method: 'get',
    params
  }).then(res => {
    if (res.data.code === 200) {
      return res.data.data;
    } else {
      throw new Error(res.data.msg || '查询任务列表失败');
    }
  }).catch(error => {
    console.error('❌ 查询批量成片任务列表失败:', error);
    throw error;
  });
}

/**
 * 获取批量成片任务详情
 * 
 * @param jobId 任务ID
 * @returns 返回任务详情
 */
export function getBatchMediaProducingJob(
  jobId: string
): Promise<GetBatchMediaProducingJobResponse> {
  if (!jobId) {
    throw new Error('JobId is required');
  }

  return request<{
    code: number;
    data: GetBatchMediaProducingJobResponse;
    msg: string;
  }>({
    url: `/video/batch-producing/${jobId}`,
    method: 'get'
  }).then(res => {
    if (res.data.code === 200) {
      return res.data.data;
    } else {
      throw new Error(res.data.msg || '获取任务详情失败');
    }
  }).catch(error => {
    console.error('❌ 获取批量成片任务详情失败:', error);
    throw error;
  });
}

/**
 * 删除批量成片任务
 * 
 * @param jobId 任务ID
 * @returns 删除结果
 */
export function deleteBatchMediaProducingJob(
  jobId: string
): Promise<{ RequestId: string }> {
  if (!jobId) {
    throw new Error('JobId is required');
  }

  return request<{
    code: number;
    data: { RequestId: string };
    msg: string;
  }>({
    url: `/video/batch-producing/${jobId}`,
    method: 'delete'
  }).then(res => {
    if (res.data.code === 200) {
      return res.data.data;
    } else {
      throw new Error(res.data.msg || '删除任务失败');
    }
  }).catch(error => {
    console.error('❌ 删除批量成片任务失败:', error);
    throw error;
  });
}

/**
 * 重试失败的批量成片任务
 * 
 * @param jobId 任务ID
 * @returns 重试结果
 */
export function retryBatchMediaProducingJob(
  jobId: string
): Promise<{ RequestId: string; JobId: string }> {
  if (!jobId) {
    throw new Error('JobId is required');
  }

  return request<{
    code: number;
    data: { RequestId: string; JobId: string };
    msg: string;
  }>({
    url: `/video/batch-producing/${jobId}/retry`,
    method: 'post'
  }).then(res => {
    if (res.data.code === 200) {
      return res.data.data;
    } else {
      throw new Error(res.data.msg || '重试任务失败');
    }
  }).catch(error => {
    console.error('❌ 重试批量成片任务失败:', error);
    throw error;
  });
}

/**
 * 取消正在处理的批量成片任务
 * 
 * @param jobId 任务ID
 * @returns 取消结果
 */
export function cancelBatchMediaProducingJob(
  jobId: string
): Promise<{ RequestId: string }> {
  if (!jobId) {
    throw new Error('JobId is required');
  }

  return request<{
    code: number;
    data: { RequestId: string };
    msg: string;
  }>({
    url: `/video/batch-producing/${jobId}/cancel`,
    method: 'post'
  }).then(res => {
    if (res.data.code === 200) {
      return res.data.data;
    } else {
      throw new Error(res.data.msg || '取消任务失败');
    }
  }).catch(error => {
    console.error('❌ 取消批量成片任务失败:', error);
    throw error;
  });
}

/**
 * 获取任务统计信息
 * 
 * @returns 统计信息
 */
export function getBatchMediaProducingStats(): Promise<{
  totalJobs: number;
  finishedJobs: number;
  processingJobs: number;
  failedJobs: number;
  todayJobs: number;
}> {
  return request<{
    code: number;
    data: {
      totalJobs: number;
      finishedJobs: number;
      processingJobs: number;
      failedJobs: number;
      todayJobs: number;
    };
    msg: string;
  }>({
    url: '/video/batch-producing/stats',
    method: 'get'
  }).then(res => {
    if (res.data.code === 200) {
      return res.data.data;
    } else {
      throw new Error(res.data.msg || '获取统计信息失败');
    }
  }).catch(error => {
    console.error('❌ 获取批量成片统计信息失败:', error);
    throw error;
  });
}

// 导出类型定义，方便其他模块使用
export type {
  InputConfig,
  EditingConfig,
  OutputConfig,
  UserData,
  TemplateConfig,
  SubmitBatchMediaProducingJobRequest,
  SubmitBatchMediaProducingJobResponse
};