import request from '@/utils/request';
import type {
  SubmitBatchMediaProducingJobResponse,
  GetBatchMediaProducingJobResponse,
  ListBatchMediaProducingJobsResponse
} from '../types/batchProducing';

// ==================== 工具函数 ====================

/**
 * 生成客户端Token，用于保证请求幂等性
 * @returns 客户端Token
 */
export function generateClientToken(): string {
  return `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// 任务相关接口定义已移至 ../types/batchProducing.ts

// 响应类型定义已移至 ../types/batchProducing.ts

/**
 * 提交批量智能一键成片任务
 * <p>
 * 通过调用阿里云ICE的 SubmitBatchMediaProducingJob 接口，提交批量智能一键成片任务。
 * 将多个视频、音频、图片素材进行智能混剪，一键批量合成视频。
 * </p>
 *
 * @param inputConfig 输入配置，包含媒体组、标题、语音文本等
 * @param editingConfig 剪辑相关配置，包含音量、背景音乐等设置
 * @param outputConfig 输出配置，包含输出URL、数量、时长等参数
 * @param userData 用户业务配置、回调配置（可选）
 * @param templateConfig 模板配置（可选）
 * @param clientToken 客户端幂等性Token（可选）
 * @returns 返回一个包含作业ID等信息的Promise
 */
export function submitBatchMediaProducingJob(
  inputConfig: string,
  editingConfig: string,
  outputConfig: string,
  userData?: string,
  templateConfig?: string,
  clientToken?: string
): Promise<SubmitBatchMediaProducingJobResponse> {
  // 参数验证
  if (!inputConfig || inputConfig.trim() === '') {
    throw new Error('输入配置不能为空');
  }
  if (!editingConfig || editingConfig.trim() === '') {
    throw new Error('剪辑配置不能为空');
  }
  if (!outputConfig || outputConfig.trim() === '') {
    throw new Error('输出配置不能为空');
  }

  // 构建请求参数
  const formData = new FormData();
  formData.append('inputConfig', inputConfig);
  formData.append('editingConfig', editingConfig);
  formData.append('outputConfig', outputConfig);

  if (userData) {
    formData.append('userData', userData);
  }
  if (templateConfig) {
    formData.append('templateConfig', templateConfig);
  }
  if (clientToken) {
    formData.append('clientToken', clientToken);
  } else {
    formData.append('clientToken', generateClientToken());
  }

  return request<SubmitBatchMediaProducingJobResponse>({
    url: '/video/BatchProducing/submit',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    } as any
  }).then(res => res.data);
}

/**
 * 获取批量智能一键成片任务列表
 * <p>
 * 获取所有符合条件的一键成片任务列表。
 * </p>
 *
 * @param startTime 开始时间（可选）
 * @param endTime 结束时间（可选）
 * @param status 任务状态（可选）
 * @param pageSize 分页大小（可选，默认10）
 * @param nextToken 下一页标记（可选）
 * @returns 返回一个包含任务列表的Promise
 */
export function listBatchMediaProducingJobs(
  startTime?: string,
  endTime?: string,
  status?: string,
  pageSize: number = 10,
  nextToken?: string
): Promise<ListBatchMediaProducingJobsResponse> {
  const params: any = { pageSize };

  if (startTime) params.startTime = startTime;
  if (endTime) params.endTime = endTime;
  if (status) params.status = status;
  if (nextToken) params.nextToken = nextToken;

  return request<ListBatchMediaProducingJobsResponse>({
    url: '/video/BatchProducing/list',
    method: 'get',
    params
  }).then(res => res.data);
}

/**
 * 获取批量智能一键成片任务详情
 * <p>
 * 获取任务的详细信息，包括任务状态、合成的媒资ID及URL等。
 * </p>
 *
 * @param jobId 批量智能一键成片作业ID
 * @returns 返回一个包含任务详细信息的Promise
 */
export function getBatchMediaProducingJob(jobId: string): Promise<GetBatchMediaProducingJobResponse> {
  if (!jobId || jobId.trim() === '') {
    throw new Error('作业ID不能为空');
  }

  return request<GetBatchMediaProducingJobResponse>({
    url: `/video/BatchProducing/job/${jobId}`,
    method: 'get'
  }).then(res => res.data);
}

/**
 * 删除批量成片任务
 * 
 * @param jobId 任务ID
 * @returns 删除结果
 */
export function deleteBatchMediaProducingJob(
  jobId: string
): Promise<{ RequestId: string }> {
  if (!jobId) {
    throw new Error('JobId is required');
  }

  return request<{
    code: number;
    data: { RequestId: string };
    msg: string;
  }>({
    url: `/video/batch-producing/${jobId}`,
    method: 'delete'
  }).then(res => {
    if (res.data.code === 200) {
      return res.data.data;
    } else {
      throw new Error(res.data.msg || '删除任务失败');
    }
  }).catch(error => {
    console.error('❌ 删除批量成片任务失败:', error);
    throw error;
  });
}

/**
 * 重试失败的批量成片任务
 * 
 * @param jobId 任务ID
 * @returns 重试结果
 */
export function retryBatchMediaProducingJob(
  jobId: string
): Promise<{ RequestId: string; JobId: string }> {
  if (!jobId) {
    throw new Error('JobId is required');
  }

  return request<{
    code: number;
    data: { RequestId: string; JobId: string };
    msg: string;
  }>({
    url: `/video/batch-producing/${jobId}/retry`,
    method: 'post'
  }).then(res => {
    if (res.data.code === 200) {
      return res.data.data;
    } else {
      throw new Error(res.data.msg || '重试任务失败');
    }
  }).catch(error => {
    console.error('❌ 重试批量成片任务失败:', error);
    throw error;
  });
}

/**
 * 取消正在处理的批量成片任务
 * 
 * @param jobId 任务ID
 * @returns 取消结果
 */
export function cancelBatchMediaProducingJob(
  jobId: string
): Promise<{ RequestId: string }> {
  if (!jobId) {
    throw new Error('JobId is required');
  }

  return request<{
    code: number;
    data: { RequestId: string };
    msg: string;
  }>({
    url: `/video/batch-producing/${jobId}/cancel`,
    method: 'post'
  }).then(res => {
    if (res.data.code === 200) {
      return res.data.data;
    } else {
      throw new Error(res.data.msg || '取消任务失败');
    }
  }).catch(error => {
    console.error('❌ 取消批量成片任务失败:', error);
    throw error;
  });
}

/**
 * 获取任务统计信息
 * 
 * @returns 统计信息
 */
export function getBatchMediaProducingStats(): Promise<{
  totalJobs: number;
  finishedJobs: number;
  processingJobs: number;
  failedJobs: number;
  todayJobs: number;
}> {
  return request<{
    code: number;
    data: {
      totalJobs: number;
      finishedJobs: number;
      processingJobs: number;
      failedJobs: number;
      todayJobs: number;
    };
    msg: string;
  }>({
    url: '/video/batch-producing/stats',
    method: 'get'
  }).then(res => {
    if (res.data.code === 200) {
      return res.data.data;
    } else {
      throw new Error(res.data.msg || '获取统计信息失败');
    }
  }).catch(error => {
    console.error('❌ 获取批量成片统计信息失败:', error);
    throw error;
  });
}

// 类型定义已在 ../types/batchProducing.ts 中统一管理