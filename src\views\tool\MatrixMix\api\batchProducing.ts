import request from '@/utils/request';
import { generateClientToken } from '../utils/batchMediaUtils';
import type {
  SubmitBatchMediaProducingJobResponse,
  SubmitBatchMediaProducingJobParams,
  GetBatchMediaProducingJobResponse,
  ListBatchMediaProducingJobsResponse,
  ListBatchMediaProducingJobsParams
} from '../types/batchProducing';

// ==================== API函数 ====================

/**
 * 提交批量智能一键成片任务
 * @param params 提交参数
 * @returns 返回作业ID等信息
 */
export function submitBatchMediaProducingJob(params: SubmitBatchMediaProducingJobParams): Promise<SubmitBatchMediaProducingJobResponse> {
  // 参数验证
  if (!params.inputConfig?.trim()) {
    throw new Error('输入配置不能为空');
  }
  if (!params.editingConfig?.trim()) {
    throw new Error('剪辑配置不能为空');
  }
  if (!params.outputConfig?.trim()) {
    throw new Error('输出配置不能为空');
  }

  // 构建请求参数
  const formData = new FormData();
  formData.append('inputConfig', params.inputConfig);
  formData.append('editingConfig', params.editingConfig);
  formData.append('outputConfig', params.outputConfig);

  if (params.userData) {
    formData.append('userData', params.userData);
  }
  if (params.templateConfig) {
    formData.append('templateConfig', params.templateConfig);
  }
  formData.append('clientToken', params.clientToken || generateClientToken());

  return request<SubmitBatchMediaProducingJobResponse>({
    url: '/video/BatchProducing/submit',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    } as any
  }).then(res => res.data);
}

/**
 * 获取批量智能一键成片任务详情
 * @param jobId 批量智能一键成片作业ID
 * @returns 返回任务详细信息
 */
export function getBatchMediaProducingJob(jobId: string): Promise<GetBatchMediaProducingJobResponse> {
  if (!jobId || jobId.trim() === '') {
    throw new Error('作业ID不能为空');
  }

  return request<GetBatchMediaProducingJobResponse>({
    url: `/video/BatchProducing/job/${jobId}`,
    method: 'get'
  }).then(res => res.data);
}

/**
 * 获取批量智能一键成片任务列表
 * @param params 查询参数
 * @returns 返回任务列表
 */
export function listBatchMediaProducingJobs(params: ListBatchMediaProducingJobsParams): Promise<ListBatchMediaProducingJobsResponse> {
  const requestParams = {
    pageSize: params.pageSize || 10,
    ...params,
    // 处理后端参数名差异
    JobType: params.jobType
  };

  // 删除前端参数名，避免重复
  delete (requestParams as any).jobType;

  return request<ListBatchMediaProducingJobsResponse>({
    url: '/video/BatchProducing/list',
    method: 'get',
    params: requestParams
  }).then(res => res.data);
}
