import request from '@/utils/request';

// ==================== 接口类型定义 ====================

/**
 * 通用API响应接口
 */
interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}

/**
 * 提交批量智能一键成片任务响应
 */
export interface SubmitBatchMediaProducingJobResponse {
  RequestId: string;
  JobId: string;
}

/**
 * 批量智能一键成片任务详情
 */
export interface BatchMediaProducingJobDetail {
  JobId: string;
  InputConfig: string;
  EditingConfig: string;
  OutputConfig: string;
  Status: 'Init' | 'Processing' | 'Finished' | 'Failed';
  UserData: string;
  Extend?: {
    ErrorCode?: string;
    ErrorMessage?: string;
  };
  CreateTime: string;
  ModifiedTime: string;
  CompleteTime?: string;
  JobType: string;
  SubJobList?: SubJobInfo[];
}

/**
 * 子任务信息
 */
export interface SubJobInfo {
  MediaId: string;
  JobId: string;
  MediaURL: string;
  Status: 'Init' | 'Processing' | 'Success' | 'Failed';
  ErrorCode?: string;
  ErrorMessage?: string;
  ProjectId: string;
  Duration: number;
}

/**
 * 获取任务详情响应
 */
export interface GetBatchMediaProducingJobResponse {
  RequestId: string;
  EditingBatchJob: BatchMediaProducingJobDetail;
}

/**
 * 任务列表响应
 */
export interface ListBatchMediaProducingJobsResponse {
  RequestId: string;
  EditingBatchJobList: BatchMediaProducingJobDetail[];
  NextToken?: string;
  MaxResults?: number;
}

/**
 * 查询表单类型
 */
export interface QueryForm {
  jobId: string;
  jobType: string;
  status: string;
  startTime: string;
  endTime: string;
}

/**
 * 解析后的任务数据类型（用于前端显示）
 */
export interface ParsedBatchJob {
  jobId: string;
  jobType: string;
  status: string;
  createTime: string;
  updateTime: string;
  completeTime: string;
  taskName: string;
  taskDescription: string;
  mediaCount: number;
  groupCount: number;
  titleCount: number;
  speechCount: number;
  outputCount: number;
  resolution: string;
  errorCode?: string;
  errorMessage?: string;
  configSnapshot: any;
  progress: number;
}

/**
 * 任务列表请求参数类型
 */
export interface ListBatchMediaProducingJobsRequest {
  jobId?: string;
  maxResults?: number;
  sortBy?: 'asc' | 'desc';
  startTime?: string;
  endTime?: string;
  status?: string;
  nextToken?: string;
  jobType?: string;
}

// ==================== 工具函数 ====================

/**
 * 生成客户端Token，用于保证请求幂等性
 * @returns 客户端Token
 */
export function generateClientToken(): string {
  return `batch_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

// ==================== API函数 ====================

/**
 * 提交批量智能一键成片任务
 * <p>
 * 将多个视频、音频、图片素材进行智能混剪，一键批量合成视频。
 * 支持脚本化自动成片、智能图文匹配成片等多种解决方案。
 * </p>
 *
 * @param inputConfig 输入配置，包含媒体组、标题、语音文本等
 * @param editingConfig 剪辑相关配置，包含音量、背景音乐等设置
 * @param outputConfig 输出配置，包含输出URL、数量、时长等参数
 * @param userData 用户业务配置（可选）
 * @param templateConfig 模板配置（可选）
 * @param clientToken 客户端幂等性Token（可选）
 * @returns 返回作业ID等信息
 */
export function submitBatchMediaProducingJob(
  inputConfig: string,
  editingConfig: string,
  outputConfig: string,
  userData?: string,
  templateConfig?: string,
  clientToken?: string
): Promise<SubmitBatchMediaProducingJobResponse> {
  // 参数验证
  if (!inputConfig || inputConfig.trim() === '') {
    throw new Error('输入配置不能为空');
  }
  if (!editingConfig || editingConfig.trim() === '') {
    throw new Error('剪辑配置不能为空');
  }
  if (!outputConfig || outputConfig.trim() === '') {
    throw new Error('输出配置不能为空');
  }

  // 构建请求参数
  const formData = new FormData();
  formData.append('inputConfig', inputConfig);
  formData.append('editingConfig', editingConfig);
  formData.append('outputConfig', outputConfig);

  if (userData) {
    formData.append('userData', userData);
  }
  if (templateConfig) {
    formData.append('templateConfig', templateConfig);
  }
  if (clientToken) {
    formData.append('clientToken', clientToken);
  } else {
    formData.append('clientToken', generateClientToken());
  }

  return request<ApiResponse<SubmitBatchMediaProducingJobResponse>>({
    url: '/video/BatchProducing/submit',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    } as any
  }).then(res => res.data.data);
}

/**
 * 获取批量智能一键成片任务详情
 * <p>
 * 获取任务的详细信息，包括任务状态、合成的媒资ID及URL等。
 * </p>
 *
 * @param jobId 批量智能一键成片作业ID
 * @returns 返回任务详细信息
 */
export function getBatchMediaProducingJob(jobId: string): Promise<GetBatchMediaProducingJobResponse> {
  if (!jobId || jobId.trim() === '') {
    throw new Error('作业ID不能为空');
  }

  return request<ApiResponse<GetBatchMediaProducingJobResponse>>({
    url: `/video/BatchProducing/job/${jobId}`,
    method: 'get'
  }).then(res => res.data.data);
}

/**
 * 获取批量智能一键成片任务列表
 * <p>
 * 获取所有符合条件的一键成片任务列表。
 * </p>
 *
 * @param startTime 开始时间（可选）
 * @param endTime 结束时间（可选）
 * @param status 任务状态（可选）
 * @param pageSize 分页大小（可选，默认10）
 * @param nextToken 下一页标记（可选）
 * @param jobType 作业类型（可选，默认为"Script"）
 * @returns 返回任务列表
 */
export function listBatchMediaProducingJobs(
  startTime?: string,
  endTime?: string,
  status?: string,
  pageSize: number = 10,
  nextToken?: string,
  jobType?: string
): Promise<ListBatchMediaProducingJobsResponse> {
  const params: any = { pageSize };

  if (startTime) params.startTime = startTime;
  if (endTime) params.endTime = endTime;
  if (status) params.status = status;
  if (nextToken) params.nextToken = nextToken;
  if (jobType) params.JobType = jobType;

  return request<ApiResponse<ListBatchMediaProducingJobsResponse>>({
    url: '/video/BatchProducing/list',
    method: 'get',
    params
  }).then(res => {
    console.log('🔍 API原始响应:', res);
    console.log('🔍 API响应数据:', res.data);

    // 根据后端返回的数据结构，返回 res.data
    // 后端返回格式: { code: 200, msg: "success", data: { RequestId, EditingBatchJobList, ... } }
    const result = res.data;
    console.log('🔍 最终返回数据:', result);
    return result;
  });
}