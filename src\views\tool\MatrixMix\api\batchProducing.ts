import request from '@/utils/request';

interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}

/**
 * 提交批量智能一键成片任务
 * <p>
 * 通过调用阿里云ICE的 BatchProducing 接口，提交批量智能一键成片任务。
 * </p>
 * @param inputConfig  输入配置，包括模板ID、媒资ID等
 * @param editingConfig 剪辑配置，包括剪辑点、音频配置等
 * @param outputConfig 输出配置，包括输出格式、分辨率等
 * @param userData 用户业务配置、回调配置
 * @param templateConfig 模板配置，包括模板ID、名称等(可选)
 * @param clientToken 客户端令牌，用于幂等性控制(可选)
 * @returns 返回一个包含任务ID的Promise
 * @throws 如果请求失败，将抛出错误。
 * <AUTHOR>
 * @date 2025-07-12
 */
export function submitBatchProducingTask(
  inputConfig: string,
  editingConfig: string,
  outputConfig: string,
  userData: string,
  templateConfig?: string,
  clientToken?: string
): Promise<ApiResponse<string>> {
  return request<ApiResponse<string>>({
    url: '/video/BatchProducing/batchProducing',
    method: 'POST',
    data: {
      inputConfig,
      editingConfig,
      outputConfig,
      userData,
      templateConfig,
      clientToken
    }
  }).then(res => res.data);
}