import request from '@/utils/request';

// ==================== 接口类型定义 ====================

/**
 * 提交批量智能一键成片任务响应
 */
export interface SubmitBatchMediaProducingJobResponse {
  RequestId: string;
  JobId: string;
}

/**
 * 批量智能一键成片任务详情
 */
export interface BatchMediaProducingJobDetail {
  JobId: string;
  InputConfig: string;
  EditingConfig: string;
  OutputConfig: string;
  Status: 'Init' | 'Processing' | 'Finished' | 'Failed';
  UserData: string; 
  Extend?: {
    ErrorCode?: string;
    ErrorMessage?: string;
  };
  CreateTime: string;
  ModifiedTime: string;
  CompleteTime?: string;
  JobType: string;
  SubJobList?: SubJobInfo[];
}

/**
 * 子任务信息
 */
export interface SubJobInfo {
  MediaId: string;
  JobId: string;
  MediaURL: string;
  Status: 'Init' | 'Processing' | 'Success' | 'Failed';
  ErrorCode?: string;
  ErrorMessage?: string;
  ProjectId: string;
  Duration: number;
}

/**
 * 获取任务详情响应
 */
export interface GetBatchMediaProducingJobResponse {
  RequestId: string;
  EditingBatchJob: BatchMediaProducingJobDetail;
}

/**
 * 任务列表响应
 */
export interface ListBatchMediaProducingJobsResponse {
  RequestId: string;
  EditingBatchJobList: BatchMediaProducingJobDetail[];
  NextToken?: string;
  MaxResults?: number;
}

/**
 * 查询表单类型
 */
export interface QueryForm {
  jobId: string;
  jobType: string;
  status: string;
  startTime: string;
  endTime: string;
}

/**
 * 解析后的任务数据类型（用于前端显示）
 */
export interface ParsedBatchJob {
  jobId: string;
  jobType: string;
  status: string;
  createTime: string;
  updateTime: string;
  completeTime: string;
  taskName: string;
  taskDescription: string;
  mediaCount: number;
  groupCount: number;
  titleCount: number;
  speechCount: number;
  outputCount: number;
  resolution: string;
  errorCode?: string;
  errorMessage?: string;
  configSnapshot: any;
  progress: number;
}

/**
 * 任务列表请求参数类型
 */
export interface ListBatchMediaProducingJobsParams {
  startTime?: string;
  endTime?: string;
  status?: string;
  pageSize?: number;
  nextToken?: string;
  jobType?: string;
}

/**
 * 任务列表请求参数类型（用于组件内部）
 */
export interface ListBatchMediaProducingJobsRequest {
  jobId?: string;
  maxResults?: number;
  sortBy?: 'asc' | 'desc';
  startTime?: string;
  endTime?: string;
  status?: string;
  nextToken?: string;
  jobType?: string;
}

// ==================== 工具函数 ====================

/**
 * 生成客户端Token，用于保证请求幂等性
 * @returns 客户端Token
 */
export function generateClientToken(): string {
  return `batch_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

// ==================== API函数 ====================

/**
 * 提交批量智能一键成片任务参数
 */
export interface SubmitBatchMediaProducingJobParams {
  inputConfig: string;
  editingConfig: string;
  outputConfig: string;
  userData?: string;
  templateConfig?: string;
  clientToken?: string;
}

/**
 * 提交批量智能一键成片任务
 * @param params 提交参数
 * @returns 返回作业ID等信息
 */
export function submitBatchMediaProducingJob(params: SubmitBatchMediaProducingJobParams): Promise<SubmitBatchMediaProducingJobResponse> {
  // 参数验证
  if (!params.inputConfig?.trim()) {
    throw new Error('输入配置不能为空');
  }
  if (!params.editingConfig?.trim()) {
    throw new Error('剪辑配置不能为空');
  }
  if (!params.outputConfig?.trim()) {
    throw new Error('输出配置不能为空');
  }

  // 构建请求参数
  const formData = new FormData();
  formData.append('inputConfig', params.inputConfig);
  formData.append('editingConfig', params.editingConfig);
  formData.append('outputConfig', params.outputConfig);

  if (params.userData) {
    formData.append('userData', params.userData);
  }
  if (params.templateConfig) {
    formData.append('templateConfig', params.templateConfig);
  }
  formData.append('clientToken', params.clientToken || generateClientToken());

  return request<SubmitBatchMediaProducingJobResponse>({
    url: '/video/BatchProducing/submit',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    } as any
  }).then(res => res.data);
}

/**
 * 获取批量智能一键成片任务详情
 * <p>
 * 获取任务的详细信息，包括任务状态、合成的媒资ID及URL等。
 * </p>
 *
 * @param jobId 批量智能一键成片作业ID
 * @returns 返回任务详细信息
 */
export function getBatchMediaProducingJob(jobId: string): Promise<GetBatchMediaProducingJobResponse> {
  if (!jobId || jobId.trim() === '') {
    throw new Error('作业ID不能为空');
  }

  return request<GetBatchMediaProducingJobResponse>({
    url: `/video/BatchProducing/job/${jobId}`,
    method: 'get'
  }).then(res => res.data);
}

/**
 * 获取批量智能一键成片任务列表
 * @param params 查询参数
 * @returns 返回任务列表
 */
export function listBatchMediaProducingJobs(params: ListBatchMediaProducingJobsParams): Promise<ListBatchMediaProducingJobsResponse> {
  const requestParams = {
    pageSize: params.pageSize || 10,
    ...params,
    // 处理后端参数名差异
    JobType: params.jobType
  };

  // 删除前端参数名，避免重复
  delete (requestParams as any).jobType;

  return request<ListBatchMediaProducingJobsResponse>({
    url: '/video/BatchProducing/list',
    method: 'get',
    params: requestParams
  }).then(res => res.data);
}