import request from '@/utils/request';
import type {
  MediaInfoQuery,
  MediaInfoResponse,
  MediaPayload,
  MediaListQueryParams,
  MediaSearchParams,
  MediaListResponse,
  GetEditingProjectMaterialsRequest,
  GetEditingProjectMaterialsResponse,
  MediaUploadRequest,
  MediaUploadResponse,
  MediaRegisterRequest,
  MediaRegisterResponse,
  UploadAndRegisterResponse,
  BatchGetMediaInfosRequest,
  BatchGetMediaInfosResponse
} from '../types/media';

// The request utility returns a wrapped response object.
// We define its structure here to properly type the response.
interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}

/**
 * 获取单个媒资的详细信息
 * <p>
 * 通过调用阿里云ICE(智能媒体服务)的 GetMediaInfo 接口，获取媒资详情。
 * 必须提供 MediaId 或 InputURL 两者之一。
 * </p>
 *
 * @param params GetMediaInfo的请求参数
 * @returns 返回一个包含了媒资详细信息的Promise。
 */
export function getMediaInfo(params: MediaInfoQuery): Promise<MediaInfoResponse> {
  // Pass the generic to get a typed response wrapper, then unwrap the 'data' property.
  return request<MediaInfoResponse>({
    url: '/video/media/info',
    method: 'get',
    params
  }).then(res => res.data);
}
/**
 * 获取剪辑工程关联素材
 * <p>
 * 通过调用阿里云ICE的 GetEditingProjectMaterials 接口，获取当前工程绑定的所有素材。
 * </p>
 *
 * @param projectId 云剪辑工程ID（必填）
 * @returns 返回一个包含工程关联素材信息的Promise
 */
export function getEditingProjectMaterials(projectId: string): Promise<GetEditingProjectMaterialsResponse> {
  return request<GetEditingProjectMaterialsResponse>({
    url: `/video/media/project/${projectId}/materials`,
    method: 'get',
  }).then(res => res.data);
}

/**
 * 增加剪辑关联素材
 * <p>
 * 通过调用阿里云ICE的 addEditingProjectMaterials 接口，增加剪辑工程的关联素材。
 * </p>
 * 
 * @param projectId 云剪辑工程ID（必填）
 * @param materialIds 素材ID列表（必填）
 * @returns 返回一个包含操作结果的Promise
 * @throws 如果请求失败，将抛出错误。
 * <AUTHOR>
 * @date 2025-07-12
 */
export function addEditingProjectMaterials(
  projectId: string,
  materialIds: string[]
): Promise<ApiResponse<void>> {
  return request<ApiResponse<void>>({
    url: `/video/media/addProjectMaterials`,
    method: 'post',
    data: { projectId, materialIds }
  }).then(res => res.data);
}


/**
 * 一体化媒资上传与注册接口
 * <p>
 * 新版本的一体化接口，实现文件上传+注册一步完成，简化前端上传流程。
 * 支持多媒体类型和分类，直接返回媒资ID等关键信息。
 * </p>
 * 
 * @param file 要上传的文件
 * @param category 媒资分类 (video/audio/music/image/font)
 * @param metadata 可选的媒资元数据
 * @returns 返回包含媒资ID等信息的Promise
 * <AUTHOR>
 * @date 2025-07-12
 */
export function uploadAndRegisterMedia(
  file: File,
  category: string,
  metadata?: {
    title?: string;
    description?: string;
    tags?: string;
    businessType?: string;
  }
): Promise<UploadAndRegisterResponse> {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('category', category);
  // 添加元数据（如果提供）
  if (metadata) {
    if (metadata.title) formData.append('title', metadata.title);
    if (metadata.description) formData.append('description', metadata.description);
    if (metadata.tags) formData.append('tags', metadata.tags);
    if (metadata.businessType) formData.append('businessType', metadata.businessType);
  }
  try {
    // @ts-ignore - FormData.entries() 可能在某些 TypeScript 版本中不被识别
    for (let [key, value] of formData.entries()) {
      if (value instanceof File) {
        console.log(`  ${key}:`, `File(${value.name}, ${value.size} bytes, ${value.type})`);
      } else {
        console.log(`  ${key}:`, value);
      }
    }
  } catch (e) {
    console.log('  无法枚举 FormData 内容 (兼容性问题)');
  }
  return request<any>({
    url: '/video/media/uploadAndRegister',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
      isToken: true
    } as any
  }).then(res => {
    console.log('✅ 上传成功响应:', res);
    // 后端返回的是扁平化的响应结构，直接返回
    return res as unknown as UploadAndRegisterResponse;
  }).catch(error => {
    console.error('❌ 上传失败错误:', error);
    console.error('❌ 错误详情:', error.response?.data || error.message);
    throw error;
  });
}

/**
 * 批量获取媒资信息
 * <p>
 * 通过调用阿里云ICE的 BatchGetMediaInfos 接口，批量获取媒资详情。支持传入多个 MediaId。
 * </p>
 * 
 * @param params 请求参数
 * @returns 返回批量媒资信息
 * @throws 如果请求失败，将抛出错误。
 * <AUTHOR>
 * @date 2025-07-12
 */
export function batchGetMediaInfos(
  params: BatchGetMediaInfosRequest
): Promise<BatchGetMediaInfosResponse> {
  return request<BatchGetMediaInfosResponse>({
    url: '/video/media/batchGetMediaInfos',
    method: 'post',
    data: params
  }).then(res => res.data);
}

/**
 * 批量获取媒资信息（简化版本）
 * 
 * @param mediaIds 媒资ID数组
 * @param additionType 额外信息类型
 * @returns 返回批量媒资信息
 */
export function batchGetMediaInfosSimple(
  mediaIds: string[],
  additionType?: string[]
): Promise<BatchGetMediaInfosResponse> {
  const params: BatchGetMediaInfosRequest = {
    MediaIds: mediaIds.join(','),
    AdditionType: additionType ? additionType.join(',') : undefined
  };
  
  return batchGetMediaInfos(params);
}

/**
 * 列出媒资基础信息列表
 * <p>
 * 通过调用阿里云ICE的 listMediaBasicInfo 接口，获取媒资列表。
 * 支持按类型、状态、标签等条件筛选。
 * </p>
 * 
 * @param params 查询参数
 * @returns 返回媒资列表
 * @throws 如果请求失败，将抛出错误。
 * <AUTHOR>
 * @date 2025-07-12
 */
export function listMediaBasicInfo(params: MediaListQueryParams): Promise<MediaListResponse> {
  return request<MediaListResponse>({
    url: '/video/media/listMediaBasicInfo',
    method: 'get',
    params
  }).then(res => res.data);
}

/**
 * 搜索媒资
 * <p>
 * 通过关键词搜索媒资，支持标题、描述、标签等字段的模糊搜索。
 * </p>
 * 
 * @param params 搜索参数
 * @returns 返回搜索结果
 * @throws 如果请求失败，将抛出错误。
 * <AUTHOR>
 * @date 2025-07-12
 */
export function searchMediaInfos(params: MediaSearchParams): Promise<MediaListResponse> {
  return request<{
    RequestId: string;
    MediaInfos: any[];
    TotalCount: number;
    PageNumber: number;
    PageSize: number;
  }>({
    url: '/video/media/search',
    method: 'get',
    params
  }).then(res => res.data);
}

// 导出类型定义，方便其他模块使用
export type {
  BatchGetMediaInfosRequest,
  BatchGetMediaInfosResponse
};

// 重新导出工具类，保持向后兼容
export { BatchMediaInfoUtils, MediaInfoCache, MediaInfoAnalyzer } from '../utils/batchMediaUtils';

// ============ 以下为已废弃的旧接口，保留用于兼容性 ============

/**
 * @deprecated 已废弃，请使用 uploadAndRegisterMedia 一体化接口
 * 获取OSS上传授权
 */
export function createUploadMedia(params: { fileInfo: { name: string; ext: string; type: string; size?: number } }): Promise<{ signedUrl: string; uploadId: string }> {
  return request<{ signedUrl: string; uploadId: string }>({
    url: '/video/media/createUploadMedia',
    method: 'post',
    data: params
  }).then(res => res.data);
}

/**
 * @deprecated 已废弃，请使用 uploadAndRegisterMedia 一体化接口
 * 注册媒资到ICE库
 */
export function registerOssFileToIce(params: {
  uploadId: string;
  fileInfo: { name: string; ext: string; type: string; size?: number };
  mediaMetaData?: {
    title?: string;
    description?: string;
    tags?: string;
    businessType?: string;
  };
  userData?: {
    extend?: Record<string, any>;
  };
}): Promise<any> {
  return request<any>({
    url: '/video/media/registerOssFileToIce',
    method: 'post',
    data: params
  }).then(res => res);
}


/**
 * @deprecated 已废弃，请使用 uploadAndRegisterMedia 一体化接口
 * 注册媒资到内容库 - 已废弃，使用新的registerOssFileToIce接口
 * <p>
 * 通过调用阿里云ICE的 RegisterMediaInfo 接口，发起一个注册媒资的任务，赋予新媒资一个IMS的mediaId。
 * 接口根据InputURL，异步调用其他媒资信息服务，获取媒资的文件信息。
 * 注册媒资是一个异步任务，一般需要2-3s完成。
 * </p>
 * 
 * @param params 媒资注册请求参数
 * @returns 返回一个包含媒资注册结果的Promise，包含RequestId和MediaId
 * @throws 如果请求失败，将抛出错误。
 * <AUTHOR>
 * @date 2025-07-12
 */
export function RegisterMedia(params: MediaRegisterRequest): Promise<MediaRegisterResponse> {
  return request<MediaRegisterResponse>({
    url: '/video/media/register',
    method: 'post',
    data: params
  }).then(res => res.data);
}
