import request from '@/utils/request';
import type { 
  AddTemplateRequest, 
  AddTemplateResponse, 
  ListTemplatesRequest, 
  ListTemplatesResponse, 
  TemplateInfo, 
  UpdateTemplateRequest, 
  UpdateTemplateResponse, 
  DeleteTemplateRequest, 
  DeleteTemplateResponse,
  GetTemplateMaterialsRequest,
  GetTemplateMaterialsResponse 
} from '../types/template';

// 定义后端返回的基础结构
interface AjaxResult<T> {
  code: number;
  msg: string;
  data: T;
}

/**
 * 创建一个新模板
 * @param data 包含创建模板所需参数的数据对象，遵循 AddTemplateRequest 接口
 * @returns 返回一个 Promise，成功时解析为包含模板信息的响应
 */
export function addTemplate(data: AddTemplateRequest): Promise<AjaxResult<AddTemplateResponse>> {
  return request({
    url: '/video/template/add',
    method: 'post',
    data: data,
  });
}

/**
 * 获取模板列表
 * @param params 包含查询参数的对象，遵循 ListTemplatesRequest 接口
 * @returns 返回一个 Promise，成功时解析为包含模板列表的响应
 */
export function listTemplates(params: ListTemplatesRequest): Promise<AjaxResult<ListTemplatesResponse>> {
  return request({
    url: '/video/template/list',
    method: 'get',
    params: params,
  });
}

/**
 * 获取单个模板的详细信息
 * @param templateId 模板ID
 * @param params 可选参数，如 relatedMediaidFlag
 * @returns 返回一个 Promise，成功时解析为包含模板详情的响应
 */
export function getTemplate(templateId: string, params?: { relatedMediaidFlag?: string }): Promise<AjaxResult<{ RequestId: string; Template: TemplateInfo }>> {
  return request({
    url: `/video/template/${templateId}`,
    method: 'get',
    params: params,
  });
}

/**
 * 修改模板
 * @param data 包含修改模板所需参数的数据对象，遵循 UpdateTemplateRequest 接口
 * @returns 返回一个 Promise，成功时解析为包含请求ID的响应
 */
export function updateTemplate(data: UpdateTemplateRequest): Promise<AjaxResult<UpdateTemplateResponse>> {
  return request({
    url: '/video/template/update',
    method: 'put',
    data: data,
  });
}

/**
 * 删除模板
 * @param data 包含删除模板所需参数的数据对象，遵循 DeleteTemplateRequest 接口
 * @returns 返回一个 Promise，成功时解析为包含请求ID的响应
 */
export function deleteTemplate(data: DeleteTemplateRequest): Promise<AjaxResult<DeleteTemplateResponse>> {
  return request({
    url: '/video/template/delete',
    method: 'post',
    params: data,
  });
}

/**
 * @deprecated 此接口已废弃，推荐使用 getTemplate + getMediaInfo 方案
 * 获取模板素材地址
 * <p>
 * 返回高级模板包中的素材地址，供高级模板编辑器使用，素材链接30分钟过期。
 * FileList为所需素材数组，不填则默认返回全部素材地址，最多返回400个。
 * 注意：此接口依赖阿里云文件签名服务，可能遇到权限配置问题
 * </p>
 *
 * @param templateId 模板ID
 * @param fileList 可选。所需文件列表，JSON格式字符串，如：["music.mp3","config.json","assets/1.jpg"]
 * @returns 返回一个 Promise，成功时解析为包含模板素材地址映射的响应
 */
export function getTemplateMaterials(
  templateId: string, 
  fileList?: string
): Promise<AjaxResult<GetTemplateMaterialsResponse>> {
  const params: GetTemplateMaterialsRequest = {
    TemplateId: templateId
  };
  
  if (fileList) {
    params.FileList = fileList;
  }
  
  return request({
    url: `/video/template/${templateId}/materials`,
    method: 'get',
    params: params,
  });
}