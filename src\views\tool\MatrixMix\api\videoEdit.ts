import request from '@/utils/request';
import type {
  VideoEditListParams,
  VideoEditApiResponse,
  VideoEditDetailApiResponse,
  CreateEditingProjectDTO
} from '../types/videoEdit';

/**
 * 获取云剪辑工程列表
 * @param params 查询参数
 */
export function listEditingProjects(params: VideoEditListParams) {
  return request<VideoEditApiResponse>({
    url: '/video/project/list',
    method: 'get',
    params
  });
}

/**
 * 删除云剪辑工程
 * @param projectIds 要删除的工程ID，多个ID以逗号分隔
 */
export function deleteEditingProjects(projectIds: string) {
  // 阿里云的删除接口返回结果我们可能不需要特别精确的类型
  return request<any>({
    url: '/video/project/delete',
    method: 'post',
    params: { projectIds } // @RequestParam, use params
  });
}

/**
 * 获取单个云剪辑工程的详细信息
 * @param projectId 云剪辑工程ID
 * @param requestSource 可选的请求来源
 */
export function getEditingProject(projectId: string, requestSource?: string) {
  return request<VideoEditDetailApiResponse>({
    url: `/video/project/${projectId}`,
    method: 'get',
    params: { requestSource }
  });
}

/**
 * 创建云剪辑工程
 * @param data 创建工程所需的数据对象
 */
export function createEditingProject(data: CreateEditingProjectDTO) {
  return request<any>({
    url: '/video/project/create',
    method: 'post',
    data // @RequestBody, use data
  });
} 