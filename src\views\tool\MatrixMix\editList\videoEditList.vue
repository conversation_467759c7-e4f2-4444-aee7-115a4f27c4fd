<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>云剪辑</span>
          <el-button class="button" link>帮助文档</el-button>
        </div>
      </template>
      <el-tabs v-model="activeTab" @tab-click="handleQuery">
        <el-tab-pane label="视频剪辑工程" name="video"></el-tab-pane>
        <el-tab-pane label="直播剪辑工程" name="live"></el-tab-pane>
        <el-tab-pane label="高级模板剪辑工程" name="template"></el-tab-pane>
      </el-tabs>

      <el-alert :closable="false" show-icon title="帮助信息" type="info">
        <p>1. 按照剪辑合成的成片时长计费，若处理失败，不收取费用。计费详情</p>
        <p>2. 当前控制台剪辑编辑器，集成了视频剪辑webSDK 5.4.0版本。
          想要将视频剪辑webSDK集成到您的系统，请先申请License（添加钉钉答疑群"【客】智能媒体服务官方答疑群"，群号：84650000851），集成说明请参考: 视频webSDK</p>
        <p>3. 默认展示近180天的剪辑工程，如需展示更早的剪辑工程，可以根据时间范围筛选</p>
      </el-alert>

      <div class="table-toolbar">
        <div class="toolbar-left-group">
          <el-button icon="Plus" plain type="primary" @click="handleCreate">创建剪辑工程</el-button>
          <el-button :disabled="multiple" icon="Delete" plain type="danger" @click="handleDelete()">删除</el-button>
          <el-input v-model="queryParams.keyword" placeholder="请输入工程标题/描述" style="width: 200px;" clearable
            @keyup.enter="handleQuery"></el-input>
          <el-button icon="Search" type="primary" @click="handleQuery">查询</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </div>
        <div class="toolbar-right-group">
          <el-button plain>查看剪辑任务</el-button>
        </div>
      </div>

      <el-table v-loading="loading" :data="projectList" style="width: 100%; margin-top: 20px;"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center"></el-table-column>
        <el-table-column label="工程" width="120">
          <template #default="{ row }">
            <div class="project-info">
              <el-image class="project-thumbnail" :src="row.CoverURL" fit="cover">
                <template #error>
                  <div class="image-slot">
                    <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="#c0c4cc">
                      <path
                        d="M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4z" />
                    </svg>
                  </div>
                </template>
              </el-image>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="工程ID/名称" min-width="250">
          <template #default="{ row }">
            <div>{{ row.ProjectId }}</div>
            <div>{{ row.Title }}</div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.Status === 'Normal' || row.Status === 'Produced' ? 'success' : 'warning'">{{ row.Status
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="创建时间" prop="CreateTime" width="180"></el-table-column>
        <el-table-column align="center" label="更新时间" prop="ModifiedTime" width="180"></el-table-column>
        <el-table-column align="center" class-name="small-padding fixed-width" label="操作" width="240">
          <template #default="{ row }">
            <el-button icon="Edit" link type="primary" @click="handleEdit(row)">剪辑</el-button>
            <el-button icon="DocumentCopy" link type="primary">复制</el-button>
            <el-button icon="Download" link type="primary">导出模板</el-button>
            <el-dropdown>
              <el-button link type="primary" style="margin-left: 10px;">
                <el-icon>
                  <More />
                </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item icon="Delete" style="color: #F56C6C;"
                    @click="handleDelete(row)">删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

    <!-- 添加/修改剪辑工程对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px">
      <el-form ref="projectFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="工程标题" prop="Title">
          <el-input v-model="form.Title" placeholder="请输入工程标题" />
        </el-form-item>
        <el-form-item label="工程描述" prop="Description">
          <el-input v-model="form.Description" type="textarea" placeholder="请输入工程描述" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="VideoEditList">
import { ref, reactive, onMounted, toRefs, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import { listEditingProjects, deleteEditingProjects, createEditingProject } from '../api/videoEdit';
import type { VideoEditProject, VideoEditListParams, CreateEditingProjectDTO } from '../types/videoEdit';
import { ElMessage, ElMessageBox, FormInstance, FormRules } from 'element-plus';

defineOptions({
  name: "VideoEditList"
});

const router = useRouter();
const activeTab = ref('video');
const projectFormRef = ref<FormInstance>();

const state = reactive({
  loading: true,
  ids: [] as string[],
  multiple: true,
  total: 0,
  projectList: [] as VideoEditProject[],
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    keyword: '',
    status: undefined, // Add other status filters if needed
  } as VideoEditListParams,
  form: {} as CreateEditingProjectDTO,
  dialog: {
    visible: false,
    title: ''
  },
  rules: {
    Title: [
      { required: true, message: "工程标题不能为空", trigger: "blur" }
    ],
  } as FormRules,
});

const { queryParams, loading, projectList, total, multiple, form, dialog, rules } = toRefs(state);

/** 取消按钮 */
function cancel() {
  state.dialog.visible = false;
  resetForm();
}

/** 表单重置 */
function resetForm() {
  state.form = {
    Title: '',
    Description: ''
  };
  projectFormRef.value?.resetFields();
}

/** 新增按钮操作 */
function handleCreate() {
  resetForm();
  state.dialog.visible = true;
  state.dialog.title = "创建剪辑工程";
}

/** 编辑按钮操作 */
function handleEdit(row: VideoEditProject) {
  router.push({
    path: `/tool/matrix-mix/video-edit/${row.ProjectId}`,
    query: { from: 'cloud-editing' }
  });
}

/** 提交按钮 */
async function submitForm() {
  projectFormRef.value?.validate(async (valid) => {
    if (valid) {
      try {
        await createEditingProject(state.form);
        ElMessage.success('创建成功');
        state.dialog.visible = false;
        getList();
      } catch (error) {
        console.error("创建失败: ", error);
        ElMessage.error("创建失败");
      }
    }
  });
}

/** 查询剪辑工程列表 */
async function getList() {
  state.loading = true;
  try {
    const response = await listEditingProjects(state.queryParams);
    state.projectList = response.data.ProjectList || [];
    state.total = response.data.TotalCount || state.projectList.length;
  } catch (error) {
    ElMessage.error('获取剪辑工程列表失败');
    console.error(error);
  } finally {
    state.loading = false;
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  state.queryParams.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  state.queryParams.pageNum = 1;
  state.queryParams.keyword = '';
  // queryFormRef.value?.resetFields(); // If using ElForm
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection: VideoEditProject[]) {
  state.ids = selection.map(item => item.ProjectId);
  state.multiple = !selection.length;
}

/** 删除按钮操作 */
async function handleDelete(row?: VideoEditProject) {
  const projectIds = row ? [row.ProjectId] : state.ids;
  if (projectIds.length === 0) {
    ElMessage.warning('请选择要删除的工程');
    return;
  }

  try {
    await ElMessageBox.confirm(
      `是否确认删除工程ID为 "${projectIds.join(', ')}" 的数据项？`,
      '系统提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    await deleteEditingProjects(projectIds.join(','));
    ElMessage.success('删除成功');
    getList(); // Refresh list
  } catch (error) {
    // 如果是用户取消，则error为'cancel'，不需要提示
    if (error !== 'cancel') {
      console.error('删除失败:', error);
      ElMessage.error('删除失败');
    }
  }
}

onMounted(() => {
  getList();
});

</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    span {
      font-size: 20px;
      font-weight: bold;
    }
  }

  .el-alert {
    margin-top: 20px;

    p {
      margin: 5px 0;
    }
  }

  .table-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 20px;

    .toolbar-left-group {
      display: flex;
      align-items: center;
      gap: 10px;
      flex-wrap: wrap;
    }
  }

  .project-info {
    display: flex;
    align-items: center;
  }

  .project-thumbnail {
    width: 128px;
    height: 72px;
    background-color: #f0f2f5;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;

    .image-slot {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      background: var(--el-fill-color-light);
      color: var(--el-text-color-secondary);
      font-size: 30px;
    }
  }
}
</style>