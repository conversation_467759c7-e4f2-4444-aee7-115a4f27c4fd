<template>
  <div class="template-factory-container">
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span>模板工厂</span>
        </div>
      </template>

      <el-alert title="帮助信息" type="info" show-icon :closable="false" class="info-alert">
        <p>1. 普通模板：通过云剪辑工程导出；</p>
        <p>2. 高级模板：通过云剪辑web sdk制作上传，导出后上传到控制台，高级模板制作和使用。</p>
      </el-alert>

      <el-tabs v-model="activeTab">
        <el-tab-pane label="普通模板" name="normal">
          <div class="tab-content">
            <div class="actions-bar">
              <el-form :model="queryParams" :inline="true">
                <el-form-item>
                  <el-input v-model="queryParams.keyword" placeholder="请输入模板ID或名称" class="search-input" clearable
                    @clear="handleQuery" @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item>
                  <el-select v-model="queryParams.status" placeholder="模板状态" clearable>
                    <el-option label="可用" value="Available"></el-option>
                    <el-option label="不可用" value="Unavailable"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handleQuery">
                    <el-icon>
                      <Search />
                    </el-icon>
                    搜索
                  </el-button>
                  <el-button @click="resetQuery">
                    <el-icon>
                      <Refresh />
                    </el-icon>
                    重置
                  </el-button>
                </el-form-item>
              </el-form>
              <div class="action-buttons">
                <el-button type="primary" @click="createTemplate">创建模板</el-button>
              </div>
            </div>

            <el-table v-loading="loading" :data="templateList" @selection-change="handleSelectionChange"
              style="width: 100%">
              <el-table-column type="selection" width="55" />
              <el-table-column label="模板" min-width="250">
                <template #default="{ row }">
                  <div class="template-info">
                    <div class="template-cover">
                      <el-image v-if="row.CoverURL" :src="row.CoverURL" fit="cover" class="template-image" />
                      <el-icon v-else>
                        <Film />
                      </el-icon>
                    </div>
                    <div class="template-details">
                      <div class="template-name">{{ row.Name }}</div>
                      <div class="template-id">ID: {{ row.TemplateId }}</div>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="Status" label="状态" width="120">
                <template #default="{ row }">
                  <el-tag :type="row.Status === 'Available' ? 'success' : 'info'">{{ row.Status === 'Available' ? '可用' :
                    '不可用' }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="CreateSource" label="创建方式" width="150" />
              <el-table-column prop="CreationTime" label="创建时间" width="180" />
              <el-table-column prop="ModifiedTime" label="更新时间" width="180" />
              <el-table-column label="操作" width="240" fixed="right">
                <template #default="{ row }">
                  <el-button link type="primary" @click="useTemplate(row)">使用模板</el-button>
                  <el-button link type="primary" @click="editTemplate(row)">编辑模板</el-button>
                  <el-button link type="danger" @click="deleteTemplate(row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>

            <el-pagination v-if="total > 0" v-model:current-page="queryParams.pageNo"
              v-model:page-size="queryParams.pageSize" :page-sizes="[10, 20, 50, 100]" :total="total"
              layout="total, sizes, prev, pager, next, jumper" @size-change="fetchTemplateList"
              @current-change="fetchTemplateList" class="pagination-container" />
          </div>
        </el-tab-pane>

        <el-tab-pane label="高级模板" name="advanced">
          <div class="tab-content">
            <el-empty description="当前没有高级模板">
              <el-button type="primary">上传模板</el-button>
            </el-empty>
          </div>
        </el-tab-pane>
      </el-tabs>

    </el-card>

    <!-- 创建模板弹窗 -->
    <el-dialog v-model="dialogVisible" title="创建新模板" width="600px" :close-on-click-modal="false">
      <el-form :model="templateForm" label-width="100px" v-loading="formLoading">
        <el-form-item label="模板名称" required>
          <el-input v-model="templateForm.Name" placeholder="请输入模板名称" />
        </el-form-item>
        <el-form-item label="模板类型">
          <el-select v-model="templateForm.Type" disabled>
            <el-option label="普通模板" value="Timeline"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="模板内容 (Config)" prop="Config">
          <ConfigBuilder v-model:config="templateForm.Config" />
        </el-form-item>
        <el-form-item label="封面URL">
          <el-upload class="cover-uploader" action="#" :http-request="handleCoverUpload" :show-file-list="false"
            :before-upload="beforeCoverUpload" list-type="picture-card">
            <img v-if="templateForm.CoverUrl" :src="templateForm.CoverUrl" class="cover-image" />
            <el-icon v-else class="el-icon-plus cover-uploader-icon"></el-icon>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmCreate" :loading="formLoading">确认创建</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, toRefs } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { UploadRawFile, UploadRequestOptions } from 'element-plus';
import { Search, Refresh, Film } from '@element-plus/icons-vue';
import { listTemplates, addTemplate, getTemplate, deleteTemplate as deleteTemplateApi } from '../api/template';
import { uploadFileUnified } from '@/api/file/info.js';
import type { TemplateInfo, ListTemplatesRequest, AddTemplateRequest } from '../types/template';
import ConfigBuilder from './components/ConfigBuilder.vue';
import { useRouter } from 'vue-router';
import { ElLoading } from 'element-plus';

const activeTab = ref('normal');
const loading = ref(false);
const templateList = ref<TemplateInfo[]>([]);
const multipleSelection = ref<TemplateInfo[]>([]);
const total = ref(0);

// --- 新增：创建模板弹窗相关 ---
const dialogVisible = ref(false);
const formLoading = ref(false);
const templateForm = ref<AddTemplateRequest>({});

const data = reactive<{
  queryParams: ListTemplatesRequest
}>({
  queryParams: {
    pageNo: 1,
    pageSize: 10,
    keyword: '',
    status: '',
    type: 'Timeline', // 默认只查询Timeline类型的模板
  }
});

const { queryParams } = toRefs(data);
const router = useRouter();

const fetchTemplateList = async () => {
  loading.value = true;
  try {
    const res = await listTemplates(queryParams.value);
    if (res.code === 200) {
      templateList.value = res.data.Templates;
      total.value = res.data.TotalCount;
    } else {
      ElMessage.error(`获取模板列表失败: ${res.msg}`);
      templateList.value = [];
      total.value = 0;
    }
  } catch (error) {
    ElMessage.error('获取模板列表时发生错误');
    templateList.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

const handleQuery = () => {
  queryParams.value.pageNo = 1;
  fetchTemplateList();
};

const resetQuery = () => {
  queryParams.value.pageNo = 1;
  queryParams.value.keyword = '';
  queryParams.value.status = '';
  fetchTemplateList();
};

const createTemplate = () => {
  templateForm.value = {
    Type: 'Timeline', // 默认类型
    Name: '',
    Config: '',
    CoverUrl: ''
  };
  dialogVisible.value = true;
};

const handleConfirmCreate = async () => {
  if (!templateForm.value.Name || !templateForm.value.Config) {
    ElMessage.warning('模板名称和Config为必填项');
    return;
  }
  formLoading.value = true;
  try {
    const res = await addTemplate(templateForm.value);
    if (res.code === 200) {
      ElMessage.success('模板创建成功');
      dialogVisible.value = false;
      fetchTemplateList(); // 刷新列表
    } else {
      ElMessage.error(`创建失败: ${res.msg}`);
    }
  } catch (error) {
    ElMessage.error('创建模板时发生网络错误');
  } finally {
    formLoading.value = false;
  }
}

const beforeCoverUpload = (rawFile: UploadRawFile) => {
  const isImage = rawFile.type.startsWith('image/');
  if (!isImage) {
    ElMessage.error('请上传图片格式文件!');
    return false;
  }
  const isLt512K = rawFile.size / 1024 < 512;
  if (!isLt512K) {
    ElMessage.error('上传封面图片大小不能超过 512KB!');
    return false;
  }
  return true;
};

const handleCoverUpload = async (options: UploadRequestOptions) => {
  const { file } = options;
  try {
    // TODO: 请根据您的实际情况修改 storageType 和 clientName
    const storageType = 'oss';
    const clientName = 'default';
    const res = await uploadFileUnified({ storageType, clientName, file });

    // 假设您的上传接口返回结构为 { code: 200, data: { url: '...' } }
    if (res.code === 200 && res.data && res.data.url) {
      templateForm.value.CoverUrl = res.data.url;
      ElMessage.success('封面上传成功');
    } else {
      ElMessage.error(res.msg || '封面上传失败');
    }
  } catch (error) {
    console.error("上传失败", error);
    ElMessage.error('封面上传时发生网络错误');
  }
};

const useTemplate = (row: TemplateInfo) => {
  ElMessage.info(`使用模板: ${row.Name}`);
};

const editTemplate = async (row: TemplateInfo) => {
  if (!row.TemplateId) {
    ElMessage.error('模板ID缺失，无法编辑。');
    return;
  }

  const loadingInstance = ElLoading.service({
    lock: true, // 全屏锁定
    text: '正在加载模板详情，请稍候...',
    background: 'rgba(0, 0, 0, 0.7)',
  });

  try {
    // 调用 getTemplate API 获取模板详细信息
    const res = await getTemplate(row.TemplateId);
    if (res.code === 200 && res.data?.Template) {
      // 成功获取模板详情后，跳转到视频编辑页
      router.push({
        path: `/tool/matrix-mix/video-edit/${row.TemplateId}`, // 修改为完整路径
        query: { from: 'template-factory' } // 添加查询参数，告知来源是模板工厂
      });
      ElMessage.success(`正在跳转到模板 [${row.Name}] 编辑页`);
    } else {
      ElMessage.error(`获取模板 [${row.Name}] 详情失败: ${res.msg || '未知错误'}`);
    }
  } catch (error) {
    console.error('获取模板详情时发生错误:', error);
    ElMessage.error(`加载模板 [${row.Name}] 详情时发生网络错误`);
  } finally {
    loadingInstance.close(); // 关闭加载提示
  }
};

const deleteTemplate = (row: TemplateInfo) => {
  ElMessageBox.confirm(`确定要删除模板 "${row.Name}" 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      // 调用 API 删除模板，templateIds 参数是逗号分隔的字符串
      const res = await deleteTemplateApi({ templateIds: row.TemplateId });
      if (res.code === 200) {
        ElMessage.success('删除成功');
        fetchTemplateList(); // 刷新列表
      } else {
        ElMessage.error(`删除失败: ${res.msg || '未知错误'}`);
      }
    } catch (error) {
      console.error('删除模板时发生错误:', error);
      ElMessage.error('删除模板时发生网络错误');
    }
  }).catch(() => {
    ElMessage.info('已取消删除');
  });
};

const handleSelectionChange = (val: TemplateInfo[]) => {
  multipleSelection.value = val;
};


onMounted(() => {
  fetchTemplateList();
});
</script>

<style lang="scss" scoped>
.template-factory-container {
  padding: 20px;
}

.card-header {
  font-size: 18px;
  font-weight: bold;
}

.info-alert {
  margin-bottom: 20px;

  p {
    margin: 0;
    line-height: 1.6;
  }
}

.tab-content {
  .actions-bar {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;

    .search-input {
      width: 300px;
    }
  }

  .template-info {
    display: flex;
    align-items: center;

    .template-cover {
      width: 48px;
      height: 48px;
      background-color: #f0f2f5;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
      font-size: 24px;
      color: #909399;
      overflow: hidden; // 确保图片不超出圆角

      .template-image {
        width: 100%;
        height: 100%;
      }
    }

    .template-details {
      .template-name {
        font-weight: 500;
      }

      .template-id {
        font-size: 12px;
        color: #909399;
      }
    }
  }
}

.pagination-container {
  margin-top: 20px;
}
</style>

<style>
/* 将 el-upload 的样式提升为全局，确保能覆盖 */
.cover-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.cover-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.cover-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 146px;
  /* 148px - 2px border */
  height: 146px;
  text-align: center;
  line-height: 146px;
}

.cover-image {
  width: 146px;
  height: 146px;
  display: block;
}
</style>