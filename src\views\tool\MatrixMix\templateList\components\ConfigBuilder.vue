<template>
  <div class="config-builder">
    <el-form label-position="top">
      <el-form-item label="选择模板预设类型">
        <el-select v-model="selectedPreset" placeholder="请选择一个预设来开始配置" @change="onPresetChange">
          <el-option label="视频添加水印" value="watermark"></el-option>
          <el-option label="视频轨道整体静音" value="mute"></el-option>
          <el-option label="增加片头片尾" value="opening_ending"></el-option>
        </el-select>
      </el-form-item>

      <!-- 动态表单区域 -->
      <div v-if="selectedPreset">
        <p class="preset-description">{{ descriptions[selectedPreset] }}</p>

        <!-- 水印模板表单 -->
        <div v-if="selectedPreset === 'watermark'">
          <el-form-item label="水印图片ID (ImageId)">
            <el-input v-model="watermarkForm.imageId" placeholder="请输入水印素材的MediaId"></el-input>
          </el-form-item>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="宽度 (Width)">
                <el-input v-model="watermarkForm.width" placeholder="例如: 200"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
               <el-form-item label="高度 (Height)">
                <el-input v-model="watermarkForm.height" placeholder="例如: 60"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
           <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="X坐标 (X)">
                <el-input v-model="watermarkForm.x" placeholder="默认值: 40"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
               <el-form-item label="Y坐标 (Y)">
                <el-input v-model="watermarkForm.y" placeholder="默认值: 40"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 片头片尾模板表单 -->
        <div v-if="selectedPreset === 'opening_ending'">
           <el-form-item label="片头视频ID (MediaId)">
            <el-input v-model="openingEndingForm.openingMediaId" placeholder="请输入片头视频素材的MediaId"></el-input>
          </el-form-item>
           <el-form-item label="片尾视频ID (MediaId)">
            <el-input v-model="openingEndingForm.endingMediaId" placeholder="请输入片尾视频素材的MediaId"></el-input>
          </el-form-item>
        </div>

      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, reactive, onMounted } from 'vue';

// --- Props & Emits ---
const props = defineProps<{
  config?: string;
}>();

const emit = defineEmits<{
  (e: 'update:config', value: string): void;
}>();


// --- 内部状态 ---
type PresetType = 'watermark' | 'mute' | 'opening_ending' | '';
const selectedPreset = ref<PresetType>('');

// --- 表单数据模型 ---
const watermarkForm = reactive({ imageId: '', width: '200', height: '60', x: '40', y: '40' });
const openingEndingForm = reactive({ openingMediaId: '', endingMediaId: '' });

const descriptions: Record<Exclude<PresetType, ''>, string> = {
  watermark: '此预设将在主视频轨道上添加一个图片水印。主视频素材ID需在合成时通过 ClipsParam 的 "$Video" 字段传入。',
  mute: '此预设将对所有传入的视频进行静音处理。视频素材列表需在合成时通过 ClipsParam 的 "$VideoArray" 字段传入。',
  opening_ending: '此预设将在视频素材列表的开始和结束位置分别插入一个固定的片头和片尾视频。'
};


// --- 方法 ---

const onPresetChange = () => {
  // 清理旧的表单数据(如果需要)
  // ...
  generateConfig();
};

const generateConfig = () => {
  let configObject: object = {};

  switch (selectedPreset.value) {
    case 'watermark':
      configObject = {
        VideoTracks: [{ VideoTrackClips: [{ MediaId: "$Video" }] }],
        ImageTracks: [{
          ImageTrackClips: [{
            ImageId: watermarkForm.imageId,
            Width: Number(watermarkForm.width) || undefined,
            Height: Number(watermarkForm.height) || undefined,
            X: `$X:${watermarkForm.x || 40}`,
            Y: `$Y:${watermarkForm.y || 40}`,
            TimelineIn: "$TimelineIn:0",
            TimelineOut: "$TimelineOut:NULL"
          }]
        }]
      };
      break;
    
    case 'mute':
      configObject = {
        VideoTracks: [{
          VideoTrackClips: [{
            Sys_Type: "ArrayItems",
            Sys_ArrayObject: "$VideoArray",
            Sys_Template: {
              MediaId: "$MediaId",
              Effects: [{ Type: "Volume", Gain: "0" }]
            }
          }]
        }]
      };
      break;

    case 'opening_ending':
      configObject = {
        VideoTracks: [{
          VideoTrackClips: [
            { MediaId: openingEndingForm.openingMediaId },
            {
              Sys_Type: "ArrayItems",
              Sys_ArrayObject: "$VideoArray",
              Sys_Template: { MediaId: "$MediaId" }
            },
            { MediaId: openingEndingForm.endingMediaId }
          ]
        }]
      };
      break;
  }
  
  // 将最终的对象转换为格式化的JSON字符串并发出
  emit('update:config', JSON.stringify(configObject, null, 2));
};

// 监听所有表单变化，自动更新Config
watch([() => props.config, selectedPreset, watermarkForm, openingEndingForm], () => {
  // 当外部的config变化时，需要尝试解析它并填充表单
  // 这个逻辑将在后续实现（用于编辑功能）
  // 目前，我们只确保内部变化能正确生成config
  generateConfig();
}, { deep: true });

onMounted(() => {
  // 初始化时生成一次
  generateConfig();
})

</script>

<style lang="scss" scoped>
.config-builder {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 20px;
}
.preset-description {
  font-size: 13px;
  color: #909399;
  margin-top: -10px;
  margin-bottom: 20px;
}
</style> 