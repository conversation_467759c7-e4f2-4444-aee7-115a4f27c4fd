/**
 * 批量智能一键成片相关类型定义
 * 基于阿里云ICE SubmitBatchMediaProducingJob API
 */

// ==================== 基础类型定义 ====================

/**
 * 贴纸配置
 */
export interface Sticker {
  /** 贴纸媒体ID */
  MediaId?: string;
  /** 贴纸OSS URL */
  MediaURL?: string;
  /** X坐标 */
  X: number;
  /** Y坐标 */
  Y: number;
  /** 宽度 */
  Width: number;
  /** 高度 */
  Height: number;
  /** 透明度，0-1 */
  Opacity?: number;
}

/**
 * 副标题配置
 */
export interface SubHeading {
  /** 副标题级别 */
  Level: number;
  /** 副标题数组 */
  TitleArray: string[];
}

/**
 * 时间范围配置
 */
export interface TimeRange {
  /** 开始时间 */
  In: string;
  /** 结束时间 */
  Out: string;
}

/**
 * 媒体元数据配置
 */
export interface MediaMetaData {
  /** 媒体ID或URL */
  Media: string;
  /** 分组名称 */
  GroupName: string;
  /** 时间范围列表 */
  TimeRangeList: TimeRange[];
}

/**
 * 媒体组配置
 */
export interface MediaGroup {
  /** 分组名称，不超过50个字符，不支持emoji */
  GroupName: string;
  /** 素材列表，支持mediaId或url，最多200个 */
  MediaArray: string[];
  /** 口播文案数组，分组口播模式使用 */
  SpeechTextArray?: string[];
  /** 当前分组对应的时长，单位秒，仅限SpeechTextArray为空时填写 */
  Duration?: number;
  /** 拆条模式：NoSplit(不拆条)、AverageSplit(自动拆条) */
  SplitMode?: 'NoSplit' | 'AverageSplit';
  /** 输入视频的音量，取值[0, 10.0] */
  Volume?: number;
}

// ==================== InputConfig 配置 ====================

/**
 * 输入配置
 */
export interface InputConfig {
  /** 媒体组数组，脚本化自动成片模式 */
  MediaGroupArray: MediaGroup[];
  /** 标题数组，每次合成随机选一个，最多50个，每个不超过50字 */
  TitleArray?: string[];
  /** 副标题设置 */
  SubHeadingArray?: SubHeading[];
  /** 口播文案数组，全局口播模式使用，最多50个，每条最长1000字符 */
  SpeechTextArray?: string[];
  /** 贴纸数组，每次合成随机选一个，最多50个 */
  StickerArray?: Sticker[];
  /** 背景音乐数组，每次合成随机选一个，最多50个 */
  BackgroundMusicArray?: string[];
  /** 背景图片数组，每次合成随机选择一个，最多50个 */
  BackgroundImageArray?: string[];
}

// ==================== EditingConfig 配置 ====================

/**
 * 媒体配置
 */
export interface MediaConfig {
  /** 输入视频素材音量 */
  Volume?: number;
  /** 媒体元数据数组 */
  MediaMetaDataArray?: MediaMetaData[];
}

/**
 * 标题配置
 */
export interface TitleConfig {
  /** 对齐方式 */
  Alignment?: string;
  /** 自适应模式 */
  AdaptMode?: string;
  /** 字体 */
  Font?: string;
  /** 尺寸请求类型 */
  SizeRequestType?: string;
  /** Y坐标 */
  Y?: number;
}

/**
 * 副标题配置
 */
export interface SubHeadingConfig {
  /** 级别配置，key为Level，value为配置对象 */
  [level: string]: {
    Y?: number;
    FontSize?: number;
  };
}

/**
 * 特殊词汇配置
 */
export interface SpecialWordsConfig {
  /** 类型：Highlight(高亮)、Forbidden(禁用) */
  Type: 'Highlight' | 'Forbidden';
  /** 样式配置 */
  Style?: {
    FontName?: string;
    FontSize?: number;
    FontColor?: string;
    OutlineColour?: string;
    Outline?: number;
    FontFace?: {
      Bold?: boolean;
      Underline?: boolean;
      Italic?: boolean;
    };
  };
  /** 词汇列表 */
  WordsList: string[];
  /** 声音替换模式，仅Forbidden类型有效 */
  SoundReplaceMode?: string;
}

/**
 * ASR配置
 */
export interface AsrConfig {
  /** 对齐方式 */
  Alignment?: string;
  /** 自适应模式 */
  AdaptMode?: string;
  /** 字体 */
  Font?: string;
  /** 尺寸请求类型 */
  SizeRequestType?: string;
  /** 间距 */
  Spacing?: number;
  /** Y坐标 */
  Y?: number;
}

/**
 * 口播配置
 */
export interface SpeechConfig {
  /** 音量 */
  Volume?: number;
  /** 语速 */
  SpeechRate?: number;
  /** 音色 */
  Voice?: string | null;
  /** 风格 */
  Style?: string | null;
  /** 人声克隆voiceId */
  CustomizedVoice?: string | null;
  /** ASR配置 */
  AsrConfig?: AsrConfig;
  /** 特殊词汇配置，仅在分组口播模式下生效 */
  SpecialWordsConfig?: SpecialWordsConfig[];
}

/**
 * 背景音乐配置
 */
export interface BackgroundMusicConfig {
  /** 音量 */
  Volume?: number;
  /** 风格 */
  Style?: string | null;
}

/**
 * 背景图片配置
 */
export interface BackgroundImageConfig {
  /** 子类型 */
  SubType?: string;
  /** 半径 */
  Radius?: number;
}

/**
 * 处理配置
 */
export interface ProcessConfig {
  /** 单镜头时长，单位秒 */
  SingleShotDuration?: number;
  /** 是否允许添加特效效果 */
  AllowVfxEffect?: boolean;
  /** 特效应用概率，取值0.0-1.0 */
  VfxEffectProbability?: number;
  /** 第一个片段特效列表 */
  VfxFirstClipEffectList?: string[];
  /** 非第一个片段特效列表 */
  VfxNotFirstClipEffectList?: string[];
  /** 是否允许添加转场效果 */
  AllowTransition?: boolean;
  /** 转场时长，单位秒 */
  TransitionDuration?: number;
  /** 自定义转场效果列表 */
  TransitionList?: string[];
  /** 单个成片中是否使用一致的转场效果 */
  UseUniformTransition?: boolean;
  /** 是否允许添加自定义滤镜 */
  AllowFilter?: boolean;
  /** 自定义滤镜效果列表 */
  FilterList?: string[];
  /** 对齐模式，仅在全局口播模式下生效：AutoSpeed(视频轨道时长按照音频轨道缩放)、Cut(视频轨道时长按照音频轨道截断) */
  AlignmentMode?: 'AutoSpeed' | 'Cut';
  /** 图片素材的持续时长，单位秒 */
  ImageDuration?: number;
}

/**
 * 前端画布配置
 */
export interface FECanvas {
  /** 宽度 */
  Width: number;
  /** 高度 */
  Height: number;
}

/**
 * 普通剪辑合成配置
 */
export interface ProduceConfig {
  /** 自动注册输入VOD媒体 */
  AutoRegisterInputVodMedia?: boolean;
  /** 输出WebM透明通道 */
  OutputWebmTransparentChannel?: boolean;
  /** 封面配置 */
  CoverConfig?: {
    StartTime?: number;
  };
  /** 音频通道复制 */
  AudioChannelCopy?: string;
  /** 管道ID */
  PipelineId?: string;
  /** 最大比特率 */
  MaxBitrate?: number;
  /** 保持原始最大比特率 */
  KeepOriginMaxBitrate?: boolean;
  /** 保持原始视频最大帧率 */
  KeepOriginVideoMaxFps?: boolean;
}

/**
 * 剪辑配置
 */
export interface EditingConfig {
  /** 输入视频素材相关配置 */
  MediaConfig?: MediaConfig;
  /** 标题相关配置 */
  TitleConfig?: TitleConfig;
  /** 多级副标题相关配置 */
  SubHeadingConfig?: SubHeadingConfig;
  /** 口播文案相关配置 */
  SpeechConfig?: SpeechConfig;
  /** 背景音乐相关配置 */
  BackgroundMusicConfig?: BackgroundMusicConfig;
  /** 背景图相关配置 */
  BackgroundImageConfig?: BackgroundImageConfig;
  /** 混剪处理配置 */
  ProcessConfig?: ProcessConfig;
  /** 前端页面预览时的画布配置 */
  FECanvas?: FECanvas;
  /** 普通剪辑合成配置 */
  ProduceConfig?: ProduceConfig;
}

// ==================== OutputConfig 配置 ====================

/**
 * 视频配置
 */
export interface VideoConfig {
  /** 视频质量，CRF值 */
  Crf?: number;
  /** 编码格式 */
  Codec?: string;
}

/**
 * 输出配置
 */
export interface OutputConfig {
  /** 输出视频地址，必须要有占位符{index} */
  MediaURL?: string;
  /** 指定输出到VOD的媒资文件存储地址 */
  StorageLocation?: string;
  /** 输出文件名称，必须要有占位符{index} */
  FileName?: string;
  /** 仅生成预览用的时间线，不实际合成 */
  GeneratePreviewOnly?: boolean;
  /** 输出视频数，数量上限为100 */
  Count?: number;
  /** 输出视频单片时长上限，单位秒 */
  MaxDuration?: number;
  /** 输出视频单片的固定时长，单位秒 */
  FixedDuration?: number;
  /** 成片宽度，px */
  Width: number;
  /** 成片高度，px */
  Height: number;
  /** 输出视频流相关配置 */
  Video?: VideoConfig;
}

// ==================== UserData 配置 ====================

/**
 * 用户数据配置
 */
export interface UserData {
  /** 回调通知地址 */
  NotifyAddress?: string;
  /** 其他用户自定义数据 */
  [key: string]: any;
}

// ==================== TemplateConfig 配置 ====================

/**
 * 模板配置
 */
export interface TemplateConfig {
  /** 模板ID */
  TemplateId?: string;
  /** 模板名称 */
  TemplateName?: string;
  /** 其他模板相关配置 */
  [key: string]: any;
}

// ==================== 请求参数类型 ====================

/**
 * 提交批量智能一键成片任务请求参数
 */
export interface SubmitBatchMediaProducingJobRequest {
  /** 调用方保证请求幂等性 Client Token */
  ClientToken?: string;
  /** 输入配置 */
  InputConfig: string; // JSON字符串
  /** 剪辑相关配置 */
  EditingConfig?: string; // JSON字符串
  /** 输出配置 */
  OutputConfig: string; // JSON字符串
  /** 用户业务配置、回调配置 */
  UserData?: string; // JSON字符串
  /** 模板配置 */
  TemplateConfig?: string; // JSON字符串
}

/**
 * 提交批量智能一键成片任务响应
 */
export interface SubmitBatchMediaProducingJobResponse {
  /** 请求ID */
  RequestId: string;
  /** 批量智能一键成片作业ID */
  JobId: string;
}

// ==================== 工具类型 ====================

/**
 * 处理模式枚举
 */
export enum ProcessingMode {
  /** 全局口播模式 */
  GLOBAL_SPEECH = 'global_speech',
  /** 分组口播模式 */
  GROUP_SPEECH = 'group_speech'
}

/**
 * 任务状态枚举
 */
export enum JobStatus {
  /** 初始化 */
  INIT = 'Init',
  /** 处理中 */
  PROCESSING = 'Processing',
  /** 已完成 */
  FINISHED = 'Finished',
  /** 失败 */
  FAILED = 'Failed'
}

/**
 * 任务类型枚举
 */
export enum JobType {
  /** 脚本化 */
  SCRIPT = 'Script',
  /** 智能混剪 */
  SMART_MIX = 'Smart_Mix'
}

/**
 * 拆条模式枚举
 */
export enum SplitMode {
  /** 不拆条 */
  NO_SPLIT = 'NoSplit',
  /** 自动拆条 */
  AVERAGE_SPLIT = 'AverageSplit'
}

/**
 * 对齐模式枚举
 */
export enum AlignmentMode {
  /** 视频轨道时长按照音频轨道缩放 */
  AUTO_SPEED = 'AutoSpeed',
  /** 视频轨道时长按照音频轨道截断 */
  CUT = 'Cut'
}

/**
 * 特殊词汇类型枚举
 */
export enum SpecialWordsType {
  /** 高亮 */
  HIGHLIGHT = 'Highlight',
  /** 禁用 */
  FORBIDDEN = 'Forbidden'
}

// ==================== 配置构建器 ====================

/**
 * 配置构建器工具类
 */
export class BatchProducingConfigBuilder {
  /**
   * 构建InputConfig
   */
  static buildInputConfig(config: InputConfig): string {
    return JSON.stringify(config);
  }

  /**
   * 构建EditingConfig
   */
  static buildEditingConfig(config: EditingConfig): string {
    return JSON.stringify(config);
  }

  /**
   * 构建OutputConfig
   */
  static buildOutputConfig(config: OutputConfig): string {
    return JSON.stringify(config);
  }

  /**
   * 构建UserData
   */
  static buildUserData(config: UserData): string {
    return JSON.stringify(config);
  }

  /**
   * 构建TemplateConfig
   */
  static buildTemplateConfig(config: TemplateConfig): string {
    return JSON.stringify(config);
  }

  /**
   * 解析InputConfig
   */
  static parseInputConfig(configStr: string): InputConfig {
    return JSON.parse(configStr);
  }

  /**
   * 解析EditingConfig
   */
  static parseEditingConfig(configStr: string): EditingConfig {
    return JSON.parse(configStr);
  }

  /**
   * 解析OutputConfig
   */
  static parseOutputConfig(configStr: string): OutputConfig {
    return JSON.parse(configStr);
  }

  /**
   * 解析UserData
   */
  static parseUserData(configStr: string): UserData {
    return JSON.parse(configStr);
  }

  /**
   * 解析TemplateConfig
   */
  static parseTemplateConfig(configStr: string): TemplateConfig {
    return JSON.parse(configStr);
  }

  /**
   * 判断处理模式
   */
  static getProcessingMode(inputConfig: InputConfig): ProcessingMode {
    // 当SpeechTextArray不为空，则视为"全局口播模式"
    if (inputConfig.SpeechTextArray && inputConfig.SpeechTextArray.length > 0) {
      return ProcessingMode.GLOBAL_SPEECH;
    }

    // 当SpeechTextArray为空，且MediaGroupArray中只要有一个MediaGroup.Duration或MediaGroup.SpeechTextArray不为空，则视为"分组口播模式"
    if (inputConfig.MediaGroupArray) {
      for (const group of inputConfig.MediaGroupArray) {
        if (group.Duration || (group.SpeechTextArray && group.SpeechTextArray.length > 0)) {
          return ProcessingMode.GROUP_SPEECH;
        }
      }
    }

    // 当SpeechTextArray为空，且MediaGroupArray中所有的MediaGroup.Duration和MediaGroup.SpeechTextArray皆为空，则视为"全局口播模式"
    return ProcessingMode.GLOBAL_SPEECH;
  }

  /**
   * 验证配置
   */
  static validateConfig(inputConfig: InputConfig, outputConfig: OutputConfig): string[] {
    const errors: string[] = [];

    // 验证InputConfig
    if (!inputConfig.MediaGroupArray || inputConfig.MediaGroupArray.length === 0) {
      errors.push('MediaGroupArray不能为空');
    }

    if (inputConfig.MediaGroupArray && inputConfig.MediaGroupArray.length > 40) {
      errors.push('MediaGroupArray最多支持40个分组');
    }

    for (const group of inputConfig.MediaGroupArray || []) {
      if (!group.GroupName || group.GroupName.length > 50) {
        errors.push('GroupName不能为空且不超过50个字符');
      }
      if (!group.MediaArray || group.MediaArray.length === 0) {
        errors.push('MediaArray不能为空');
      }
      if (group.MediaArray && group.MediaArray.length > 200) {
        errors.push('每个分组的MediaArray最多支持200个素材');
      }
    }

    // 验证OutputConfig
    if (!outputConfig.GeneratePreviewOnly) {
      if (!outputConfig.MediaURL && !outputConfig.StorageLocation) {
        errors.push('当GeneratePreviewOnly=false时，必须提供MediaURL或StorageLocation');
      }
      if (outputConfig.StorageLocation && !outputConfig.FileName) {
        errors.push('当输出到VOD时，必须提供FileName');
      }
    }

    if (outputConfig.Count && (outputConfig.Count < 1 || outputConfig.Count > 100)) {
      errors.push('Count必须在1-100之间');
    }

    if (outputConfig.MaxDuration && outputConfig.FixedDuration) {
      errors.push('MaxDuration和FixedDuration只能二选一');
    }

    return errors;
  }
}

// ==================== 默认配置 ====================

/**
 * 默认InputConfig配置
 */
export const DEFAULT_INPUT_CONFIG: InputConfig = {
  MediaGroupArray: [],
  TitleArray: [],
  SpeechTextArray: [],
  StickerArray: [],
  BackgroundMusicArray: [],
  BackgroundImageArray: []
};

/**
 * 默认EditingConfig配置
 */
export const DEFAULT_EDITING_CONFIG: EditingConfig = {
  MediaConfig: {
    Volume: 0
  },
  TitleConfig: {
    Alignment: 'TopCenter',
    AdaptMode: 'AutoWrap',
    Font: 'Alibaba PuHuiTi 2.0 95 ExtraBold',
    SizeRequestType: 'Nominal',
    Y: 0.1
  },
  SpeechConfig: {
    Volume: 1,
    SpeechRate: 0,
    Voice: null,
    Style: null,
    CustomizedVoice: null,
    AsrConfig: {
      Alignment: 'TopCenter',
      AdaptMode: 'AutoWrap',
      Font: 'Alibaba PuHuiTi 2.0 65 Medium',
      SizeRequestType: 'Nominal',
      Spacing: -1,
      Y: 0.8
    }
  },
  BackgroundMusicConfig: {
    Volume: 0.2,
    Style: null
  },
  ProcessConfig: {
    SingleShotDuration: 3,
    AllowVfxEffect: false,
    AllowTransition: false,
    AlignmentMode: 'AutoSpeed'
  }
};

/**
 * 默认OutputConfig配置
 */
export const DEFAULT_OUTPUT_CONFIG: OutputConfig = {
  Count: 1,
  MaxDuration: 15,
  Width: 1080,
  Height: 1920,
  Video: {
    Crf: 27
  },
  GeneratePreviewOnly: false
};

/**
 * 默认UserData配置
 */
export const DEFAULT_USER_DATA: UserData = {
  NotifyAddress: ''
};
