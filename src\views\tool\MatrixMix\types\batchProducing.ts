/**
 * 批量智能一键成片相关类型定义
 * 基于阿里云ICE SubmitBatchMediaProducingJob API 官方文档
 *
 * 支持功能：
 * - 脚本化自动成片
 * - 智能图文匹配成片（通用场景版、影视集锦版）
 * - 体育赛事集锦成片
 * - 高燃混剪成片
 */

// ==================== 基础类型定义 ====================

/**
 * 贴纸配置
 * 每次合成随机选一个，最多50个
 */
export interface Sticker {
  /** 贴纸媒体ID */
  MediaId?: string;
  /** 贴纸OSS URL */
  MediaURL?: string;
  /** X坐标 */
  X: number;
  /** Y坐标 */
  Y: number;
  /** 宽度 */
  Width: number;
  /** 高度 */
  Height: number;
  /** 透明度，0-1 */
  Opacity?: number;
}

/**
 * 副标题配置
 */
export interface SubHeading {
  /** 副标题级别 */
  Level: number;
  /** 副标题数组 */
  TitleArray: string[];
}

/**
 * 媒体组配置
 * 脚本化自动成片模式，最多40个分组，每组最多200个素材
 */
export interface MediaGroup {
  /** 分组名称，不超过50个字符，不支持emoji */
  GroupName: string;
  /** 素材列表，支持媒资ID或OSS URL，最多200个素材 */
  MediaArray: string[];
  /** 口播文案数组，分组口播模式使用，最多50个，每条最长1000字符 */
  SpeechTextArray?: string[];
  /** 当前分组对应的时长，单位秒，仅限SpeechTextArray为空时填写 */
  Duration?: number;
  /** 拆条模式：NoSplit(不拆条)、AverageSplit(自动拆条) */
  SplitMode?: 'NoSplit' | 'AverageSplit';
  /** 输入视频的音量，取值[0, 10.0]，支持小数点后两位 */
  Volume?: number;
}

// ==================== InputConfig 配置 ====================

/**
 * 输入配置
 */
export interface InputConfig {
  /** 媒体组数组，脚本化自动成片模式 */
  MediaGroupArray: MediaGroup[];
  /** 标题数组，每次合成随机选一个，最多50个，每个不超过50字 */
  TitleArray?: string[];
  /** 副标题设置 */
  SubHeadingArray?: SubHeading[];
  /** 口播文案数组，全局口播模式使用，最多50个，每条最长1000字符 */
  SpeechTextArray?: string[];
  /** 贴纸数组，每次合成随机选一个，最多50个 */
  StickerArray?: Sticker[];
  /** 背景音乐数组，每次合成随机选一个，最多50个 */
  BackgroundMusicArray?: string[];
  /** 背景图片数组，每次合成随机选择一个，最多50个 */
  BackgroundImageArray?: string[];
}

// ==================== EditingConfig 配置 ====================

/**
 * 媒体配置
 */
export interface MediaConfig {
  /** 输入视频素材音量，取值[0, 10.0] */
  Volume?: number;
}

/**
 * 标题配置
 * 支持配置字幕参数
 */
export interface TitleConfig {
  /** 对齐方式：TopCenter、BottomCenter等 */
  Alignment?: string;
  /** 自适应模式：AutoWrap等 */
  AdaptMode?: string;
  /** 字体：Alibaba PuHuiTi 2.0 95 ExtraBold等 */
  Font?: string;
  /** 尺寸请求类型：Nominal等 */
  SizeRequestType?: string;
  /** Y坐标，竖屏默认0.1，横屏默认0.05，方屏默认0.08 */
  Y?: number;
  /** 字体大小 */
  FontSize?: number;
}

/**
 * 副标题配置
 * 多级副标题相关配置，key为Level，value为横幅文字配置
 */
export interface SubHeadingConfig {
  [level: string]: {
    /** Y坐标 */
    Y?: number;
    /** 字体大小 */
    FontSize?: number;
  };
}

/**
 * ASR字幕配置
 */
export interface AsrConfig {
  /** 对齐方式：TopCenter、BottomCenter等 */
  Alignment?: string;
  /** 自适应模式：AutoWrap等 */
  AdaptMode?: string;
  /** 字体：Alibaba PuHuiTi 2.0 65 Medium等 */
  Font?: string;
  /** 尺寸请求类型：Nominal等 */
  SizeRequestType?: string;
  /** 间距 */
  Spacing?: number;
  /** Y坐标，竖屏默认0.8，横屏默认0.9，方屏默认0.85 */
  Y?: number;
}

/**
 * 口播配置
 */
export interface SpeechConfig {
  /** 音量，默认1.0 */
  Volume?: number;
  /** 语速，默认0 */
  SpeechRate?: number;
  /** 音色 */
  Voice?: string | null;
  /** 风格 */
  Style?: string | null;
  /** 人声克隆voiceId，若填写了此字段，Voice和Style将失效 */
  CustomizedVoice?: string | null;
  /** ASR字幕配置 */
  AsrConfig?: AsrConfig;
}

/**
 * 背景音乐配置
 */
export interface BackgroundMusicConfig {
  /** 音量 */
  Volume?: number;
  /** 风格 */
  Style?: string | null;
}

/**
 * 背景图片配置
 */
export interface BackgroundImageConfig {
  /** 子类型 */
  SubType?: string;
  /** 半径 */
  Radius?: number;
}

/**
 * 混剪处理配置
 */
export interface ProcessConfig {
  /** 长视频素材进行剪辑时会自动拆条，拆条后单镜头的时长，单位秒，默认3 */
  SingleShotDuration?: number;
  /** 是否允许添加特效效果，默认false */
  AllowVfxEffect?: boolean;
  /** 特效应用在每个视频片段上的概率，取值0.0-1.0，默认0.5 */
  VfxEffectProbability?: number;
  /** 是否允许添加转场效果，默认false */
  AllowTransition?: boolean;
  /** 转场时长，单位秒，默认0.5秒 */
  TransitionDuration?: number;
  /** 单个成片中是否使用一致的转场效果，默认true */
  UseUniformTransition?: boolean;
  /** 是否允许添加自定义滤镜，默认false */
  AllowFilter?: boolean;
  /** 对齐模式，仅在全局口播模式下生效：AutoSpeed(视频轨道时长按照音频轨道缩放)、Cut(视频轨道时长按照音频轨道截断) */
  AlignmentMode?: 'AutoSpeed' | 'Cut';
  /** 图片素材的持续时长，单位秒，默认2 */
  ImageDuration?: number;
}

/**
 * 剪辑配置
 * 用户可通过配置EditingConfig，指定成片素材的音量、位置及其他合成参数
 */
export interface EditingConfig {
  /** 输入视频素材相关配置 */
  MediaConfig?: MediaConfig;
  /** 标题相关配置，支持配置字幕参数 */
  TitleConfig?: TitleConfig;
  /** 多级副标题相关配置 */
  SubHeadingConfig?: SubHeadingConfig;
  /** 口播文案相关配置 */
  SpeechConfig?: SpeechConfig;
  /** 背景音乐相关配置 */
  BackgroundMusicConfig?: BackgroundMusicConfig;
  /** 背景图相关配置 */
  BackgroundImageConfig?: BackgroundImageConfig;
  /** 混剪处理配置 */
  ProcessConfig?: ProcessConfig;
}

// ==================== OutputConfig 配置 ====================

/**
 * 视频配置
 */
export interface VideoConfig {
  /** 视频质量，CRF值 */
  Crf?: number;
  /** 编码格式 */
  Codec?: string;
}

/**
 * 输出配置
 * 用户可通过配置OutputConfig，指定成片输出地址、名称规则、成片的宽高、输出成片数量等合成参数
 */
export interface OutputConfig {
  /** 输出视频地址，必须要有占位符{index}，当GeneratePreviewOnly=false时且成片输出到OSS时必填 */
  MediaURL?: string;
  /** 指定输出到VOD的媒资文件存储地址，当GeneratePreviewOnly=false时且成片输出到VOD时必填 */
  StorageLocation?: string;
  /** 输出文件名称，必须要有占位符{index}，当GeneratePreviewOnly=false时且成片输出到VOD时必填 */
  FileName?: string;
  /** 仅生成预览用的时间线，不实际合成，默认false */
  GeneratePreviewOnly?: boolean;
  /** 输出视频数，数量上限为100，默认1 */
  Count?: number;
  /** 输出视频单片时长上限，单位秒，默认15。FixedDuration和MaxDuration只能二选一 */
  MaxDuration?: number;
  /** 输出视频单片的固定时长，单位秒。FixedDuration和MaxDuration只能二选一 */
  FixedDuration?: number;
  /** 成片宽度，px */
  Width: number;
  /** 成片高度，px */
  Height: number;
  /** 输出视频流相关配置 */
  Video?: VideoConfig;
}

// ==================== UserData 配置 ====================

/**
 * 用户数据配置
 */
export interface UserData {
  /** 回调通知地址 */
  NotifyAddress?: string;
  /** 其他用户自定义数据 */
  [key: string]: any;
}

// ==================== TemplateConfig 配置 ====================

/**
 * 模板配置
 */
export interface TemplateConfig {
  /** 模板ID */
  TemplateId?: string;
  /** 模板名称 */
  TemplateName?: string;
  /** 其他模板相关配置 */
  [key: string]: any;
}

// ==================== API 请求响应类型 ====================

/**
 * 提交批量智能一键成片任务响应
 */
export interface SubmitBatchMediaProducingJobResponse {
  /** 请求ID */
  RequestId: string;
  /** 批量智能一键成片作业ID */
  JobId: string;
}

/**
 * 获取批量智能一键成片任务详情响应
 */
export interface GetBatchMediaProducingJobResponse {
  /** 请求ID */
  RequestId: string;
  /** 作业ID */
  JobId: string;
  /** 任务状态 */
  Status: string;
  /** 任务详细信息 */
  [key: string]: any;
}

/**
 * 获取批量智能一键成片任务列表响应
 */
export interface ListBatchMediaProducingJobsResponse {
  /** 请求ID */
  RequestId: string;
  /** 任务列表 */
  JobList: any[];
  /** 下一页标记 */
  NextToken?: string;
}

// ==================== 常用枚举类型 ====================

/**
 * 拆条模式
 */
export type SplitMode = 'NoSplit' | 'AverageSplit';

/**
 * 对齐模式（仅在全局口播模式下生效）
 */
export type AlignmentMode = 'AutoSpeed' | 'Cut';

/**
 * 任务状态
 */
export type JobStatus = 'Init' | 'Processing' | 'Finished' | 'Failed';

// ==================== 工具函数 ====================

/**
 * 判断处理模式
 * 根据官方文档规则判断是全局口播模式还是分组口播模式
 */
export function getProcessingMode(inputConfig: InputConfig): 'global' | 'group' {
  // 当SpeechTextArray不为空，则视为"全局口播模式"
  if (inputConfig.SpeechTextArray && inputConfig.SpeechTextArray.length > 0) {
    return 'global';
  }

  // 当SpeechTextArray为空，且MediaGroupArray中只要有一个MediaGroup.Duration或MediaGroup.SpeechTextArray不为空，则视为"分组口播模式"
  if (inputConfig.MediaGroupArray) {
    for (const group of inputConfig.MediaGroupArray) {
      if (group.Duration || (group.SpeechTextArray && group.SpeechTextArray.length > 0)) {
        return 'group';
      }
    }
  }

  // 当SpeechTextArray为空，且MediaGroupArray中所有的MediaGroup.Duration和MediaGroup.SpeechTextArray皆为空，则视为"全局口播模式"
  return 'global';
}

// ==================== 默认配置 ====================

/**
 * 默认剪辑配置
 */
export const DEFAULT_EDITING_CONFIG: EditingConfig = {
  MediaConfig: {
    Volume: 0 // 默认视频素材静音
  },
  SpeechConfig: {
    Volume: 1, // 口播音频默认用原始音量
    SpeechRate: 0,
    AsrConfig: {
      Alignment: 'BottomCenter',
      AdaptMode: 'AutoWrap',
      Font: 'Alibaba PuHuiTi 2.0 65 Medium',
      SizeRequestType: 'Nominal',
      Spacing: -1,
      Y: 0.8
    }
  },
  BackgroundMusicConfig: {
    Volume: 0.2 // 背景音乐默认用20%音量
  },
  ProcessConfig: {
    SingleShotDuration: 3, // 拆条后的镜头时长
    AllowVfxEffect: false, // 是否添加特效效果
    AllowTransition: false, // 是否添加转场效果
    AlignmentMode: 'AutoSpeed' // 仅在全局口播模式下支持此字段
  }
};

/**
 * 默认输出配置
 */
export const DEFAULT_OUTPUT_CONFIG: OutputConfig = {
  Count: 1,
  MaxDuration: 15,
  Width: 1080,
  Height: 1920,
  Video: {
    Crf: 27
  },
  GeneratePreviewOnly: false
};
