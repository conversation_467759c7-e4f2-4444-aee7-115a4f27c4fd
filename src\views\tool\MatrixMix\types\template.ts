/**
 * @file template.ts
 * @description
 * 该文件包含了所有与模板相关的 TypeScript 类型定义。
 * 这些类型用于创建、列出和检索模板详情时的 API 请求和响应。
 * 它们的结构基于官方的 API 文档。
 */

// =========================================================================
// 主模板信息字段内部的详细数据结构类型
// =========================================================================

/**
 * 模板 Config 中，视频轨道上的一个片段。
 */
export interface TemplateVideoTrackClip {
  /**
   * 媒体ID。在模板中，这可以是一个变量，如 "$Video"，在合成时会被替换。
   */
  MediaId?: string;
  /**
   * 片段标题，通常是媒体文件名。
   */
  Title?: string;
  /**
   * 是否可以被替换的模板素材标记。
   */
  TemplateReplaceable?: boolean;
  /**
   * 模板素材的唯一ID，用于在 ClipsParam 中查找对应的值。
   */
  TemplateMaterialId?: string;
  /**
   * 模板素材的备注信息。
   */
  TemplateRemark?: string;
  /**
   * 系统字段，值为 'ArrayItems' 时表示这是一个数组参数模板。
   */
  Sys_Type?: 'ArrayItems';
  /**
   * 数组参数的变量名，如 "$VideoArray"。合成时会查找 ClipsParam 中同名的数组。
   */
  Sys_ArrayObject?: string;
  /**
   * 用于遍历数组中每一项的子模板。
   */
  Sys_Template?: {
    /**
     * 子模板中的媒体ID变量，如 "$MediaId"。
     */
    MediaId?: string;
    /**
     * 应用于每个片段的特效。
     */
    Effects?: any[];
  };
  /**
   * 允许其他任意属性。
   */
  [key: string]: any;
}

/**
 * 模板 Config 中的视频轨道。
 */
export interface TemplateVideoTrack {
  /**
   * 视频轨道上的片段数组。
   */
  VideoTrackClips: TemplateVideoTrackClip[];
  ImageTrackClips: TemplateImageTrackClip[];
}

/**
 * 模板 Config 中，图片轨道上的一个片段。
 */
export interface TemplateImageTrackClip {
  /**
   * 图片素材的媒体ID。
   */
  ImageId: string;
  /**
   * 图片宽度。可包含默认值，如 "$Width:200"。
   */
  Width?: number | string;
  /**
   * 图片高度。可包含默认值，如 "$Height:60"。
   */
  Height?: number | string;
  /**
   * 图片水平位置。可包含默认值，如 "$X:40"。
   */
  X?: number | string;
  /**
   * 图片垂直位置。可包含默认值，如 "$Y:40"。
   */
  Y?: number | string;
  /**
   * 在时间线上的入点。可包含默认值，如 "$TimelineIn:0"。
   */
  TimelineIn?: number | string;
  /**
   * 在时间线上的出点。可包含默认值，如 "$TimelineOut:NULL"。
   */
  TimelineOut?: number | string;
}

/**
 * 模板 Config 中的图片轨道。
 */
export interface TemplateImageTrack {
  /**
   * 图片轨道上的片段数组。
   */
  ImageTrackClips: TemplateImageTrackClip[];
}

/**
 * 'Config' JSON字符串内部的完整数据结构。
 * 这是一个类时间线对象，其值可以是模板变量（以'$'开头的字符串）。
 */
export interface TemplateConfig {
  /**
   * 视频轨道定义。
   */
  VideoTracks?: TemplateVideoTrack[];
  /**
   * 图片轨道定义。
   */
  ImageTracks?: TemplateImageTrack[];
  /**
   * 其他轨道类型，如音频、字幕等。
   */
  [key: string]: any;
}

/**
 * 'ClipsParam' JSON字符串内部的数据结构。
 * 用于在提交合成任务时，替换Config中的变量。
 */
export type ClipsParamData = Record<string, any>;

/**
 * 'RelatedMediaids' JSON字符串内部的数据结构。
 * 定义了模板所关联的各类媒体素材ID。
 */
export type RelatedMediaidsData = Record<string, string[]>;

/**
 * ClipsParam JSON字符串内部的数据结构。
 * 用于在提交合成任务时，替换Config中的变量。
 * 键通常是 TemplateMaterialId，值是实际的 MediaId。
 */
export type ClipsParamMap = Record<string, string>;

/**
 * 描述模板可变素材的接口，用于在前端展示。
 */
export interface Material {
    /** 素材名称 */
    name: string;
    /** 素材类型，如 "视频", "图片", "文本" 等 */
    type: string;
    /** 是否可以替换 */
    replace?: boolean;
    /** 素材的ID命名，对应 TemplateMaterialId */
    idNum: string;
    /** 备注信息 */
    remark?: string;
    /** 操作，例如编辑、预览等，具体值待定 */
    action?: string;
    /** 高级设置，用于存储从 ClipsParam 解析出的素材扩展属性 */
    advancedSettings?: Record<string, any>;
}

// =========================================================================
// 用于模板操作的主要 API 类型
// =========================================================================

/**
 * API响应中一个模板的完整信息。
 */
export interface TemplateInfo {
  /**
   * 模板的唯一ID。
   */
  TemplateId: string;
  /**
   * 模板名称。
   */
  Name: string;
  /**
   * 模板类型，如 'Timeline' 或 'VETemplate'。
   */
  Type: string;
  /**
   * 模板配置（一个JSON字符串）。
   * 其内部结构由 TemplateConfig 定义。
   * @see TemplateConfig
   */
  Config: string;
  /**
   * 模板封面的URL。
   */
  CoverURL: string;
  /**
   * 用于预览的媒体ID。
   */
  PreviewMedia: string;
  /**
   * 模板状态 (例如: 'Available', 'Processing')。
   */
  Status: string;
  /**
   * 模板的创建来源 (例如: 'OpenAPI', 'WebSDK')。
   */
  CreateSource: string;
  /**
   * 模板的最后修改来源。
   */
  ModifiedSource: string;
  /**
   * 预览媒体的状态 (例如: 'Normal', 'Preparing')。
   */
  PreviewMediaStatus?: string;
  /**
   * 创建时间 (ISO 8601格式)。
   */
  CreationTime?: string;
  /**
   * 最后修改时间 (ISO 8601格式)。
   */
  ModifiedTime?: string;
  /**
   * 提交合成任务时，用于替换模板变量的参数模板（一个JSON字符串）。
   * @see ClipsParamData
   */
  ClipsParam?: string;
  /**
   * 模板关联的媒体素材ID（一个JSON字符串），供编辑器使用。
   * @see RelatedMediaidsData
   */
  RelatedMediaids?: string;
}

/**
 * `addTemplate` 接口的完整响应。
 */
export interface AddTemplateResponse {
  /**
   * 请求的唯一ID。
   */
  RequestId: string;
  /**
   * 创建成功后返回的模板信息。
   */
  Template: TemplateInfo;
}

/**
 * `addTemplate` 接口的请求体。
 */
export interface AddTemplateRequest {
  /**
   * 模板名称。
   */
  Name?: string;
  /**
   * 模板类型。
   */
  Type?: 'Timeline' | 'VETemplate';
  /**
   * 模板配置（JSON字符串）。
   */
  Config?: string;
  /**
   * 模板封面URL。
   */
  CoverUrl?: string;
  /**
   * 用于预览的媒体ID。
   */
  PreviewMedia?: string;
  /**
   * 模板状态。
   */
  Status?: string;
  /**
   * 创建来源。
   */
  Source?: string;
  /**
   * 关联的媒体ID（JSON字符串）。
   */
  RelatedMediaids?: string;
  /**
   * 模板变量，用于高级模板。
   */
  Variables?: Record<string, any>;
}

/**
 * `listTemplates` 接口的请求参数。
 */
export interface ListTemplatesRequest {
  /**
   * 分页页码。
   */
  pageNo?: number;
  /**
   * 每页数量。
   */
  pageSize?: number;
  /**
   * 按类型筛选。
   */
  type?: string;
  /**
   * 按状态筛选。
   */
  status?: string;
  /**
   * 按创建来源筛选。
   */
  createSource?: string;
  /**
   * 用于搜索的关键字。
   */
  keyword?: string;
  /**
   * 排序方式。
   */
  sortType?: string;
}

/**
 * `listTemplates` 接口的完整响应。
 */
export interface ListTemplatesResponse {
  /**
   * 请求的唯一ID。
   */
  RequestId: string;
  /**
   * 符合条件的总数。
   */
  TotalCount: number;
  /**
   * 当前页的模板列表。
   */
  Templates: TemplateInfo[];
}

/**
 * `updateTemplate` 接口的请求体。
 * 注意：虽然官方文档中 TemplateId 标记为"否"（非必填），但实际修改操作通常需要 TemplateId 来唯一标识待修改的模板。
 * 因此在此接口中将其定义为必填。
 * 其他字段为可选，表示可以只传入需要修改的字段。
 */
export interface UpdateTemplateRequest {
  TemplateId: string; // 逻辑上必填，用于指定要更新的模板
  Name?: string;
  Config?: string; // JSON字符串
  CoverUrl?: string;
  PreviewMedia?: string;
  Status?: 'Available' | 'Created' | 'Uploading' | 'Processing' | 'UploadFailed' | 'ProcessFailed';
  Source?: 'AliyunConsole' | 'OpenAPI' | 'WebSDK';
  RelatedMediaids?: string; // JSON字符串
  // Variables 字段在 updateTemplate 接口文档中未提及，故不在此处包含
}

/**
 * `updateTemplate` 接口的响应。
 */
export interface UpdateTemplateResponse {
  RequestId: string;
}

/**
 * `deleteTemplate` 接口的请求体。
 */
export interface DeleteTemplateRequest {
  /**
   * 需要删除的模板 ID，多个 ID 用半角逗号（,）隔开。
   * 示例: "id1,id2,id3"
   */
  templateIds: string;
}

/**
 * `deleteTemplate` 接口的响应。
 */
export interface DeleteTemplateResponse {
  /**
   * 请求 ID。
   */
  RequestId: string;
}

// =========================================================================
// GetTemplateMaterials API 类型定义
// =========================================================================

/**
 * `getTemplateMaterials` 接口的请求参数。
 */
export interface GetTemplateMaterialsRequest {
  /**
   * 模板 ID。
   */
  TemplateId: string;
  
  /**
   * 可选。所需文件列表，JSON格式字符串。
   * 不填则默认返回全部素材地址，最多返回400个。
   * 示例: ["music.mp3","config.json","assets/1.jpg"]
   */
  FileList?: string;
}

/**
 * `getTemplateMaterials` 接口的响应。
 */
export interface GetTemplateMaterialsResponse {
  /**
   * 请求 ID。
   */
  RequestId: string;
  
  /**
   * 关联素材地址映射表。
   * 键为文件名，值为对应的临时访问URL（30分钟过期）。
   */
  MaterialUrls: Record<string, string>;
}

/**
 * `getTemplateMaterials` 接口的响应类型（用于 API 调用）
 */
export type GetTemplateMaterialsResult = Promise<GetTemplateMaterialsResponse>;

/**
 * 模板素材文件信息
 */
export interface TemplateMaterialFile {
  /**
   * 文件名
   */
  fileName: string;
  
  /**
   * 文件的临时访问URL（30分钟过期）
   */
  fileUrl: string;
  
  /**
   * 文件类型（根据文件扩展名推断）
   */
  mediaType: 'video' | 'audio' | 'image';
}