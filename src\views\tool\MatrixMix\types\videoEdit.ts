export interface VideoEditProject {
  ProjectId: string;
  Title: string;
  Status: string;
  CreateTime?: string;
  ModifiedTime?: string;
  CoverURL?: string;
  Description?: string;
  Timeline?: any; // Consider defining a detailed Timeline interface later
  Duration?: number;
  TemplateId?: string;
  ClipsParam?: string;
  TemplateType?: string;
  ProjectType?: string;
  BusinessConfig?: any;
  BusinessStatus?: string;
  TimelineConvertStatus?: string;
  TimelineConvertErrorMessage?: string;
  CreateSource?: string;
  ModifiedSource?: string;
  ProduceMediaId?: string;
}

export interface VideoEditListParams {
  pageNum: number;
  pageSize: number;
  keyword?: string;
  status?: string;
  nextToken?: string;
}

export interface VideoEditApiResponse {
  RequestId: string;
  NextToken?: string;
  MaxResults: number;
  ProjectList: VideoEditProject[];
  TotalCount?: number; // 假设后端会添加这个字段用于分页
}

export interface VideoEditDetailApiResponse {
  Project: VideoEditProject;
  RequestId?: string;
}

export interface TimelineClip {
  MediaId: string;
  Type: string;
  TimelineIn: number;
  TimelineOut: number;
  FileName?: string;
  Content?: string;
  [key: string]: any; // 其他可能的属性，如 Effects, Content等
}

export interface VideoTrack {
  VideoTrackClips: TimelineClip[];
}

export interface AudioTrack {
  AudioTrackClips: TimelineClip[];
}

export interface SubtitleTrack {
  SubtitleTrackClips: TimelineClip[];
}

export interface ImageTrack {
  ImageTrackClips: TimelineClip[];
}

export interface Timeline {
  VideoTracks?: VideoTrack[];
  AudioTracks?: AudioTrack[];
  SubtitleTracks?: SubtitleTrack[];
  ImageTracks?: ImageTrack[];
  [key: string]: any; // 其他可能的顶层属性
}

export interface CreateEditingProjectDTO {
  Title: string;
  Description?: string;
  Timeline?: Timeline;
}

/**
 * @interface SelectedClip
 * @description 用于表示时间轴上当前选中的素材片段的信息。
 * @property {('video' | 'audio' | 'subtitle' | null)} type - 选中片段的轨道类型，可以是 'video'、'audio'、'subtitle' 或 null（如果未选中任何片段）。
 * @property {number | null} index - 选中片段在其所属轨道数组中的索引，如果未选中则为 null。
 */
export interface SelectedClip {
  type: 'video' | 'audio' | 'subtitle' | null;
  index: number | null;
}

/**
 * @interface RulerMarker
 * @description 定义时间标尺上每个刻度标记的数据结构。
 * @property {number} second - 该标记所代表的秒数。
 * @property {boolean} showLabel - 是否在该标记处显示时间文本标签。
 */
export interface RulerMarker {
  second: number;
  showLabel: boolean;
}