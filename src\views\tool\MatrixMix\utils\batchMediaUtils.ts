/**
 * @file batchMediaUtils.ts
 * @description 批量获取媒资信息的工具函数集合
 *              包含媒资ID处理、参数验证、数据格式化、状态检查等通用功能
 *              用于简化批量媒资信息查询和处理流程
 */

import type {
  BatchGetMediaInfosRequest,
  BatchGetMediaInfosResponse,
  BatchMediaInfo
} from '../types/media';
import {
  AdditionType,
  MediaType,
  TranscodeStatus
} from '../types/media';
import { formatFileSize, formatDuration, validateMediaId } from './commonUtils';

/**
 * 生成客户端Token，用于保证请求幂等性
 * @returns 客户端Token
 */
export function generateClientToken(): string {
  return `batch_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

/**
 * 批量获取媒资信息的工具类
 */
export class BatchMediaInfoUtils {
  /**
   * 构建MediaIds字符串
   * @param mediaIds 媒资ID数组
   * @returns 逗号分隔的媒资ID字符串
   */
  static buildMediaIds(mediaIds: string[]): string {
    return mediaIds.join(',');
  }

  /**
   * 解析MediaIds字符串
   * @param mediaIdsStr 逗号分隔的媒资ID字符串
   * @returns 媒资ID数组
   */
  static parseMediaIds(mediaIdsStr: string): string[] {
    return mediaIdsStr.split(',').filter(id => id.trim() !== '');
  }

  /**
   * 构建AdditionType字符串
   * @param types 额外信息类型数组
   * @returns 逗号分隔的额外信息类型字符串
   */
  static buildAdditionType(types: AdditionType[]): string {
    return types.join(',');
  }

  /**
   * 解析AdditionType字符串
   * @param additionTypeStr 逗号分隔的额外信息类型字符串
   * @returns 额外信息类型数组
   */
  static parseAdditionType(additionTypeStr: string): AdditionType[] {
    return additionTypeStr.split(',').map(type => type.trim() as AdditionType);
  }

  /**
   * 验证媒资ID格式
   * @param mediaId 媒资ID
   * @returns 是否有效
   */
  static validateMediaId(mediaId: string): boolean {
    return validateMediaId(mediaId);
  }

  /**
   * 验证批量请求参数
   * @param request 请求参数
   * @returns 验证错误信息数组
   */
  static validateBatchRequest(request: BatchGetMediaInfosRequest): string[] {
    const errors: string[] = [];

    if (!request.MediaIds) {
      errors.push('MediaIds不能为空');
    } else {
      const mediaIds = this.parseMediaIds(request.MediaIds);
      if (mediaIds.length === 0) {
        errors.push('MediaIds格式无效');
      } else if (mediaIds.length > 100) {
        errors.push('单次查询的媒资数量不能超过100个');
      }

      // 验证每个媒资ID的格式
      for (const mediaId of mediaIds) {
        if (!this.validateMediaId(mediaId)) {
          errors.push(`媒资ID格式无效: ${mediaId}`);
        }
      }
    }

    if (request.AuthTimeout !== undefined) {
      if (request.AuthTimeout < 0 || request.AuthTimeout > 9007199254740991) {
        errors.push('AuthTimeout必须在0-9007199254740991之间');
      }
    }

    return errors;
  }

  /**
   * 从响应中提取媒资信息
   * @param response 批量获取媒资信息响应
   * @param mediaId 指定的媒资ID
   * @returns 指定的媒资信息，如果不存在则返回undefined
   */
  static getMediaInfoById(response: BatchGetMediaInfosResponse, mediaId: string): BatchMediaInfo | undefined {
    return response.MediaInfos.find(info => info.MediaId === mediaId);
  }

  /**
   * 从响应中提取多个媒资信息
   * @param response 批量获取媒资信息响应
   * @param mediaIds 媒资ID数组
   * @returns 媒资信息映射，key为媒资ID
   */
  static getMediaInfosByIds(response: BatchGetMediaInfosResponse, mediaIds: string[]): Record<string, BatchMediaInfo | undefined> {
    const result: Record<string, BatchMediaInfo | undefined> = {};
    
    for (const mediaId of mediaIds) {
      result[mediaId] = this.getMediaInfoById(response, mediaId);
    }
    
    return result;
  }

  /**
   * 检查媒资是否包含文件信息
   * @param mediaInfo 媒资信息
   * @returns 是否包含文件信息
   */
  static hasFileInfo(mediaInfo: BatchMediaInfo): boolean {
    return mediaInfo.FileInfoList !== undefined && mediaInfo.FileInfoList.length > 0;
  }

  /**
   * 获取媒资的第一个文件信息
   * @param mediaInfo 媒资信息
   * @returns 第一个文件信息，如果不存在则返回undefined
   */
  static getFirstFileInfo(mediaInfo: BatchMediaInfo) {
    return this.hasFileInfo(mediaInfo) ? mediaInfo.FileInfoList![0] : undefined;
  }

  /**
   * 获取媒资的时长（秒）
   * @param mediaInfo 媒资信息
   * @returns 时长，如果无法获取则返回0
   */
  static getDuration(mediaInfo: BatchMediaInfo): number {
    const fileInfo = this.getFirstFileInfo(mediaInfo);
    return fileInfo?.FileBasicInfo.Duration || 0;
  }

  /**
   * 获取媒资的文件大小（字节）
   * @param mediaInfo 媒资信息
   * @returns 文件大小，如果无法获取则返回0
   */
  static getFileSize(mediaInfo: BatchMediaInfo): number {
    const fileInfo = this.getFirstFileInfo(mediaInfo);
    return fileInfo?.FileBasicInfo.FileSize || 0;
  }

  /**
   * 获取媒资的分辨率
   * @param mediaInfo 媒资信息
   * @returns 分辨率对象，如果无法获取则返回null
   */
  static getResolution(mediaInfo: BatchMediaInfo): { width: number; height: number } | null {
    const fileInfo = this.getFirstFileInfo(mediaInfo);
    if (fileInfo?.FileBasicInfo.Width && fileInfo?.FileBasicInfo.Height) {
      return {
        width: fileInfo.FileBasicInfo.Width,
        height: fileInfo.FileBasicInfo.Height
      };
    }
    return null;
  }

  /**
   * 格式化文件大小
   * @param bytes 字节数
   * @returns 格式化后的文件大小字符串
   */
  static formatFileSize(bytes: number): string {
    return formatFileSize(bytes);
  }

  /**
   * 格式化时长
   * @param seconds 秒数
   * @returns 格式化后的时长字符串 (HH:MM:SS)
   */
  static formatDuration(seconds: number): string {
    return formatDuration(seconds);
  }

  /**
   * 检查媒资类型
   * @param mediaInfo 媒资信息
   * @param mediaType 要检查的媒体类型
   * @returns 是否为指定类型
   */
  static isMediaType(mediaInfo: BatchMediaInfo, mediaType: MediaType): boolean {
    return mediaInfo.MediaBasicInfo.MediaType === mediaType;
  }

  /**
   * 检查媒资是否为视频类型
   * @param mediaInfo 媒资信息
   * @returns 是否为视频
   */
  static isVideo(mediaInfo: BatchMediaInfo): boolean {
    return mediaInfo.MediaBasicInfo.MediaType === 'Video';
  }

  /**
   * 检查媒资是否为图片类型
   * @param mediaInfo 媒资信息
   * @returns 是否为图片
   */
  static isImage(mediaInfo: BatchMediaInfo): boolean {
    return mediaInfo.MediaBasicInfo.MediaType === 'Image';
  }

  /**
   * 检查媒资是否为音频类型
   * @param mediaInfo 媒资信息
   * @returns 是否为音频
   */
  static isAudio(mediaInfo: BatchMediaInfo): boolean {
    return mediaInfo.MediaBasicInfo.MediaType === 'Audio';
  }

  /**
   * 检查媒资是否为文本类型
   * @param mediaInfo 媒资信息
   * @returns 是否为文本
   */
  static isText(mediaInfo: BatchMediaInfo): boolean {
    return mediaInfo.MediaBasicInfo.MediaType === 'Text';
  }

  /**
   * 获取媒资的转码状态
   * @param mediaInfo 媒资信息
   * @returns 转码状态
   */
  static getTranscodeStatus(mediaInfo: BatchMediaInfo): TranscodeStatus {
    return mediaInfo.MediaBasicInfo.TranscodeStatus as TranscodeStatus;
  }

  /**
   * 检查媒资是否转码完成
   * @param mediaInfo 媒资信息
   * @returns 是否转码完成
   */
  static isTranscodeCompleted(mediaInfo: BatchMediaInfo): boolean {
    return this.getTranscodeStatus(mediaInfo) === TranscodeStatus.TRANSCODE_SUCCESS;
  }

  /**
   * 检查媒资是否转码失败
   * @param mediaInfo 媒资信息
   * @returns 是否转码失败
   */
  static isTranscodeFailed(mediaInfo: BatchMediaInfo): boolean {
    return this.getTranscodeStatus(mediaInfo) === TranscodeStatus.TRANSCODE_FAILED;
  }

  /**
   * 检查媒资是否正在转码
   * @param mediaInfo 媒资信息
   * @returns 是否正在转码
   */
  static isTranscoding(mediaInfo: BatchMediaInfo): boolean {
    return this.getTranscodeStatus(mediaInfo) === TranscodeStatus.TRANSCODING;
  }
}

/**
 * 媒资信息缓存管理器
 */
export class MediaInfoCache {
  private cache = new Map<string, BatchMediaInfo>();
  private cacheTime = new Map<string, number>();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存

  /**
   * 获取媒资信息（优先从缓存获取）
   */
  async getMediaInfo(mediaId: string, fetchFunction: (mediaIds: string[]) => Promise<BatchGetMediaInfosResponse>): Promise<BatchMediaInfo | null> {
    // 检查缓存
    if (this.isCacheValid(mediaId)) {
      return this.cache.get(mediaId) || null;
    }

    // 从API获取
    try {
      const response = await fetchFunction([mediaId]);
      const mediaInfo = response.MediaInfos.find(info => info.MediaId === mediaId);
      
      if (mediaInfo) {
        this.setCache(mediaId, mediaInfo);
        return mediaInfo;
      }
    } catch (error) {
      console.error('获取媒资信息失败:', error);
    }

    return null;
  }

  /**
   * 批量获取媒资信息（智能缓存）
   */
  async getBatchMediaInfo(mediaIds: string[], fetchFunction: (mediaIds: string[]) => Promise<BatchGetMediaInfosResponse>): Promise<BatchMediaInfo[]> {
    const uncachedIds: string[] = [];
    const cachedResults: BatchMediaInfo[] = [];

    // 检查缓存
    for (const mediaId of mediaIds) {
      if (this.isCacheValid(mediaId)) {
        const cached = this.cache.get(mediaId);
        if (cached) {
          cachedResults.push(cached);
        }
      } else {
        uncachedIds.push(mediaId);
      }
    }

    // 获取未缓存的媒资信息
    if (uncachedIds.length > 0) {
      try {
        const response = await fetchFunction(uncachedIds);
        
        for (const mediaInfo of response.MediaInfos) {
          this.setCache(mediaInfo.MediaId, mediaInfo);
          cachedResults.push(mediaInfo);
        }
      } catch (error) {
        console.error('批量获取媒资信息失败:', error);
      }
    }

    return cachedResults;
  }

  private isCacheValid(mediaId: string): boolean {
    const cacheTime = this.cacheTime.get(mediaId);
    if (!cacheTime) return false;
    
    return Date.now() - cacheTime < this.CACHE_DURATION;
  }

  private setCache(mediaId: string, mediaInfo: BatchMediaInfo): void {
    this.cache.set(mediaId, mediaInfo);
    this.cacheTime.set(mediaId, Date.now());
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache.clear();
    this.cacheTime.clear();
  }

  /**
   * 清除过期缓存
   */
  clearExpiredCache(): void {
    const now = Date.now();
    for (const [mediaId, cacheTime] of this.cacheTime.entries()) {
      if (now - cacheTime >= this.CACHE_DURATION) {
        this.cache.delete(mediaId);
        this.cacheTime.delete(mediaId);
      }
    }
  }
}

/**
 * 媒资信息统计分析器
 */
export class MediaInfoAnalyzer {
  /**
   * 分析媒资集合的统计信息
   */
  static analyzeMediaCollection(mediaInfos: BatchMediaInfo[]) {
    const stats = {
      total: mediaInfos.length,
      byType: {
        video: 0,
        image: 0,
        audio: 0,
        text: 0
      },
      byTranscodeStatus: {
        completed: 0,
        failed: 0,
        processing: 0,
        init: 0
      },
      totalDuration: 0,
      totalSize: 0,
      resolutionStats: {
        '1920x1080': 0,
        '1280x720': 0,
        '3840x2160': 0,
        other: 0
      }
    };

    mediaInfos.forEach(mediaInfo => {
      // 按类型统计
      if (BatchMediaInfoUtils.isVideo(mediaInfo)) {
        stats.byType.video++;
      } else if (BatchMediaInfoUtils.isImage(mediaInfo)) {
        stats.byType.image++;
      } else if (BatchMediaInfoUtils.isAudio(mediaInfo)) {
        stats.byType.audio++;
      } else if (BatchMediaInfoUtils.isText(mediaInfo)) {
        stats.byType.text++;
      }

      // 按转码状态统计
      if (BatchMediaInfoUtils.isTranscodeCompleted(mediaInfo)) {
        stats.byTranscodeStatus.completed++;
      } else if (BatchMediaInfoUtils.isTranscodeFailed(mediaInfo)) {
        stats.byTranscodeStatus.failed++;
      } else if (BatchMediaInfoUtils.isTranscoding(mediaInfo)) {
        stats.byTranscodeStatus.processing++;
      } else {
        stats.byTranscodeStatus.init++;
      }

      // 累计时长和大小
      stats.totalDuration += BatchMediaInfoUtils.getDuration(mediaInfo);
      stats.totalSize += BatchMediaInfoUtils.getFileSize(mediaInfo);

      // 分辨率统计
      const resolution = BatchMediaInfoUtils.getResolution(mediaInfo);
      if (resolution) {
        const resKey = `${resolution.width}x${resolution.height}`;
        if (stats.resolutionStats.hasOwnProperty(resKey)) {
          stats.resolutionStats[resKey as keyof typeof stats.resolutionStats]++;
        } else {
          stats.resolutionStats.other++;
        }
      }
    });

    return stats;
  }

  /**
   * 生成媒资集合报告
   */
  static generateReport(mediaInfos: BatchMediaInfo[]): string {
    const stats = this.analyzeMediaCollection(mediaInfos);
    
    return `
媒资集合分析报告
================

基本信息:
- 总数量: ${stats.total} 个媒资
- 总时长: ${BatchMediaInfoUtils.formatDuration(stats.totalDuration)}
- 总大小: ${BatchMediaInfoUtils.formatFileSize(stats.totalSize)}

类型分布:
- 视频: ${stats.byType.video} 个
- 图片: ${stats.byType.image} 个
- 音频: ${stats.byType.audio} 个
- 文本: ${stats.byType.text} 个

转码状态:
- 转码完成: ${stats.byTranscodeStatus.completed} 个
- 转码失败: ${stats.byTranscodeStatus.failed} 个
- 转码中: ${stats.byTranscodeStatus.processing} 个
- 初始化: ${stats.byTranscodeStatus.init} 个

分辨率分布:
- 1920x1080: ${stats.resolutionStats['1920x1080']} 个
- 1280x720: ${stats.resolutionStats['1280x720']} 个
- 3840x2160: ${stats.resolutionStats['3840x2160']} 个
- 其他: ${stats.resolutionStats.other} 个
    `.trim();
  }
}

// 导出类型定义，方便其他模块使用
export type {
  BatchGetMediaInfosRequest,
  BatchGetMediaInfosResponse,
  BatchMediaInfo
}; 