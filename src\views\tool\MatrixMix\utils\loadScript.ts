export function loadScript(src: string): Promise<void> {
  return new Promise((resolve, reject) => {
    if (document.querySelector(`script[src="${src}"]`)) {
      resolve();
      return;
    }

    const script = document.createElement('script');
    script.src = src;
    script.async = true;

    script.onload = () => {
      resolve();
    };

    script.onerror = (err) => {
      reject(new Error(`Failed to load script: ${src}. Error: ${err}`));
    };

    document.head.appendChild(script);
  });
} 