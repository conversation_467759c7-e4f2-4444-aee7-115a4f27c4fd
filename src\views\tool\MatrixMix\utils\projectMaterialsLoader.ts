/**
 * @file projectMaterialsLoader.ts
 * @description 工程素材加载器，专门处理云剪辑和模板工厂两种模式下的素材加载
 *              进一步简化 useVideoEditor.ts 中的长函数，提供统一的素材加载接口
 */

import type { EditSource } from './editSourceSelector';
import { createSelector } from './editSourceSelector';
import { ElMessage } from 'element-plus';

/**
 * 素材加载结果
 */
export interface MaterialsLoadResult {
  materials: any[];
  loadMethod: 'cloudEdit' | 'templateNew' | 'templateFallback';
  success: boolean;
  message?: string;
}

/**
 * 云剪辑模式素材加载器
 * @param projectId - 工程ID
 * @returns 素材加载结果
 */
export async function loadCloudEditMaterials(projectId: string): Promise<MaterialsLoadResult> {
  try {
    const { getEditingProjectMaterials } = await import('../api/media');
    console.log('🎯 使用云剪辑专用API获取工程素材');
    
    const response = await getEditingProjectMaterials(projectId);
    const materials = response.MediaInfos || [];
    
    console.log(`✅ 云剪辑工程素材加载成功，素材数量: ${materials.length}`);
    
    if (materials.length === 0) {
      console.log('📭 当前云剪辑工程暂无关联素材');
    }
    
    return {
      materials,
      loadMethod: 'cloudEdit',
      success: true
    };
  } catch (error) {
    console.error('❌ 云剪辑工程素材加载失败:', error);
    return {
      materials: [],
      loadMethod: 'cloudEdit',
      success: false,
      message: error instanceof Error ? error.message : '云剪辑工程素材加载失败'
    };
  }
}

/**
 * @deprecated 此方法已废弃，请使用 loadTemplateFactoryMaterialsFallback
 * 模板工厂模式素材加载器（使用模板素材API）
 * 注意：此方法依赖阿里云签名服务，可能会遇到权限配置问题
 * @param templateId - 模板ID
 * @returns 素材加载结果
 */
export async function loadTemplateFactoryMaterialsNew(templateId: string): Promise<MaterialsLoadResult> {
  try {
    const { getTemplateMaterials } = await import('../api/template');
    console.log('🎯 尝试使用 getTemplateMaterials API（获取模板包内素材地址）');
    
    const materialsResponse = await getTemplateMaterials(templateId);
    console.log('✅ 成功获取模板素材地址:', materialsResponse);
    
    // 处理模板素材地址响应
    if (materialsResponse.data && typeof materialsResponse.data === 'object') {
      // 将素材地址转换为标准格式
      const materialsList = Object.entries(materialsResponse.data).map(([fileName, fileUrl]) => ({
        MediaId: `template_${fileName}`, // 生成虚拟MediaId
        MediaBasicInfo: {
          MediaId: `template_${fileName}`,
          Title: fileName,
          MediaType: fileName.toLowerCase().includes('.mp4') || fileName.toLowerCase().includes('.mov') ? 'video' : 
                    fileName.toLowerCase().includes('.mp3') || fileName.toLowerCase().includes('.wav') ? 'audio' : 'image',
          CoverURL: fileUrl,
          Status: 'Normal'
        },
        FileInfoList: [{
          FileBasicInfo: {
            FileName: fileName,
            FileUrl: fileUrl,
            FileStatus: 'Normal'
          }
        }]
      }));
      
      console.log(`✅ 使用模板素材地址API加载成功，素材数量: ${materialsList.length}`);
      return {
        materials: materialsList,
        loadMethod: 'templateNew',
        success: true
      };
    } else {
      throw new Error('模板素材地址响应格式异常');
    }
  } catch (error) {
    console.error('❌ 模板素材地址API调用失败:', error);
    return {
      materials: [],
      loadMethod: 'templateNew',
      success: false,
      message: error instanceof Error ? error.message : '模板素材地址API调用失败'
    };
  }
}

/**
 * 模板工厂模式素材加载器（标准方案）
 * 使用 getTemplate + getMediaInfo 获取模板关联素材
 * @param templateId - 模板ID
 * @param templateData - 可选的模板数据，如果提供则不再重复调用 getTemplate
 * @returns 素材加载结果
 */
export async function loadTemplateFactoryMaterialsFallback(
  templateId: string, 
  templateData?: any
): Promise<MaterialsLoadResult> {
  try {
    console.log('🔄 使用标准方案获取模板素材');
    console.debug('标准方案原理:', {
      step1: templateData ? '使用已获取的模板数据' : '调用getTemplate接口，设置relatedMediaidFlag=1',
      step2: '从RelatedMediaids中提取关联素材MediaId列表',
      step3: '批量调用getMediaInfo获取每个素材的详细信息',
      advantage: '使用官方推荐的关联素材获取方式，稳定可靠',
      reason: '避免了文件签名的复杂性，直接使用媒资库查询'
    });
    
    let template;
    if (templateData) {
      template = templateData;
      console.log('📦 使用已提供的模板数据，避免重复API调用');
    } else {
      console.log('🌐 模板数据未提供，重新获取模板详情（含关联素材）');
      const { getTemplate } = await import('../api/template');
      const response = await getTemplate(templateId, { relatedMediaidFlag: '1' });
      template = response.data.Template;
      console.log('✅ 重新获取模板数据完成');
    }
    let allMediaIds: string[] = [];
    
    if (template.RelatedMediaids) {
      const relatedMediaIds = typeof template.RelatedMediaids === 'string' 
        ? JSON.parse(template.RelatedMediaids) 
        : template.RelatedMediaids;
      
      // 提取所有有效的MediaId，过滤掉空值
      allMediaIds = [
        ...(relatedMediaIds.video || []),
        ...(relatedMediaIds.audio || []),
        ...(relatedMediaIds.image || [])
      ].filter(id => id && id.trim() !== '');
      
      console.log('🔍 从模板RelatedMediaids中提取的MediaId:', allMediaIds);
    }
    
    // 如果没有RelatedMediaids，尝试从Config中解析实际使用的MediaId
    if (allMediaIds.length === 0 && template.Config) {
      console.log('🔍 尝试从模板Config中解析MediaId');
      try {
        const config = typeof template.Config === 'string' 
          ? JSON.parse(template.Config) 
          : template.Config;
        
        const mediaIds = new Set<string>();
        
        // 遍历视频轨道提取MediaId
        if (config.VideoTracks) {
          config.VideoTracks.forEach((track: any) => {
            if (track.VideoTrackClips) {
              track.VideoTrackClips.forEach((clip: any) => {
                if (clip.MediaId && !clip.MediaId.startsWith('$')) {
                  mediaIds.add(clip.MediaId);
                }
              });
            }
          });
        }
        
        // 遍历音频轨道提取MediaId
        if (config.AudioTracks) {
          config.AudioTracks.forEach((track: any) => {
            if (track.AudioTrackClips) {
              track.AudioTrackClips.forEach((clip: any) => {
                if (clip.MediaId && !clip.MediaId.startsWith('$')) {
                  mediaIds.add(clip.MediaId);
                }
              });
            }
          });
        }
        
        allMediaIds = Array.from(mediaIds);
        console.log('🔍 从模板Config中解析的MediaId:', allMediaIds);
      } catch (configError) {
        console.error('解析模板Config失败:', configError);
      }
    }
    
    if (allMediaIds.length > 0) {
      const { getMediaInfo } = await import('../api/media');
      const mediaPromises = allMediaIds.map(mediaId => 
        getMediaInfo({ mediaId }).catch(err => {
          console.warn(`获取媒资信息失败 (MediaId: ${mediaId}):`, err);
          return null;
        })
      );
      const mediaResponses = await Promise.all(mediaPromises);
      
      // 过滤掉失败的请求
      const validMediaResponses = mediaResponses.filter(res => res !== null);
      const materials = validMediaResponses.map(res => res.MediaInfo);
      
      console.log(`✅ 使用标准方案加载成功，素材数量: ${materials.length}`);
      if (validMediaResponses.length < allMediaIds.length) {
        console.warn(`⚠️ 部分素材获取失败，成功: ${validMediaResponses.length}/${allMediaIds.length}`);
      }
      
      return {
        materials,
        loadMethod: 'templateFallback',
        success: true
      };
    } else {
      console.log('📭 模板没有找到有效的关联素材');
      return {
        materials: [],
        loadMethod: 'templateFallback',
        success: true
      };
    }
  } catch (error) {
    console.error('❌ 模板标准方案失败:', error);
    return {
      materials: [],
      loadMethod: 'templateFallback',
      success: false,
      message: error instanceof Error ? error.message : '模板标准方案失败'
    };
  }
}


/**
 * 统一的工程素材加载器
 * 根据编辑来源自动选择合适的加载方案
 * @param editSource - 编辑来源
 * @param projectId - 工程ID（云剪辑模式）
 * @param templateId - 模板ID（模板工厂模式）
 * @param templateData - 可选的模板数据（模板工厂模式）
 * @returns 素材加载结果
 */
export async function loadProjectMaterials(
  editSource: EditSource,
  projectId?: string,
  templateId?: string,
  templateData?: any
): Promise<MaterialsLoadResult> {
  
  const selector = createSelector(editSource);
  const currentId = selector.selectId(projectId || '', templateId || '');
  const loadingText = selector.selectText('云剪辑工程素材', '模板工厂素材');
  
  console.log(`🚀 开始加载${loadingText}`, {
    editSource,
    currentId,
    projectId,
    templateId,
    hasTemplateData: !!templateData
  });
  
  if (!currentId) {
    const idType = selector.selectText('云剪辑工程ID', '模板工厂模板ID');
    const errorMessage = `${idType}为空，无法加载工程素材`;
    console.warn(`⚠️ ${errorMessage}`);
    return {
      materials: [],
      loadMethod: selector.isCloudEditing() ? 'cloudEdit' : 'templateFallback',
      success: false,
      message: errorMessage
    };
  }
  
  try {
    let result: MaterialsLoadResult;
    
    if (selector.isCloudEditing()) {
      // 使用云剪辑API加载工程素材
      result = await loadCloudEditMaterials(currentId);
    } else {
      // 使用模板工厂标准方案加载素材
      result = await loadTemplateFactoryMaterialsFallback(currentId, templateData);
    }
    return result;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error(`❌ ${loadingText}加载异常:`, error);
    
    return {
      materials: [],
      loadMethod: selector.isCloudEditing() ? 'cloudEdit' : 'templateFallback',
      success: false,
      message: errorMessage
    };
  }
}

/**
 * 检查素材加载结果并显示用户友好的提示
 * @param result - 素材加载结果
 * @param showMessage - 是否显示消息提示
 */
export function handleMaterialsLoadResult(result: MaterialsLoadResult, showMessage: boolean = true): void {
  if (!showMessage) return;
  
  if (result.success) {
    const methodText = {
      cloudEdit: '云剪辑API',
      templateNew: '模板素材API（已废弃）',
      templateFallback: '模板标准方案'
    }[result.loadMethod];
    
    if (result.materials.length > 0) {
      ElMessage.success(`素材加载成功，共${result.materials.length}个素材 (${methodText})`);
    } else {
      ElMessage.info(`暂无素材 (${methodText})`);
    }
  } else {
    ElMessage.error(`素材加载失败: ${result.message}`);
  }
}
