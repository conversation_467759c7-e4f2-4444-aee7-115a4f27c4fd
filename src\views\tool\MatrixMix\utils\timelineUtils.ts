/**
 * @file timelineUtils.ts
 * @description 时间轴操作相关的工具函数集合
 *              提供时间轴数据处理、片段操作、轨道管理等通用功能
 *              从 useVideoEditor.ts 中抽取，降低代码复杂度，提高可维护性
 */

import type { Timeline, TimelineClip } from '../types/videoEdit';

/**
 * @description 支持的轨道类型枚举
 */
export type TrackType = 'video' | 'audio' | 'subtitle' | 'image';

/**
 * @interface TimelineOperationResult
 * @description 时间轴操作的统一返回结果格式
 */
export interface TimelineOperationResult {
  success: boolean;
  timeline?: Timeline;
  error?: string;
}

/**
 * @interface TrackClipsResult
 * @description 获取轨道片段的结果接口
 */
export interface TrackClipsResult {
  trackClips: TimelineClip[] | undefined;
  found: boolean;
}

/**
 * @function deepCopyTimeline
 * @description 深拷贝时间轴数据，确保 Vue 响应性更新时不污染原始数据
 * @param {Timeline} timeline - 原始时间轴数据
 * @returns {Timeline} 深拷贝后的时间轴数据
 * @example
 * ```typescript
 * const copiedTimeline = deepCopyTimeline(originalTimeline);
 * // 可以安全地修改 copiedTimeline 而不影响原始数据
 * ```
 */
export function deepCopyTimeline(timeline: Timeline): Timeline {
  return JSON.parse(JSON.stringify(timeline));
}

/**
 * @function getTrackClips
 * @description 根据轨道类型获取对应的片段数组
 *              支持视频、音频、字幕、图片等多种轨道类型
 * @param {Timeline} timeline - 时间轴数据
 * @param {TrackType} type - 轨道类型
 * @param {number} trackIndex - 轨道索引，默认为 0（通常只有一个轨道）
 * @returns {TrackClipsResult} 包含片段数组和是否找到的结果对象
 * @example
 * ```typescript
 * const { trackClips, found } = getTrackClips(timeline, 'video', 0);
 * if (found && trackClips) {
 *   console.log('视频轨道包含', trackClips.length, '个片段');
 * }
 * ```
 */
export function getTrackClips(timeline: Timeline, type: TrackType, trackIndex: number = 0): TrackClipsResult {
  let trackClips: TimelineClip[] | undefined;
  let found = false;

  switch (type) {
    case 'video':
      trackClips = timeline.VideoTracks?.[trackIndex]?.VideoTrackClips;
      found = !!(timeline.VideoTracks?.[trackIndex]?.VideoTrackClips);
      break;
    case 'audio':
      trackClips = timeline.AudioTracks?.[trackIndex]?.AudioTrackClips;
      found = !!(timeline.AudioTracks?.[trackIndex]?.AudioTrackClips);
      break;
    case 'subtitle':
      trackClips = timeline.SubtitleTracks?.[trackIndex]?.SubtitleTrackClips;
      found = !!(timeline.SubtitleTracks?.[trackIndex]?.SubtitleTrackClips);
      break;
    case 'image':
      trackClips = timeline.ImageTracks?.[trackIndex]?.ImageTrackClips;
      found = !!(timeline.ImageTracks?.[trackIndex]?.ImageTrackClips);
      break;
    default:
      console.warn(`不支持的轨道类型: ${type}`);
      return { trackClips: undefined, found: false };
  }

  return { trackClips, found };
}

/**
 * @function validateClipIndex
 * @description 验证片段索引是否在有效范围内
 * @param {TimelineClip[] | undefined} trackClips - 轨道片段数组
 * @param {number} index - 要验证的索引
 * @param {string} operation - 操作名称（用于错误提示）
 * @returns {boolean} 索引是否有效
 */
export function validateClipIndex(trackClips: TimelineClip[] | undefined, index: number, operation: string): boolean {
  if (!trackClips || trackClips.length <= index) {
    console.warn(`${operation}失败：索引 ${index} 超出范围（总共 ${trackClips?.length || 0} 个片段）`);
    return false;
  }
  return true;
}

/**
 * @function validateCutTime
 * @description 验证切割时间点是否在片段的有效范围内
 * @param {TimelineClip} clip - 要切割的片段
 * @param {number} cutTime - 切割时间点（秒）
 * @returns {boolean} 切割时间是否有效
 */
export function validateCutTime(clip: TimelineClip, cutTime: number): boolean {
  if (cutTime <= clip.TimelineIn || cutTime >= clip.TimelineOut) {
    console.warn(
      `切割点 ${cutTime.toFixed(2)} 秒不在片段有效范围 (${clip.TimelineIn.toFixed(2)} - ${clip.TimelineOut.toFixed(2)} 秒)`
    );
    return false;
  }
  return true;
}

/**
 * @function createCutClips
 * @description 根据切割时间点创建两个新片段
 * @param {TimelineClip} originalClip - 原始片段
 * @param {number} cutTime - 切割时间点（秒）
 * @returns {[TimelineClip, TimelineClip]} 切割后的两个片段数组
 */
export function createCutClips(originalClip: TimelineClip, cutTime: number): [TimelineClip, TimelineClip] {
  const newClip1: TimelineClip = {
    ...originalClip,
    TimelineOut: cutTime, // 第一个片段的结束时间为切割点
  };

  const newClip2: TimelineClip = {
    ...originalClip,
    TimelineIn: cutTime, // 第二个片段的开始时间为切割点
  };

  return [newClip1, newClip2];
}

/**
 * @function updateClipTimeRange
 * @description 更新片段的时间范围，保持原始持续时间
 * @param {TimelineClip} clip - 要更新的片段
 * @param {number} newStartTime - 新的开始时间（秒）
 * @returns {TimelineClip} 更新后的片段（修改原对象）
 */
export function updateClipTimeRange(clip: TimelineClip, newStartTime: number): TimelineClip {
  // 计算原始片段的持续时间
  const originalDuration = clip.TimelineOut - clip.TimelineIn;
  
  // 更新 TimelineIn 和 TimelineOut
  clip.TimelineIn = newStartTime;
  clip.TimelineOut = newStartTime + originalDuration;
  
  return clip;
}

/**
 * @function cutClipAtTime
 * @description 在指定时间点切割时间轴中的片段
 *              这是一个高级函数，整合了验证、切割、更新等逻辑
 * @param {Timeline} timeline - 时间轴数据
 * @param {TrackType} type - 轨道类型
 * @param {number} index - 片段索引
 * @param {number} cutTime - 切割时间点（秒）
 * @returns {TimelineOperationResult} 操作结果，包含成功状态和时间轴数据
 */
export function cutClipAtTime(
  timeline: Timeline, 
  type: TrackType, 
  index: number, 
  cutTime: number
): TimelineOperationResult {
  // 1. 深拷贝时间轴数据
  const newTimeline = deepCopyTimeline(timeline);
  
  // 2. 获取对应轨道的片段数组
  const { trackClips, found } = getTrackClips(newTimeline, type);
  if (!found) {
    const error = `未找到 ${type} 轨道`;
    console.warn(error);
    return { success: false, error };
  }
  
  // 3. 验证片段索引
  if (!validateClipIndex(trackClips, index, '切割')) {
    const error = `索引 ${index} 超出范围（总共 ${trackClips?.length || 0} 个片段）`;
    return { success: false, error };
  }
  
  // 4. 获取原始片段并验证切割时间
  const originalClip = trackClips![index];
  if (!validateCutTime(originalClip, cutTime)) {
    const error = `切割点 ${cutTime.toFixed(2)} 秒不在片段有效范围 (${originalClip.TimelineIn.toFixed(2)} - ${originalClip.TimelineOut.toFixed(2)} 秒)`;
    return { success: false, error };
  }
  
  // 5. 创建两个新片段
  const [newClip1, newClip2] = createCutClips(originalClip, cutTime);
  
  // 6. 在原位置替换为两个新片段
  trackClips!.splice(index, 1, newClip1, newClip2);
  
  return { success: true, timeline: newTimeline };
}

/**
 * @function deleteClipAtIndex
 * @description 删除时间轴中指定索引的片段
 * @param {Timeline} timeline - 时间轴数据
 * @param {TrackType} type - 轨道类型
 * @param {number} index - 片段索引
 * @returns {TimelineOperationResult} 操作结果，包含成功状态和时间轴数据
 */
export function deleteClipAtIndex(
  timeline: Timeline, 
  type: TrackType, 
  index: number
): TimelineOperationResult {
  // 1. 深拷贝时间轴数据
  const newTimeline = deepCopyTimeline(timeline);
  
  // 2. 获取对应轨道的片段数组
  const { trackClips, found } = getTrackClips(newTimeline, type);
  if (!found) {
    const error = `未找到 ${type} 轨道`;
    console.warn(error);
    return { success: false, error };
  }
  
  // 3. 验证片段索引
  if (!validateClipIndex(trackClips, index, '删除')) {
    const error = `索引 ${index} 超出范围（总共 ${trackClips?.length || 0} 个片段）`;
    return { success: false, error };
  }
  
  // 4. 删除片段
  const deletedClip = trackClips!.splice(index, 1)[0];
  console.log(`已删除 ${type} 轨道中的片段:`, deletedClip);
  
  return { success: true, timeline: newTimeline };
}

/**
 * @function updateClipTimeAtIndex
 * @description 更新时间轴中指定片段的时间范围
 * @param {Timeline} timeline - 时间轴数据
 * @param {TrackType} type - 轨道类型
 * @param {number} index - 片段索引
 * @param {number} newStartTime - 新的开始时间（秒）
 * @returns {TimelineOperationResult} 操作结果，包含成功状态和时间轴数据
 */
export function updateClipTimeAtIndex(
  timeline: Timeline, 
  type: TrackType, 
  index: number, 
  newStartTime: number
): TimelineOperationResult {
  // 1. 深拷贝时间轴数据
  const newTimeline = deepCopyTimeline(timeline);
  
  // 2. 获取对应轨道的片段数组
  const { trackClips, found } = getTrackClips(newTimeline, type);
  if (!found) {
    const error = `未找到 ${type} 轨道`;
    console.warn(error);
    return { success: false, error };
  }
  
  // 3. 验证片段索引
  if (!validateClipIndex(trackClips, index, '时间更新')) {
    const error = `索引 ${index} 超出范围（总共 ${trackClips?.length || 0} 个片段）`;
    return { success: false, error };
  }
  
  // 4. 更新片段时间范围
  const clipToUpdate = trackClips![index];
  updateClipTimeRange(clipToUpdate, newStartTime);
  
  return { success: true, timeline: newTimeline };
}
