<template>
  <div class="vid-edit-header-toolbar">
    <div class="vid-edit-header-left">
      <div class="vid-edit-header-logo"></div>
      <span>{{ title }}</span>
      <span class="vid-edit-back-link" @click="emit('go-back')">
        <i class="el-icon-arrow-left"></i>
        返回
      </span>
    </div>
    <div class="vid-edit-header-right">
      <template v-if="editSource === 'template-factory'">
        <el-button @click="handleSaveTemplate" type="primary">保存模板</el-button>
      </template>
      <template v-else>
        <el-button @click="emit('save-as-template')" type="default">生成为模板</el-button>
        <el-button @click="emit('save')" type="default">保存</el-button>
        <el-dropdown @command="handleExportCommand" trigger="click">
          <el-button type="primary">
            导出为<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-tooltip
                content="仅支持导出视频、图片、音频素材，其他类型素材将会自动过滤"
                placement="left"
              >
                <el-dropdown-item command="export-clips">各片段独立导出</el-dropdown-item>
              </el-tooltip>
              <el-tooltip
                content="选中的片段需在同一条轨道上"
                placement="left"
              >
                <el-dropdown-item command="export-composition">片段合成导出</el-dropdown-item>
              </el-tooltip>
              <el-dropdown-item command="export-video" divided>导出视频</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';
import { useVideoEditorStore } from '../useVideoEditor';

defineProps<{
  title: string;
  editSource?: 'cloud-editing' | 'template-factory';
}>();

const emit = defineEmits<{
  (e: 'go-back'): void;
  (e: 'save-as-template'): void;
  (e: 'save'): void;
  (e: 'export', command: string): void;
}>();

// 直接在组件中访问数据中心
const videoEditorStore = useVideoEditorStore();

const handleExportCommand = (command: string) => {
  emit('export', command);
};

// 直接在组件中处理保存模板
const handleSaveTemplate = () => {
  videoEditorStore.handleSaveTemplate();
};
</script>

<style lang="scss" scoped>
.vid-edit-header-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #24292e;
  padding: 10px 20px;
  border-bottom: 1px solid #3a3f44;
  flex-shrink: 0;
  height: 7vh;

  .vid-edit-header-left {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 18px;
    font-weight: 600;
    color: #f0f0f0;

    .vid-edit-header-logo {
      width: 32px;
      height: 32px;
      background: linear-gradient(45deg, #1378f9, #4a90e2);
      border-radius: 8px;
    }

    .vid-edit-back-link {
      font-size: 14px;
      color: #a8b2c2;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 4px;
      margin-left: 16px;
      &:hover {
        color: #ffffff;
      }
    }
  }

  .vid-edit-header-right {
    display: flex;
    align-items: center;
    gap: 12px;
  }
}
</style> 