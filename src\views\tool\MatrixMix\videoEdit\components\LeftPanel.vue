<template>
    <div class="vid-edit-left-panel" :class="{ 'is-collapsed': props.isCollapsed }">
        <div class="collapse-button" @click="emit('toggle-collapse')">
            <el-icon v-if="props.isCollapsed"><ArrowRight /></el-icon>
            <el-icon v-else><ArrowLeft /></el-icon>
        </div>
        <div v-if="props.editSource === 'template-factory' && !props.isCollapsed" class="vid-edit-body-left-top">
            <el-tabs v-model="activeName" class="demo-tabs">
                <el-tab-pane label="内容编辑" name="first" />
                <el-tab-pane label="模板可变素材管理" name="second" />
                <el-tab-pane label="随机效果设置" name="third" />
            </el-tabs>
        </div>
        <div v-else-if="props.editSource !== 'template-factory' || props.isCollapsed" class="vid-edit-body-left-top-dummy"></div>

        <div class="vid-edit-body-left-middle">
            <div v-if="props.editSource === 'cloud-editing' || activeName === 'first'">
                <LeftPanelContentEditingPanel 
                    :edit-source="props.editSource"
                    :is-collapsed="props.isCollapsed"
                    @menu-change="handleMenuChange"
                    @tab-change="handleTabChange"
                    @import-material="handleImportMaterial"
                    @select-resource="handleSelectResource"
                />
            </div>
            <div v-if="props.editSource === 'template-factory' && activeName === 'second' && !props.isCollapsed" class="vid-edit-content-panel">
                <el-scrollbar class="custom-scrollbar" :max-height="360">
                    <el-table :data="videoEditorStore.templateMaterials" style="width: 100%;">
                        <el-table-column prop="name" label="素材名称" />
                        <el-table-column prop="type" label="类型" width="60" />
                        <el-table-column prop="replace" label="是否可以替换" width="100">
                            <template #default="scope">
                                <el-switch
                                    v-model="scope.row.replace"
                                    :active-value="true"
                                    :inactive-value="false"
                                    @change="(val: boolean) => handleReplaceChange(scope.$index, val)"
                                />
                            </template>
                        </el-table-column>
                        <el-table-column prop="idNum" label="ID命名" width="90" />
                        <el-table-column prop="remark" label="备注">
                            <template #default="scope">
                                <span
                                    v-if="editingRemarkIndex !== scope.$index && scope.row.remark"
                                    @click="
                                        editingRemarkIndex = scope.$index;
                                        tempRemarkValue = scope.row.remark || '';
                                        $nextTick(() => remarkInputRef?.focus());
                                    "
                                    class="editable-remark-text"
                                >{{ scope.row.remark }}</span>
                                <span v-else-if="editingRemarkIndex !== scope.$index"
                                    @click="
                                        editingRemarkIndex = scope.$index;
                                        tempRemarkValue = scope.row.remark || '';
                                        $nextTick(() => remarkInputRef?.focus());
                                    "
                                    class="editable-remark-empty"
                                ></span>
                                <el-input
                                    v-else
                                    v-model="tempRemarkValue"
                                    placeholder="请输入备注"
                                    size="small"
                                    @blur="
                                        if (tempRemarkValue !== scope.row.remark) {
                                            handleRemarkChange(scope.$index, tempRemarkValue);
                                        }
                                        editingRemarkIndex = null;
                                    "
                                    ref="remarkInputRef"
                                />
                            </template>
                        </el-table-column>
                        <el-table-column prop="action" label="操作">
                            <template #default="scope">
                                <span v-if="scope.row.action === '高级设置'">
                                    <el-button link type="primary" @click="handleOpenAdvancedSettings(scope.row.advancedSettings, scope.$index)">高级设置</el-button>
                                </span>
                                <span v-else-if="scope.row.action">{{ scope.row.action }}</span>
                                <span v-else>-</span>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-scrollbar>
            </div>
            <div v-if="props.editSource === 'template-factory' && activeName === 'third' && !props.isCollapsed" class="vid-edit-content-panel">随机效果设置</div>
        </div>
    </div>
    <!-- 高级设置对话框 -->
    <MaterialAdvancedSettingsDialog
        v-model:visible="showAdvancedSettingsDialog"
        :settings="selectedMaterialAdvancedSettings"
        @confirm="handleAdvancedSettingsConfirm"
    />
</template>

<script setup lang="ts">
import { ref, defineProps, withDefaults, watchEffect, defineEmits } from 'vue'
import { Plus, ArrowLeft, ArrowRight, VideoPlay } from '@element-plus/icons-vue'
import type { Material } from '../../types/template'
import { useVideoEditorStore } from '../useVideoEditor'
import MaterialAdvancedSettingsDialog from './MaterialAdvancedSettingsDialog.vue'
import LeftPanelContentEditingPanel from './LeftPanel_ContentEditingPanel.vue'
import { ElMessage, ElScrollbar, ElTable } from 'element-plus'

interface Props {
    materials?: Material[];
    editSource?: 'cloud-editing' | 'template-factory';
    isCollapsed?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    materials: () => [],
    editSource: 'cloud-editing',
    isCollapsed: false
})

// 定义组件可以发出的事件
const emit = defineEmits(['toggle-collapse', 'add-element'])

// 资源相关状态（保留一些与模板工厂相关的状态）
const selectedResourceIndex = ref<number | null>(null);

const activeName = ref<string>('');

const videoEditorStore = useVideoEditorStore();

// 新增：用于控制备注编辑状态的索引
const editingRemarkIndex = ref<number | null>(null);
// 新增：控制高级设置对话框的显示
const showAdvancedSettingsDialog = ref(false);
// 新增：存储当前选中的素材的高级设置
const selectedMaterialAdvancedSettings = ref<Record<string, any>>({});
// 新增：存储当前正在编辑高级设置的素材索引
const currentEditingMaterialIndex = ref<number | null>(null);

// 备注编辑临时值
const tempRemarkValue = ref<string>('');
// 备注输入框的引用
const remarkInputRef = ref<any>(null);
const handleOpenAdvancedSettings = (settings: Record<string, any>, index: number) => {
    selectedMaterialAdvancedSettings.value = settings;
    currentEditingMaterialIndex.value = index; // 记录当前编辑的素材索引
    showAdvancedSettingsDialog.value = true;
};

// 新增：处理可替换状态变更，同时更新action字段
const handleReplaceChange = (index: number, val: boolean) => {
    // 更新可替换状态
    videoEditorStore.updateTemplateMaterial(index, 'replace', val);
    
    // 同时更新action字段，确保按钮显示状态同步
    const actionValue = val ? '高级设置' : '';
    videoEditorStore.updateTemplateMaterial(index, 'action', actionValue);
    
    console.log(`素材 ${index} 可替换状态已更改为: ${val}, 操作按钮: ${actionValue}`);
};

// 新增：处理备注变更
const handleRemarkChange = (index: number, remarkValue: string) => {
    videoEditorStore.updateTemplateMaterial(index, 'remark', remarkValue);
    console.log(`素材 ${index} 备注已更改为: "${remarkValue}"`);
};

const handleAdvancedSettingsConfirm = (newSettings: Record<string, any>) => {
    // 在这里处理从对话框返回的新设置
    console.log('从高级设置对话框接收到新的设置:', newSettings);

    if (currentEditingMaterialIndex.value !== null) {
        // 更新对应素材的 advancedSettings
        videoEditorStore.updateTemplateMaterial(
            currentEditingMaterialIndex.value,
            'advancedSettings',
            newSettings
        );
        ElMessage.success('高级设置已更新。');
    } else {
        console.warn('无法更新高级设置：未找到当前编辑素材的索引。');
        ElMessage.error('更新高级设置失败。');
    }
    showAdvancedSettingsDialog.value = false;
    currentEditingMaterialIndex.value = null; // 清除索引
};

// 处理素材选择（保留，用于模板工厂）
const handleSelectResource = (resource: any) => {
    if (typeof resource === 'number') {
        // 兼容原有的索引选择方式（模板工厂使用）
        selectedResourceIndex.value = resource;
        console.log('选中资源索引:', resource);
    } else {
        // 新的媒资对象选择方式（云剪辑使用）
        console.log('选中媒资:', resource);
        // TODO: 将来与 useVideoEditor 集成时在这里处理
    }
};

// 处理内容编辑面板的事件
const handleMenuChange = (menu: string) => {
    console.log('菜单切换:', menu);
    // TODO: 将来与 useVideoEditor 集成时在这里处理
};

const handleTabChange = (tab: string) => {
    console.log('标签页切换:', tab);
    // TODO: 将来与 useVideoEditor 集成时在这里处理
};

// 处理导入素材
const handleImportMaterial = () => {
    console.log('导入素材');
    // TODO: 实现导入素材逻辑
    ElMessage.info('导入素材功能待实现');
};

watchEffect(() => {
    if (props.editSource === 'template-factory') {
        activeName.value = 'second';
    } else {
        activeName.value = 'first';
    }
});
</script>

<style lang="scss" scoped>
.vid-edit-left-panel {
    width: 580px;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    background-color: #24292e;
    border-radius: 8px;
    padding: 10px;
    min-height: 0;
    max-height: 100%; /* 限制最大高度 */
    height: 100%; /* 确保占满父容器高度 */
    position: relative; /* 为 collapse-button 定位 */
    transition: width 0.3s ease; /* 添加过渡动画 */
    overflow: hidden; /* 确保内容不会溢出 */

    &.is-collapsed {
        width: 80px; /* 折叠后的宽度 */
        .vid-edit-body-left-top, .vid-edit-body-left-top-dummy {
            display: none; // Hide the top tabs or dummy
        }

        .vid-edit-body-left-middle {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: flex-start; // Align menu to top
            align-items: center; // Center horizontally if needed

            .content-editing-panel {
                flex-grow: 1;
                display: flex;
                flex-direction: column;
                overflow: hidden;

                .editing-menu {
                    width: 100%; // Take full width of the collapsed panel
                    border-right: none; // Remove right border
                    padding: 10px 0; // Adjust padding to center items
                    flex-grow: 1; // Let the menu take available space to push content out if needed

                    .menu-item {
                        padding: 15px 0; // Make menu items taller
                        font-size: 12px; // Potentially smaller font size
                        white-space: nowrap; // Prevent text wrapping
                    }
                }
                .editing-content,
                ~ .vid-edit-content-panel { /* Hide other content panels */
                    display: none;
                }
            }
        }
    }

    .collapse-button {
        position: absolute;
        right: -20px; /* 按钮位置，更贴近面板 */
        top: 50%;
        transform: translateY(-50%);
        width: 20px; /* 稍微减小宽度 */
        height: 48px;
        background-color: #24292e;
        border-radius: 0 4px 4px 0;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 10;
        border: 1px solid #3a3f44;
        border-left: none;
        color: #a8b2c2;

        &:hover {
            background-color: #3e3e42;
        }
    }

    .vid-edit-body-left-top {
        padding: 0 10px;
        flex-shrink: 0;
    }

    /* 新增：折叠时显示的菜单区域占位符 */
    .vid-edit-body-left-top-dummy {
        height: 40px; /* 模拟 tabs 的高度 */
        flex-shrink: 0;
    }

    .vid-edit-body-left-middle {
        flex: 1; /* 使用 flex: 1 替代 flex-grow: 1 以获得更好的空间分配 */
        display: flex;
        flex-direction: column;
        min-height: 300px; /* 设置最小高度，确保有足够空间显示内容 */
        height: 100%; /* 确保占满可用高度 */
        padding-top: 0;
        overflow: hidden; /* 确保不会溢出 */

        .vid-edit-content-panel {
            padding: 10px;
            flex: 1; /* 使用 flex: 1 替代 flex-grow: 1 */
            display: flex; /* Make it a flex container */
            flex-direction: column; /* Children stack vertically */
            min-height: 0; /* 确保 flex 子元素能够正确收缩 */
            height: 380px; /* 设置固定高度，确保滚动条计算准确 */
            max-height: 380px; /* 限制最大高度 */
            overflow: hidden; /* 防止内容溢出 */
        }
    }
}

// Override Element Plus tabs styles for dark theme
::v-deep(.el-tabs__header) {
    margin-bottom: 10px;
    border-bottom: 1px solid #3a3f44;
}

::v-deep(.el-tabs__nav-wrap::after) {
    background-color: transparent;
}

::v-deep(.el-tabs__item) {
    color: #a8b2c2;

    &:hover {
        color: #ffffff;
    }

    &.is-active {
        color: #1378f9;
    }
}

::v-deep(.el-tabs__active-bar) {
    background-color: #1378f9;
}

// Table styles
::v-deep(.el-table) {
    background-color: transparent;
    --el-table-border-color: #3a3f44;
    --el-table-header-bg-color: #2a2f34;
    --el-table-tr-bg-color: #24292e;
    --el-table-row-hover-bg-color: #2e3439;
}

::v-deep(.el-table th),
::v-deep(.el-table td) {
    background-color: transparent !important;
    color: #e0e0e0;
    border-bottom: 1px solid #3a3f44;
    font-size: 12px;
}

::v-deep(.el-table thead) {
    color: #f0f0f0;
}

::v-deep(.el-table__body tr:hover>td) {
    background-color: #2e3439 !important;
}

// 自定义滚动条样式
::v-deep(.custom-scrollbar) {
    height: 100%; /* 确保滚动容器占满父容器 */
    
    .el-scrollbar__wrap {
        overflow-x: hidden; /* 隐藏横向滚动条 */
        overflow-y: auto; /* 确保纵向滚动正常 */
    }
    
    .el-scrollbar__view {
        height: 100%; /* 确保视图容器高度正确 */
    }
    
    .el-scrollbar__bar {
        &.is-vertical {
            right: 2px; /* 滚动条距离右边的距离 */
            width: 8px; /* 滚动条宽度 */
            z-index: 1; /* 确保滚动条在最上层 */
        }
        
        &.is-horizontal {
            display: none; /* 隐藏横向滚动条 */
        }
    }
    
    .el-scrollbar__thumb {
        background-color: #4a5568; /* 滚动条滑块颜色 */
        border-radius: 4px; /* 滚动条滑块圆角 */
        opacity: 0.6; /* 默认透明度 */
        transition: all 0.2s ease; /* 减少过渡时间，提高响应性 */
        
        &:hover {
            background-color: #5a6578; /* 悬停时的颜色 */
            opacity: 0.8; /* 悬停时的透明度 */
        }
    }
    
    .el-scrollbar__track {
        background-color: #2d3748; /* 滚动条轨道颜色 */
        border-radius: 4px; /* 滚动条轨道圆角 */
        opacity: 0.3; /* 轨道透明度 */
        
        &:hover {
            opacity: 0.5; /* 悬停时轨道透明度 */
        }
    }
}

.editable-remark-text,
.editable-remark-empty {
  cursor: pointer;
  display: inline-block; /* 确保可以设置宽度和高度 */
  min-width: 50px; /* 最小宽度，确保点击区域 */
  min-height: 20px; /* 最小高度，确保点击区域 */
  line-height: 20px; /* 垂直居中 */
  vertical-align: middle;
}

.editable-remark-empty {
  /* 可以添加一些视觉反馈，例如 hover 时的背景色 */
  &:hover {
    background-color: #2a2f34; /* 稍微深一点的背景色 */
    border-radius: 4px;
  }
}
</style>