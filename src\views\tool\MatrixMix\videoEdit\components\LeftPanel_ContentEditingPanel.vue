<template>
    <div class="content-editing-panel">
        <div class="editing-menu">
            <template v-if="props.editSource === 'cloud-editing'">
                <div class="menu-item" 
                     :class="{ active: activeEditingMenu === 'resource' }"
                     @click="setActiveEditingMenu('resource')">资源库</div>
                <div class="menu-item" 
                     :class="{ active: activeEditingMenu === 'material' }"
                     @click="setActiveEditingMenu('material')">素材库</div>
                <div class="menu-item" 
                     :class="{ active: activeEditingMenu === 'avatar' }"
                     @click="setActiveEditingMenu('avatar')">数字人</div>
                <div class="menu-item" 
                     :class="{ active: activeEditingMenu === 'voice' }"
                     @click="setActiveEditingMenu('voice')">智能配音</div>
                <div class="menu-item" 
                     :class="{ active: activeEditingMenu === 'subtitle' }"
                     @click="setActiveEditingMenu('subtitle')">字幕</div>
                <div class="menu-item" 
                     :class="{ active: activeEditingMenu === 'sticker' }"
                     @click="setActiveEditingMenu('sticker')">贴纸</div>
            </template>
            <template v-else>
                <div class="menu-item" 
                     :class="{ active: activeEditingMenu === 'resource' }"
                     @click="setActiveEditingMenu('resource')">资源库</div>
                <div class="menu-item" 
                     :class="{ active: activeEditingMenu === 'voice' }"
                     @click="setActiveEditingMenu('voice')">智能配音</div>
                <div class="menu-item" 
                     :class="{ active: activeEditingMenu === 'subtitle' }"
                     @click="setActiveEditingMenu('subtitle')">字幕</div>
                <div class="menu-item" 
                     :class="{ active: activeEditingMenu === 'sticker' }"
                     @click="setActiveEditingMenu('sticker')">贴纸</div>
                <div class="menu-item" 
                     :class="{ active: activeEditingMenu === 'filter' }"
                     @click="setActiveEditingMenu('filter')">滤镜</div>
            </template>
        </div>
        <div class="editing-content" v-if="!props.isCollapsed">
            <!-- 资源库内容 -->
            <div v-if="activeEditingMenu === 'resource'" class="resource-library">
                <div class="resource-tabs">
                    <span class="resource-tab" 
                          :class="{ active: activeResourceTab === 'video' }"
                          @click="setActiveResourceTab('video')">视频</span>
                    <span class="resource-tab" 
                          :class="{ active: activeResourceTab === 'audio' }"
                          @click="setActiveResourceTab('audio')">音频</span>
                    <span class="resource-tab" 
                          :class="{ active: activeResourceTab === 'image' }"
                          @click="setActiveResourceTab('image')">图片</span>
                    <div class="spacer"></div>
                    <el-button type="primary" size="small" class="import-btn" @click="handleImportMaterial">
                        <template #icon><el-icon><Plus /></el-icon></template>
                        导入素材
                    </el-button>
                </div>
                <div class="resource-content">
                    <div v-loading="videoEditorStore.projectMaterialsLoading" 
                         class="resource-loading-container">
                        <div class="resource-grid-container">
                            <el-scrollbar height="35vh" always>
                                <div v-if="hasResources" class="resource-grid">
                                    <!-- 真实媒资卡片 -->
                                    <div v-for="(media, index) in filteredResources" :key="media.MediaId" 
                                         class="resource-card" 
                                         :class="{ 'selected': selectedResourceIndex === index }"
                                         @click="handleSelectResource(index)">
                                        <div class="resource-thumbnail">
                                            <img v-if="media.MediaBasicInfo.CoverURL" 
                                                 :src="media.MediaBasicInfo.CoverURL" 
                                                 :alt="media.MediaBasicInfo.Title" />
                                            <div v-else class="media-placeholder" :class="media.MediaBasicInfo.MediaType">
                                                <div class="media-icon">
                                                    <span v-if="media.MediaBasicInfo.MediaType === 'video'">🎬</span>
                                                    <span v-else-if="media.MediaBasicInfo.MediaType === 'audio'">🎵</span>
                                                    <span v-else-if="media.MediaBasicInfo.MediaType === 'image'">🖼️</span>
                                                    <span v-else>📄</span>
                                                </div>
                                            </div>
                                            <!-- 时长标签（仅视频和音频） -->
                                            <div v-if="media.FileInfoList && media.FileInfoList[0]?.FileBasicInfo?.Duration && 
                                                      (media.MediaBasicInfo.MediaType === 'video' || media.MediaBasicInfo.MediaType === 'audio')" 
                                             class="duration-tag">
                                                {{ formatRulerTime(media.FileInfoList[0].FileBasicInfo.Duration) }}
                                            </div>
                                            <!-- 文件大小标签 -->
                                            <div v-if="media.FileInfoList && media.FileInfoList[0]?.FileBasicInfo?.FileSize" 
                                                 class="size-tag">
                                                {{ formatFileSize(media.FileInfoList[0].FileBasicInfo.FileSize) }}
                                            </div>
                                            <!-- 转码状态标签 -->
                                            <div v-if="media.MediaBasicInfo.TranscodeStatus !== 'TranscodeSuccess'" 
                                                 class="status-tag" 
                                                 :class="media.MediaBasicInfo.TranscodeStatus.toLowerCase()">
                                                <span v-if="media.MediaBasicInfo.TranscodeStatus === 'Transcoding'">转码中</span>
                                                <span v-else-if="media.MediaBasicInfo.TranscodeStatus === 'TranscodeFailed'">转码失败</span>
                                                <span v-else-if="media.MediaBasicInfo.TranscodeStatus === 'Init'">初始化</span>
                                            </div>
                                            <div class="hover-overlay">
                                                <div class="play-icon">
                                                    <span v-if="media.MediaBasicInfo.MediaType === 'video'">▶</span>
                                                    <span v-else-if="media.MediaBasicInfo.MediaType === 'audio'">🎧</span>
                                                    <span v-else-if="media.MediaBasicInfo.MediaType === 'image'">👁️</span>
                                                    <span v-else>📄</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="resource-title" :title="media.MediaBasicInfo.Title">
                                            {{ media.MediaBasicInfo.Title }}
                                        </div>
                                        <div class="resource-meta">
                                            <span class="media-type">{{ media.MediaBasicInfo.MediaType.toUpperCase() }}</span>
                                            <span v-if="media.MediaBasicInfo.Category" class="category">{{ media.MediaBasicInfo.Category }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div v-else class="resource-empty-state">
                                    <div class="empty-icon">📁</div>
                                    <div class="empty-text">{{ activeResourceTab === 'video' ? '视频素材为空' : 
                                                            activeResourceTab === 'audio' ? '音频素材为空' : 
                                                            activeResourceTab === 'image' ? '图片素材为空' : '素材为空' }}</div>
                                    <div class="empty-hint">
                                        {{ videoEditorStore.projectMaterials.length > 0 ? '当前分类下暂无素材' : '可以点击右上角添加素材' }}
                                    </div>
                                </div>
                            </el-scrollbar>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 其他菜单内容的占位 -->
            <div v-else class="other-content">
                <div class="content-placeholder">
                    <div class="placeholder-icon">🎵</div>
                    <div class="placeholder-text">{{ getMenuDisplayName(activeEditingMenu) }}功能</div>
                    <div class="placeholder-hint">该功能正在开发中...</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, defineProps, withDefaults, defineEmits, onMounted, computed, watch } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage, ElScrollbar } from 'element-plus'
import { useVideoEditorStore } from '../useVideoEditor'
import { formatRulerTime } from '@/views/tool/MatrixMix/utils/timeUtils'

interface Props {
    editSource?: 'cloud-editing' | 'template-factory';
    isCollapsed?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    editSource: 'cloud-editing',
    isCollapsed: false
})

const emit = defineEmits(['menu-change', 'tab-change', 'import-material', 'select-resource'])

// 使用视频编辑器数据中心
const videoEditorStore = useVideoEditorStore()

// 内容编辑相关状态
const activeEditingMenu = ref<string>('resource');
const activeResourceTab = ref<string>('video');
const selectedResourceIndex = ref<number | null>(null);

// 计算属性：根据当前选中的标签页过滤媒资
const filteredResources = computed(() => {
    return videoEditorStore.filterProjectMaterialsByType(activeResourceTab.value as 'video' | 'audio' | 'image');
});

// 计算属性：是否有资源
const hasResources = computed(() => filteredResources.value.length > 0);
// 处理编辑菜单切换
const setActiveEditingMenu = (menu: string) => {
    activeEditingMenu.value = menu;
    console.log('切换到编辑菜单:', menu);
    emit('menu-change', menu);
    // 无论是云剪辑还是模板工厂，切换到资源库时都需要重新加载素材
    if (menu === 'resource' && videoEditorStore.editingProjectId) {
        videoEditorStore.loadProjectMaterials();
    }
};

// 处理资源标签页切换
const setActiveResourceTab = (tab: string) => {
    activeResourceTab.value = tab;
    console.log('切换到资源标签页:', tab);
    emit('tab-change', tab);
};

// 处理素材选择
const handleSelectResource = (index: number) => {
    selectedResourceIndex.value = index;
    const selectedResource = filteredResources.value[index];
    console.log('选中资源:', selectedResource);
    emit('select-resource', selectedResource);
};

// 处理导入素材
const handleImportMaterial = () => {
    console.log('导入素材');
    emit('import-material');
    ElMessage.info('导入素材功能待实现');
};
// 获取文件大小格式化显示
const formatFileSize = (bytes: string): string => {
    if (!bytes) return '0 B';
    
    const bytesNumber = parseInt(bytes);
    if (isNaN(bytesNumber)) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytesNumber) / Math.log(k));
    
    return parseFloat((bytesNumber / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 获取菜单显示名称
const getMenuDisplayName = (menu: string) => {
    const menuNames: Record<string, string> = {
        resource: '资源库',
        material: '素材库',
        avatar: '数字人',
        voice: '智能配音',
        subtitle: '字幕',
        sticker: '贴纸',
        filter: '滤镜'
    };
    return menuNames[menu] || menu;
};

// 组件挂载时加载素材
onMounted(() => {
    // 进入页面时，只要有工程ID就直接加载素材（无论云剪辑还是模板工厂都需要）
    // 这个页面就是为了编辑工程，有工程ID就应该加载对应素材
    if (videoEditorStore.editingProjectId || videoEditorStore.editingTemplateId) {
        console.log('📋 组件挂载，检测到工程ID，开始加载素材:', videoEditorStore.editingProjectId);
        videoEditorStore.loadProjectMaterials();
    } else {
        console.log('⚠️ 组件挂载，未检测到工程ID，跳过素材加载');
    }
});

// 监听工程ID变化，自动重新加载素材
watch(() => videoEditorStore.editingProjectId, (newProjectId, oldProjectId) => {
    console.log('🔄 工程ID变化:', { oldProjectId, newProjectId });
    
    // 只要工程ID有变化且新值存在，就重新加载素材
    if (newProjectId && newProjectId !== oldProjectId) {
        console.log('📋 工程ID变化，重新加载素材');
        videoEditorStore.loadProjectMaterials();
    } else if (!newProjectId) {
        // 如果工程ID被清空，清空素材数据
        console.log('🧹 工程ID被清空，清空素材数据');
        videoEditorStore.projectMaterials.length = 0; // 直接清空数组
    }
}, { immediate: false });
</script>

<style lang="scss" scoped>
// 内容编辑面板样式
.content-editing-panel {
    flex: 1;
    min-height: 0;
    display: flex;
    overflow: hidden; /* 防止内容溢出 */

    .editing-menu {
        width: 120px;
        background-color: #1e2328;
        border-right: 1px solid #3a3f44;
        padding: 10px 0;
        flex-shrink: 0;

        .menu-item {
            padding: 12px 16px;
            color: #a8b2c2;
            cursor: pointer;
            font-size: 14px;
            border-radius: 4px;
            margin: 2px 8px;
            transition: all 0.2s ease;

            &:hover {
                background-color: #2a2f34;
                color: #ffffff;
            }

            &.active {
                background-color: #1378f9;
                color: #ffffff;
            }
        }
    }


    .editing-content {
        flex: 1;
        min-height: 0;
        height: calc(100% - 10px); /* 确保占满除菜单外的高度 */
        display: flex;
        flex-direction: column;
        background-color: #24292e;
        overflow: hidden; /* 确保内容不会溢出 */


        .resource-library {
            flex: 1;
            min-height: 300px; /* 确保有足够的高度 */
            height: 100%; /* 占满父容器高度 */
            display: flex;
            flex-direction: column;

            .resource-tabs {
                display: flex;
                align-items: center;
                padding: 12px 16px;
                border-bottom: 1px solid #3a3f44;
                background-color: #2a2f34;
                flex-shrink: 0;

                .resource-tab {
                    padding: 8px 16px;
                    margin-right: 8px;
                    background-color: #24292e;
                    color: #a8b2c2;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 13px;
                    transition: all 0.2s ease;
                    border: 1px solid #3a3f44;

                    &:hover {
                        background-color: #2e3439;
                        color: #ffffff;
                        border-color: #4a5568;
                    }

                    &.active {
                        background-color: #1378f9;
                        color: #ffffff;
                        border-color: #1378f9;
                    }
                }

                .spacer {
                    flex: 1;
                }

                .import-btn {
                    --el-button-bg-color: #1378f9;
                    --el-button-border-color: #1378f9;
                    --el-button-hover-bg-color: #0d6efd;
                    --el-button-hover-border-color: #0d6efd;
                    --el-button-active-bg-color: #0a58ca;
                    --el-button-active-border-color: #0a58ca;
                    height: 32px;
                    font-size: 13px;
                }
            }
            .resource-content {
                flex: 1;
                // min-height: 300px; /* 确保有足够的高度 */
                height: 100%; /* 占满父容器高度 */
                display: flex;
                flex-direction: column;
                padding: 16px;
                overflow: hidden; /* 防止内容溢出 */
            }

            .resource-loading-container {
                flex: 1;
                min-height: 300px; /* 确保有足够的高度 */
                height: 100%; /* 占满父容器高度 */
                display: flex;
                flex-direction: column;
                overflow: hidden; /* 防止内容溢出 */
            }

            .resource-grid-container {
                flex: 1;
                min-height: 300px; /* 确保有足够的高度 */
                height: 100%; /* 占满父容器高度 */
                display: flex;
                flex-direction: column;
                overflow: hidden;
                position: relative; /* 为绝对定位的子元素提供参考 */
            }
            .resource-grid {
                display: grid;
                grid-template-columns:repeat(auto-fill, minmax(160px, 1fr));
                gap: 12px;
                height: 100%;
                padding: 4px 12px 4px 4px; /* 右侧增加padding，为滚动条留出空间 */
                min-height: 100%; /* 确保有足够的内容高度触发滚动 */
                margin-bottom: 20px; /* 底部增加间距，确保最后一行内容完全显示 */
                .resource-card {
                        background-color: #2a2f34;
                        border-radius: 8px;
                        overflow: hidden;
                        cursor: pointer;
                        transition: all 0.2s ease;
                        border: 2px solid transparent;

                        &:hover {
                            background-color: #2e3439;
                            transform: translateY(-2px);
                            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

                            .hover-overlay {
                                opacity: 1;
                            }
                        }

                        &.selected {
                            border-color: #1378f9;
                            background-color: #2e3439;

                            .resource-title {
                                color: #1378f9;
                            }
                        }

                        .resource-thumbnail {
                            position: relative;
                            width: 100%;
                            height: 90px;
                            background-color: #1e2328;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            overflow: hidden;

                            img {
                                width: 100%;
                                height: 100%;
                                object-fit: cover;
                            }

                            .media-placeholder {
                                width: 100%;
                                height: 100%;
                                background: linear-gradient(135deg, #2a2f34 0%, #1e2328 100%);
                                display: flex;
                                align-items: center;
                                justify-content: center;

                                &.video {
                                    background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
                                }

                                &.audio {
                                    background: linear-gradient(135deg, #553c9a 0%, #3c2563 100%);
                                }

                                &.image {
                                    background: linear-gradient(135deg, #059669 0%, #047857 100%);
                                }

                                .media-icon {
                                    font-size: 24px;
                                    opacity: 0.8;
                                }
                            }

                            .duration-tag {
                                position: absolute;
                                bottom: 4px;
                                right: 4px;
                                background-color: rgba(0, 0, 0, 0.8);
                                color: #ffffff;
                                padding: 2px 6px;
                                border-radius: 4px;
                                font-size: 11px;
                                font-weight: 500;
                            }

                            .size-tag {
                                position: absolute;
                                top: 4px;
                                right: 4px;
                                background-color: rgba(0, 0, 0, 0.6);
                                color: #ffffff;
                                padding: 2px 6px;
                                border-radius: 4px;
                                font-size: 10px;
                                font-weight: 400;
                            }

                            .status-tag {
                                position: absolute;
                                top: 4px;
                                left: 4px;
                                padding: 2px 6px;
                                border-radius: 4px;
                                font-size: 10px;
                                font-weight: 500;

                                &.transcoding {
                                    background-color: #f59e0b;
                                    color: #ffffff;
                                }

                                &.transcodefailed {
                                    background-color: #ef4444;
                                    color: #ffffff;
                                }

                                &.init {
                                    background-color: #6b7280;
                                    color: #ffffff;
                                }
                            }

                            .hover-overlay {
                                position: absolute;
                                top: 0;
                                left: 0;
                                right: 0;
                                bottom: 0;
                                background-color: rgba(0, 0, 0, 0.6);
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                opacity: 0;
                                transition: opacity 0.2s ease;

                                .play-icon {
                                    font-size: 32px;
                                    color: #ffffff;
                                }
                            }
                        }

                        .resource-title {
                            padding: 8px 12px 4px;
                            color: #e0e0e0;
                            font-size: 12px;
                            line-height: 1.4;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            transition: color 0.2s ease;
                        }

                        .resource-meta {
                            padding: 0 12px 8px;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            font-size: 10px;

                            .media-type {
                                background-color: #1378f9;
                                color: #ffffff;
                                padding: 2px 6px;
                                border-radius: 12px;
                                font-weight: 500;
                            }

                            .category {
                                color: #a8b2c2;
                                opacity: 0.8;
                            }
                        }
                    }
                }

                .resource-empty-state {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    flex: 1;
                    min-height: 300px; /* 确保有足够的高度 */
                    height: 100%; /* 占满父容器高度 */
                    color: #a8b2c2;
                    padding: 20px;

                    .empty-icon {
                        font-size: 48px;
                        margin-bottom: 16px;
                        opacity: 0.6;
                    }

                    .empty-text {
                        font-size: 16px;
                        margin-bottom: 8px;
                        color: #e0e0e0;
                    }

                    .empty-hint {
                        font-size: 14px;
                        opacity: 0.8;
                    }
                }
            }
        }
        .other-content {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px 20px;
            .content-placeholder {
                text-align: center;
                color: #a8b2c2;
                .placeholder-icon {
                    font-size: 48px;
                    margin-bottom: 16px;
                    opacity: 0.6;
                }
                .placeholder-text {
                    font-size: 16px;
                    margin-bottom: 8px;
                    color: #e0e0e0;
                }
                .placeholder-hint {
                    font-size: 14px;
                    opacity: 0.8;
                }
            }
        }
    }
</style>
