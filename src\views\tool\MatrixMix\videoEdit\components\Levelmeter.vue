<template>
    <div class="audio-meter-container">
        <div class="meter-wrapper">
            <div class="meter-scale">
                <div class="scale-item" v-for="(mark, index) in scaleMarks" :key="index">
                    <span class="scale-number">{{ mark }}</span>
                    <div class="scale-line"></div>
                </div>
            </div>
            <div class="meter-bars">
                <div class="meter-bar left-channel">
                    <div class="bar-container">
                        <div v-for="(bar, index) in meterBars" :key="`left-${index}`" class="bar-segment" :class="{
                            'active': leftLevel > bar.threshold,
                            'green': bar.color === 'green',
                            'yellow': bar.color === 'yellow',
                            'red': bar.color === 'red'
                        }"></div>
                    </div>
                    <div class="bar-label">L</div>
                </div>
                <div class="meter-bar right-channel">
                    <div class="bar-container">
                        <div v-for="(bar, index) in meterBars" :key="`right-${index}`" class="bar-segment" :class="{
                            'active': rightLevel > bar.threshold,
                            'green': bar.color === 'green',
                            'yellow': bar.color === 'yellow',
                            'red': bar.color === 'red'
                        }"></div>
                    </div>
                    <div class="bar-label">R</div>
                </div>
            </div>
        </div>
        <div class="meter-title">电平表</div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, defineProps, defineEmits, PropType } from 'vue'

// 定义组件的 props
const props = defineProps({
    // 关联的 video 元素
    videoElement: {
        type: Object as PropType<HTMLVideoElement | null>,
        default: null
    },
    // 视频是否正在播放
    isPlaying: {
        type: Boolean,
        default: false
    },
    // 【新增】视频音量
    volume: {
        type: Number,
        default: 1
    },
    // 【新增】是否静音
    isMuted: {
        type: Boolean,
        default: false
    }
})

// 定义组件的 emits 事件
const emit = defineEmits(['audio-initialized', 'audio-error'])

// 响应式数据，用于存储左右声道的音频电平
const leftLevel = ref(0)
const rightLevel = ref(0)
// requestAnimationFrame 的 ID，用于在组件卸载时取消动画
let animationId: number | null = null
// Web Audio API 的核心对象
let audioContext: AudioContext | null = null
// 音频分析节点
let analyser: AnalyserNode | null = null
// 音频源节点
let source: MediaElementAudioSourceNode | null = null
// 标记是否存在音频轨道
let hasAudio = ref(false)

// 刻度标记，用于在 UI 上显示分贝刻度
const scaleMarks = [0, -6, -12, -18, -24, -30, -36, -42, -48, -54, -60]

// 电平条的配置数组，采用您修改后的版本
const meterBars = [
    // 红色区域
    { threshold: 92, color: 'red' },
    { threshold: 84, color: 'red' },
    // 黄色区域
    { threshold: 76, color: 'yellow' },
    { threshold: 68, color: 'yellow' },
    { threshold: 60, color: 'yellow' },
    // 绿色区域
    { threshold: 52, color: 'green' },
    { threshold: 44, color: 'green' },
    { threshold: 36, color: 'green' },
    { threshold: 28, color: 'green' },
    { threshold: 20, color: 'green' },
    { threshold: 12, color: 'green' },
    { threshold: 4, color: 'green' },
]

// 初始化音频上下文和分析器
const initAudioContext = async () => {
    try {
        if (!props.videoElement) return

        // 如果音频源已存在，说明已经初始化，直接返回，避免重复创建
        if (source) {
            return
        }

        const video = props.videoElement

        // 创建音频上下文（如果尚未创建）
        if (!audioContext) {
            const AudioContext = window.AudioContext || (window as any).webkitAudioContext;
            audioContext = new AudioContext()
        }

        // 创建分析器（如果尚未创建）
        if (!analyser) {
            analyser = audioContext.createAnalyser()
            analyser.fftSize = 512 // FFT 窗口大小
            analyser.smoothingTimeConstant = 0.3 // 平滑常数
        }

        // 将 video 元素作为音频源
        source = audioContext.createMediaElementSource(video)
        // 连接音频源到分析器，再连接到输出设备
        source.connect(analyser)
        analyser.connect(audioContext.destination)

        hasAudio.value = true
        emit('audio-initialized', true) // 发送初始化成功事件
        console.log('音频分析器初始化成功')
    } catch (error) {
        console.error('音频分析器初始化失败:', error)
        hasAudio.value = false
        emit('audio-error', error) // 发送初始化失败事件
        // 如果Web Audio API失败，则在后续分析中使用模拟数据
        console.log('使用模拟音频数据')
    }
}

// 实时分析音频电平
const analyzeAudioLevel = () => {
    // 如果视频未播放、已静音或音量为0，则强制电平表归零
    if (!props.isPlaying || props.isMuted || props.volume === 0) {
        leftLevel.value = 0
        rightLevel.value = 0
    } else if (analyser && hasAudio.value) {
        // 真实分析: MediaElementAudioSourceNode 会自动处理音量，无需额外操作
        const bufferLength = analyser.frequencyBinCount
        const dataArray = new Uint8Array(bufferLength)
        analyser.getByteFrequencyData(dataArray) // 获取频域数据
        
        // 计算平均电平
        let sum = 0
        for (let i = 0; i < bufferLength; i++) {
            sum += dataArray[i]
        }
        const average = sum / bufferLength
        
        // 将平均值映射到 0-100 的范围，并为左右声道添加随机变化以模拟立体声效果
        const baseLevel = (average / 255) * 100
        leftLevel.value = Math.min(100, baseLevel + Math.random() * 15)
        rightLevel.value = Math.min(100, baseLevel + Math.random() * 15)
    } else if (props.isPlaying) {
        // 模拟数据: 需要手动乘以音量
        const time = Date.now() / 1000
        const baseLeft = 30 + Math.sin(time * 2) * 20 + Math.random() * 30
        const baseRight = 30 + Math.cos(time * 2.2) * 20 + Math.random() * 30
        
        // 应用音量
        leftLevel.value = Math.max(0, Math.min(100, baseLeft * props.volume))
        rightLevel.value = Math.max(0, Math.min(100, baseRight * props.volume))
        
        hasAudio.value = true // 假设有音频
    }
    
    // 使用 requestAnimationFrame 创建动画循环，以高效地更新UI
    animationId = requestAnimationFrame(analyzeAudioLevel)
}

// 暴露给父组件的方法，用于手动触发音频初始化
const initAudio = async () => {
    await initAudioContext()
}

// 暴露给父组件的方法，用于在用户交互后恢复被挂起的音频上下文
const resumeAudioContext = async () => {
    if (audioContext && audioContext.state === 'suspended') {
        await audioContext.resume()
    }
}

// 使用 defineExpose 将方法暴露给父组件
defineExpose({
    initAudio,
    resumeAudioContext
})

// 组件挂载后，开始音频电平分析循环
onMounted(() => {
    analyzeAudioLevel()
})

// 组件卸载时，清理资源
onUnmounted(() => {
    if (animationId) {
        cancelAnimationFrame(animationId) // 取消动画帧
    }
    if (audioContext) {
        audioContext.close() // 关闭音频上下文
    }
})
</script>

<style lang="scss" scoped>
.audio-meter-container {
    display: flex;
    flex-direction: column;
    height: 100%; /* 撑满父容器高度 */
    padding: 5px;
    box-sizing: border-box;
    background-color: #2c3e50;
    border-radius: 5px;
}

.meter-wrapper {
    display: flex;
    flex-grow: 1; /* 占据所有可用垂直空间 */
    min-height: 0; /* 允许在 flex 容器中收缩 */
}

.meter-title {
    color: #ffffff;
    font-size: 16px;
    margin-bottom: 8px; /* 调整间距 */
    text-align: center;
    flex-shrink: 0; /* 不收缩 */
}

.meter-scale {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%; /* 撑满 wrapper 高度 */
    width: 30px;
    margin-right: 5px;

    .scale-item {
        display: flex;
        align-items: center;
        gap: 1px;
        color: #95a5a6;
        font-size: 10px;

        .scale-number {
            width: 20px;
            text-align: right;
        }

        .scale-line {
            width: 5px;
            height: 1px;
            background-color: #555;
        }
    }
}

.meter-bars {
    display: flex;
    gap: 8px;
    flex-grow: 1; /* 占据 wrapper 宽度 */
}

.meter-bar {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%; /* 改为百分比宽度 */

    .bar-container {
        display: flex;
        flex-direction: column; /* 改回 column，确保从上往下排列 */
        width: 100%;
        height: 100%;
        background-color: #1a1a1a;
        border-radius: 3px;
        padding: 2px;
        gap: 1px;
    }

    .bar-label {
        color: #ffffff;
        font-size: 12px;
        margin-top: 8px;
        font-weight: bold;
        flex-shrink: 0;
    }

    .bar-segment {
        height: 100%; /* 改为百分比高度，由 flex 控制 */
        flex-grow: 1; /* 填充可用空间 */
        width: 100%;
        background-color: #333;
        border-radius: 1px;
        transition: background-color 0.1s ease;

        &.active {
            &.green {
                background-color: #4CAF50;
                box-shadow: 0 0 3px #4CAF50;
            }

            &.yellow {
                background-color: #FFC107;
                box-shadow: 0 0 3px #FFC107;
            }

            &.red {
                background-color: #F44336;
                box-shadow: 0 0 3px #F44336;
            }
        }
    }
}
</style>