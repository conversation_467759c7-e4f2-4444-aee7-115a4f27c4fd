<template>
    <el-dialog
        v-model="dialogVisible"
        title="高级设置"
        width="500px"
        :close-on-click-modal="false"
        @close="handleClose"
    >
        <el-form :model="form" label-width="180px" label-position="left">
            <!-- 时长超出处理：当替换视频时长大于模板槽位时的处理方式 -->
            <el-form-item label="替换视频时长大于模板槽位">
                <el-select v-model="form.longVideoHandling" placeholder="请选择处理方式">
                    <el-option label="根据模板槽位适应（默认）" value="default" />
                    <el-option label="超出部分截断" value="TruncateExcessPart" />
                    <el-option label="随机截取片段" value="RandomTruncate" />
                </el-select>
            </el-form-item>
            <!-- 时长不足处理：当替换视频时长小于模板槽位时的处理方式 -->
            <el-form-item label="替换视频时长小于模板槽位">
                <el-select v-model="form.shortVideoHandling" placeholder="请选择处理方式">
                    <el-option label="根据模板槽位适应（默认）" value="default" />
                    <el-option label="空白区域填充黑帧" value="FillBlackFrame" />
                    <el-option label="空白区域补充最后一帧" value="FillLastFrame" />
                </el-select>
            </el-form-item>
            <!-- 缩放适配处理：视频尺寸与模板尺寸不匹配时的处理方式 -->
            <el-form-item label="替换视频缩放尺寸">
                <el-select v-model="form.adaptMode" placeholder="请选择适配模式">
                    <el-option label="拉伸（默认）" value="Fill" />
                    <el-option label="等比例缩放至内容可全部展示的最大效果" value="Contain" />
                    <el-option label="等比例缩放至撑满全画面" value="Cover" />
                </el-select>
            </el-form-item>
        </el-form>

        <template #footer>
            <el-button @click="handleClose">取消</el-button>
            <el-button type="primary" @click="handleConfirm">确定</el-button>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, defineProps, defineEmits, withDefaults } from 'vue';

interface Props {
    visible: boolean;
    settings?: Record<string, any>;
}

const props = withDefaults(defineProps<Props>(), {
    settings: () => ({}),
});

const emit = defineEmits(['update:visible', 'confirm']);

const dialogVisible = ref(props.visible);

// 简化的表单数据
const form = ref({
    longVideoHandling: 'default',
    shortVideoHandling: 'default',
    adaptMode: 'Fill',
});

// 监听 props.visible 的变化来同步 dialogVisible
watch(() => props.visible, (newVal) => {
    dialogVisible.value = newVal;
    if (newVal) {
        // 当对话框打开时，从传入的设置中初始化表单数据
        
        // 映射常见的字段名到表单字段（支持中文和英文字段名）
        // 优先级：Effects[0] > 中文字段 > 英文字段 > 默认值
        
        // 长视频处理逻辑 - 按优先级获取值
        let longValue = 'default';
        if (props.settings.Effects && props.settings.Effects[0] && props.settings.Effects[0].MaterialLongerThanInterval) {
            longValue = props.settings.Effects[0].MaterialLongerThanInterval;
        } else if (props.settings['替换视频时长大于模板槽位']) {
            longValue = props.settings['替换视频时长大于模板槽位'];
        } else if (props.settings['LongVideoHandling']) {
            longValue = props.settings['LongVideoHandling'];
        }
        
        // 短视频处理逻辑 - 按优先级获取值
        let shortValue = 'default';
        if (props.settings.Effects && props.settings.Effects[0] && props.settings.Effects[0].MaterialShorterThanInterval) {
            shortValue = props.settings.Effects[0].MaterialShorterThanInterval;
        } else if (props.settings['替换视频时长小于模板槽位']) {
            shortValue = props.settings['替换视频时长小于模板槽位'];
        } else if (props.settings['ShortVideoHandling']) {
            shortValue = props.settings['ShortVideoHandling'];
        }
        
        // 缩放模式处理逻辑 - 按优先级获取值
        let adaptValue = 'Fill';
        if (props.settings['AdaptMode']) {
            adaptValue = props.settings['AdaptMode'];
        } else if (props.settings['替换视频缩放尺寸']) {
            adaptValue = props.settings['替换视频缩放尺寸'];
        }
        
        // 数据验证和标准化
        const validLongValues = ['default', 'TruncateExcessPart', 'RandomTruncate'];
        const validShortValues = ['default', 'FillBlackFrame', 'FillLastFrame'];
        const validAdaptValues = ['Fill', 'Contain', 'Cover'];
        
        if (!validLongValues.includes(longValue)) {
            longValue = 'default';
        }
        
        if (!validShortValues.includes(shortValue)) {
            shortValue = 'default';
        }
        
        if (!validAdaptValues.includes(adaptValue)) {
            adaptValue = 'Fill';
        }
        
        form.value.longVideoHandling = longValue;
        form.value.shortVideoHandling = shortValue;
        form.value.adaptMode = adaptValue;
    }
});

// 监听 dialogVisible 的变化来通知父组件
watch(dialogVisible, (newVal) => {
    emit('update:visible', newVal);
});

const handleClose = () => {
    dialogVisible.value = false;
};

const handleConfirm = () => {
    // 创建全新的简化设置对象，完全避免继承任何多余字段
    const newSettings: any = {};
    
    // 手动复制必要的基础字段
    const essentialFields = [
        'Id', 'TrackId', 'Type', 'TimelineIn', 'TimelineOut', '_hasTranscodedAudio',
        'In', 'VirginDuration', 'Title', 'Duration', 'Out', 'MediaId',
        'X', 'Y', 'Height', 'Width', 'IsFromTemplate', 'TemplateReplaceable',
        'TemplateMaterialId', 'TemplateRemark', 'FileName', 'FileUrl'
    ];
    
    essentialFields.forEach(field => {
        if (props.settings[field] !== undefined) {
            newSettings[field] = props.settings[field];
        }
    });
    
    // 保留 EffectPriority（如果存在）
    if (props.settings.EffectPriority) {
        newSettings.EffectPriority = props.settings.EffectPriority;
    }
    
    // 添加高级设置字段 - 使用与 user1.mp4 完全一致的结构
    newSettings.AdaptMode = form.value.adaptMode;
    newSettings['替换视频时长大于模板槽位'] = form.value.longVideoHandling;
    newSettings['替换视频时长小于模板槽位'] = form.value.shortVideoHandling;
    newSettings['替换视频缩放尺寸'] = form.value.adaptMode;
    
    emit('confirm', newSettings);
    handleClose();
};

</script>

<style scoped lang="scss">
.el-select {
    width: 100%;
}
</style>
