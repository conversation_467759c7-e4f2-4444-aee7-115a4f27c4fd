<template>
  <el-dialog
    :model-value="visible"
    title="另存为模板"
    width="500px"
    @close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form :model="form" ref="formRef" label-width="100px" v-loading="loading">
      <el-form-item
        label="模板名称"
        prop="name"
        :rules="[{ required: true, message: '模板名称不能为空', trigger: 'blur' }]"
      >
        <el-input v-model="form.name" placeholder="请输入模板名称"></el-input>
      </el-form-item>
      <el-form-item label="封面URL" prop="coverUrl">
        <el-input v-model="form.coverUrl" placeholder="请输入封面图片URL"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import { ElMessage, type FormInstance } from 'element-plus';
import { addTemplate } from '../../api/template';
import type { AddTemplateRequest } from '../../types/template';
import type { Timeline } from '../../types/videoEdit';

const props = defineProps<{
  visible: boolean;
  timeline: Timeline | null;
  previewMediaId: string | null;
}>();

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
}>();

const formRef = ref<FormInstance>();
const loading = ref(false);
const form = reactive({
  name: '',
  coverUrl: '',
});

watch(() => props.visible, (newVal) => {
  if (newVal) {
    formRef.value?.resetFields();
    form.name = '';
    form.coverUrl = '';
  }
});

const handleClose = () => {
  emit('update:visible', false);
};

const handleSubmit = async () => {
  if (!formRef.value) return;
  await formRef.value.validate(async (valid) => {
    if (valid) {
      if (!props.timeline) {
        ElMessage.error('无法创建模板，因为缺少Timeline数据。');
        return;
      }

      loading.value = true;
      try {
        const templateData = buildTemplateData(props.timeline, form.name, form.coverUrl, props.previewMediaId);
        const res = await addTemplate(templateData);
        if (res.code === 200) {
          ElMessage.success('模板创建成功');
          handleClose();
        } else {
          ElMessage.error(`模板创建失败: ${res.msg}`);
        }
      } catch (error) {
        ElMessage.error('创建模板时发生网络错误');
        console.error('Failed to create template:', error);
      } finally {
        loading.value = false;
      }
    }
  });
};

function buildTemplateData(timeline: Timeline, name: string, coverUrl: string, previewMedia: string | null): AddTemplateRequest {
  const config = JSON.parse(JSON.stringify(timeline));

  let relatedMediaIds: string[] = [];

  if (config.VideoTracks && config.VideoTracks.length > 0) {
    // 假设我们只处理第一个视频轨道
    const mainVideoTrack = config.VideoTracks[0];
    if (mainVideoTrack.VideoTrackClips && mainVideoTrack.VideoTrackClips.length > 0) {
      const clips = mainVideoTrack.VideoTrackClips;
      const placeholder = {
        "Sys_Type": "ArrayItems",
        "Sys_ArrayObject": "$VideoArray",
        "Sys_Template": {
          "MediaId": "$MediaId"
        }
      };
      
      // 提取中间素材ID (用于 RelatedMediaids)
      if (clips.length > 2) {
        relatedMediaIds = clips.slice(1, -1).map((clip: any) => clip.MediaId);
      }
      
      // 构建新的模板轨道 (Config)
      if (clips.length <= 1) {
        // 如果只有一个或零个片段，则模板就是一个纯粹的占位符
        mainVideoTrack.VideoTrackClips = [placeholder];
      } else {
        // 如果有多个片段，则将第一个和最后一个作为片头片尾，中间插入占位符
        const firstClip = clips[0];
        const lastClip = clips[clips.length - 1];
        mainVideoTrack.VideoTrackClips = [firstClip, placeholder, lastClip];
      }
    }
  }

  const templateRequest: AddTemplateRequest = {
    Name: name,
    Type: 'Timeline',
    Config: JSON.stringify(config),
    CoverUrl: coverUrl || undefined,
    PreviewMedia: previewMedia || undefined,
    RelatedMediaids: relatedMediaIds.length > 0 ? JSON.stringify({ video: relatedMediaIds }) : undefined,
    Source: 'WebSDK',
    Variables: JSON.stringify(config).includes('$VideoArray') 
      ? { "VideoArray": { "Type": "video" } } 
      : undefined,
  };

  return templateRequest;
}

</script> 