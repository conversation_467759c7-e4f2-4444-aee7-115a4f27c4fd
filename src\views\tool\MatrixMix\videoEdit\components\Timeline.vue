<!--
  Timeline.vue
  ==============
  这是一个功能全面的时间轴组件，用于视频编辑界面。它负责以下核心功能：
  1.  **显示控件**: 提供播放、暂停和重置视频的按钮。
  2.  **时间显示**: 展示当前播放时间和视频总时长。
  3.  **多轨道布局**: 使用固定的轨道头部（视频、音频、字幕）和可水平滚动的内容区域，实现经典编辑软件布局。
  4.  **时间标尺**: 渲染一个动态的时间标尺，用户可以直观地看到时间刻度。
  5.  **素材片段渲染**: 从传入的 `videoEditorStore.timeline` 数据中，解析视频、音频、字幕等轨道信息，并将它们作为"片段"渲染在对应的轨道上。
  6.  **播放头**: 显示一个红色的播放头，它的位置会根据当前播放时间实时更新。
  7.  **用户交互**:
      *   允许用户点击时间轴的任意位置来"搜寻"(seek)到特定时间点。
      *   允许用户点击并选中某个素材片段。
  8.  **状态管理**: 通过 Pinia Store (`useVideoEditorStore`) 集中管理播放状态、时间轴数据和选中片段信息。
-->
<template>
    <div class="timeline-component">
        <!-- ======================================================================= -->
        <!-- 1. 顶部工具栏 (Header) -->
        <!-- ======================================================================= -->
        <!-- 这部分包含了播放控制和时间显示，是用户与播放器交互的主要入口。 -->
        <div class="timeline-header">
            <!-- 播放/暂停/重置 控制按钮 -->
            <div class="controls">
                <!--
                  播放/暂停按钮
                  - 恢复为单个按钮，通过调用 togglePlayPause 方法来切换状态。
                  - :title 和图标直接读取 store 的 isPlaying 状态。
                -->
                <button @click="videoEditorStore.togglePlayPause" :title="videoEditorStore.isVideoPlaying ? '暂停' : '播放'">
                    <el-icon>
                        <VideoPause v-if="videoEditorStore.isVideoPlaying" />
                        <VideoPlay v-else />
                    </el-icon>
                </button>
                <!-- 回到开头按钮，调用 store 的 action -->
                <button @click="videoEditorStore.handleReset" title="回到开头">
                    <el-icon>
                        <RefreshLeft />
                    </el-icon>
                </button>
                <!-- 切割按钮 点一次切割一次  -->
                <button @click="videoEditorStore.handleCut" title="切割"> <!-- 修改：直接调用 store 的 handleCut -->
                    <el-icon>
                        <Scissor />
                    </el-icon>
                </button>
                <!-- 删除按钮 -->
                <button @click="videoEditorStore.handleDeleteClip" title="删除片段">
                    <el-icon>
                        <Delete />
                    </el-icon>
                </button>
                <!-- 时间显示，直接读取 store 的 getter -->
                <span class="time-display">
                    {{ videoEditorStore.currentTimeFormatted }} / {{ videoEditorStore.videoDurationFormatted }}
                </span>
            </div>
        </div>

        <!-- ======================================================================= -->
        <!-- 2. 时间轴主体 (Body) -->
        <!-- ======================================================================= -->
        <!-- 这是时间轴的核心区域，由左侧固定的轨道标题和右侧可滚动的内容区组成。 -->
        <div class="timeline-body">
            <!-- 2.1 左侧固定的轨道头部 (Track Headers) -->
            <!-- 这部分在垂直方向上与右侧的轨道对齐，但在水平方向上是固定的，不会随内容滚动。 -->
            <div class="track-headers">
                <!-- 这是一个占位符，其高度与右侧的时间标尺(ruler)相同，以确保轨道标题和轨道内容垂直对齐。 -->
                <div class="header-placeholder"></div>
                <!-- 视频、音频、字幕轨道的标题 -->
                <div class="track-header">
                    <i class="el-icon-video-camera track-icon"></i>
                    <span>视频轨道</span>
                </div>
                <div class="track-header">
                    <i class="el-icon-headset track-icon"></i>
                    <span>音频轨道</span>
                </div>
                <div class="track-header">
                    <i class="el-icon-chat-line-square track-icon"></i>
                    <span>字幕轨道</span>
                </div>
            </div>

            <!-- 2.2 右侧可滚动区域 (Scrollable Area) -->
            <!-- 这个容器包含了所有会水平滚动的内容：时间标尺、所有轨道和播放头。 -->
            <!-- `ref="scrollAreaRef"` 用于获取该元素的引用，方便后续计算。 -->
            <!-- `@scroll="handleScroll"` 监听滚动事件，以备将来需要同步滚动等复杂操作。 -->
            <div class="timeline-scroll-area" ref="scrollAreaRef" @scroll="handleScroll">
                <!--
                  时间标尺 (Ruler)
                  - `@mousedown="handleTimelineInteraction"`: 监听鼠标按下事件。这是实现点击跳转(seek)的关键。
                  - `:style` 动态计算其总宽度。总宽度 = 标尺标记数量 * 每秒像素值。这确保了标尺和轨道内容一样长。
                -->
                <div class="ruler" @mousedown="handleTimelineInteraction"
                    :style="{ width: `${rulerMarkers.length * PIXELS_PER_SECOND}px` }">
                    <!--
                      标尺刻度 (Ruler Mark)
                      - `v-for` 遍历 `rulerMarkers` 计算属性来生成每个刻度。
                      - 每个刻度的宽度固定为 `PIXELS_PER_SECOND`，代表一秒钟的长度。
                      - `v-if="marker.showLabel"`: 控制是否显示时间标签，避免过于密集。这里是每5秒显示一次。
                    -->
                    <div class="ruler-mark" v-for="marker in rulerMarkers" :key="`ruler-${marker.second}`"
                        :style="{ width: `${PIXELS_PER_SECOND}px` }">
                        <span class="time" v-if="marker.showLabel">{{ formatRulerTime(marker.second) }}</span>
                    </div>
                </div>

                <!--
                  所有轨道集合 (Tracks Container)
                  - 这个容器的总宽度同样由视频总时长决定，与标尺宽度保持一致。
                -->
                <div class="tracks" :style="{ width: `${rulerMarkers.length * PIXELS_PER_SECOND}px` }">
                    <!-- 视频轨道 -->
                    <div class="track">
                        <!--
                          视频片段 (Video Clip)
                          - `v-for` 遍历从 `videoEditorStore.timeline` 解析出的 `videoClips` 数组。
                          - `:style="getClipStyle(clip)"`: 动态计算每个片段的 `left` 和 `width`，决定其在时间轴上的位置和长度。
                          - `@mousedown.stop="startClipDrag($event, clip, 'video', index)"`: **点击和拖拽的统一入口**，并阻止事件冒泡。
                          - `:class="{ selected: ..., 'is-dragging': isActualDragging && draggedClipData?.type === 'video' && draggedClipData?.index === index }"`: 如果片段被选中或正在被拖拽，则添加相应的类以高亮显示。
                        -->
                        <div v-for="(clip, index) in videoClips" :key="`video-${index}`" class="clip video-clip"
                            :style="getClipStyle(clip)"
                            @mousedown.stop="startClipDrag($event, clip, 'video', index)"
                            :class="{ selected: isSelected('video', index), 'is-dragging': isActualDragging && draggedClipData?.type === 'video' && draggedClipData?.index === index }">
                            <span>{{ clip.name }}</span>
                        </div>
                    </div>
                    <!-- 音频轨道 (结构与视频轨道完全相同) -->
                    <div class="track">
                        <div v-for="(clip, index) in audioClips" :key="`audio-${index}`" class="clip audio-clip"
                            :style="getClipStyle(clip)"
                            @mousedown.stop="startClipDrag($event, clip, 'audio', index)"
                            :class="{ selected: isSelected('audio', index), 'is-dragging': isActualDragging && draggedClipData?.type === 'audio' && draggedClipData?.index === index }">
                            <span>{{ clip.name }}</span>
                        </div>
                    </div>
                    <!-- 字幕轨道 (结构与视频轨道完全相同) -->
                    <div class="track">
                        <div v-for="(clip, index) in subtitleClips" :key="`subtitle-${index}`"
                            class="clip subtitle-clip" :style="getClipStyle(clip)"
                            @mousedown.stop="startClipDrag($event, clip, 'subtitle', index)"
                            :class="{ selected: isSelected('subtitle', index), 'is-dragging': isActualDragging && draggedClipData?.type === 'subtitle' && draggedClipData?.index === index }">
                            <span>{{ clip.name }}</span>
                        </div>
                    </div>
                </div>

                <!--
                  播放头 (Playhead)
                  - 这是一个独立的层，覆盖在所有轨道之上。
                  - 它的位置由 `playheadLeft` 计算属性动态控制，`left` 值与当前播放时间精确对应。
                -->
                <div class="playhead" :style="{ left: `${playheadLeft}px` }">
                    <div class="playhead-top"></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, type ComputedRef } from 'vue' // 导入 ComputedRef 类型
import { ElIcon } from 'element-plus'
import { VideoPlay, VideoPause, RefreshLeft, Scissor, Delete } from '@element-plus/icons-vue' // 导入 Scissor, Delete
import { useVideoEditorStore } from '../useVideoEditor'
import { formatRulerTime } from '../../utils/timeUtils'
import type { Timeline, TimelineClip, SelectedClip, RulerMarker } from '../../types/videoEdit'; // 导入 RulerMarker

// --- Pinia Store ---
const videoEditorStore = useVideoEditorStore();

// =======================================================================
// Props & Emits (已移除，现在由 Pinia Store 集中管理)
// =======================================================================
// const props = defineProps<{
//     timeline: Timeline | null;
// }>();
// const emit = defineEmits<{
//     (e: 'clip-select', payload: { type: 'video' | 'audio' | 'subtitle', index: number }): void
// }>();

// =======================================================================
// DOM & 状态引用 (Refs)
// =======================================================================

const scrollAreaRef = ref<HTMLElement | null>(null);

// =======================================================================
// 核心常量 (Constants)
// =======================================================================

const PIXELS_PER_SECOND = 50; // 每秒对应的像素值
const SNAPPING_THRESHOLD_PX = 8; // 吸附阈值，单位像素 (例如: 8px)
const DRAG_THRESHOLD_PX = 3; // 判定为"拖拽"所需的最小鼠标移动距离 (像素)

// =======================================================================
// 内部响应式状态 (Internal Reactive State)
// =======================================================================
// 新增拖拽相关状态
let isDragging = ref(false); // 标记当前是否正在进行 mousedown 操作 (无论是点击还是拖拽)
let isActualDragging = ref(false); // 新增：标记当前是否判定为实际的"拖拽"行为
let initialMouseX = 0; // 鼠标按下时的X坐标
let initialClipLeft = 0; // 片段在时间轴内的初始left值
let originalClipDuration = 0; // 被拖拽片段的原始持续时间 (秒)
let draggedClipData: { type: 'video' | 'audio' | 'subtitle', index: number } | null = null; // 被拖拽片段的类型和索引
let currentDraggedClipElement: HTMLElement | null = null; // 当前被拖拽的DOM元素引用
let hasPausedOnDrag = ref(false); // 新增：标记在本次拖拽中是否已经暂停过视频

// selectedClip 现在从 store 获取，不再作为组件内部状态，因此移除以下定义
// const selectedClip = ref<SelectedClip>({ type: null, index: null });

// toggleCuttingMode 占位函数已移除，因为切割逻辑直接由 store 的 handleCut 处理

// =======================================================================
// 计算属性 (Computed Properties)
// =======================================================================

/**
 * @computed rulerMarkers
 * @description 动态计算时间标尺上需要显示的所有刻度标记。
 * @returns {RulerMarker[]} 一个标记对象数组。
 * - `second`: 这个标记代表的秒数。
 * - `showLabel`: 是否应该在这个标记上显示时间文本（例如 "00:05"）。
 *
 * 工作原理：
 * 1. 它依赖于 `videoEditorStore.videoDurationSeconds`。如果总时长改变，这个计算属性会自动重新计算。
 * 2. `totalMarkers` 不仅覆盖了整个视频时长，还额外增加了20秒的缓冲区，确保时间轴末尾有留白，方便查看。
 * 3. 循环遍历每一秒，生成一个标记对象。
 * 4. `i % 5 === 0` 这个条件判断意味着每隔5秒的标记才会显示时间文本，防止标签过于拥挤。
 */
const rulerMarkers: ComputedRef<RulerMarker[]> = computed(() => { // 明确指定类型为 ComputedRef<RulerMarker[]>
    if (videoEditorStore.videoDurationSeconds <= 0) return [];
    const markers: RulerMarker[] = [];
    const totalDurationInSeconds = Math.ceil(videoEditorStore.videoDurationSeconds);
    const totalMarkers = totalDurationInSeconds + 20;

    for (let i = 0; i < totalMarkers; i++) {
        markers.push({
            second: i,
            showLabel: i % 5 === 0
        });
    }
    return markers;
});

// ProcessedClip 接口保持不变，但其数据来源将改变
interface ProcessedClip {
    name: string;
    start: number;
    duration: number;
}


/**
 * @computed videoClips, audioClips, subtitleClips
 * @description 这些计算属性负责从复杂的 `videoEditorStore.timeline` 对象中提取并转换出模板需要的数据格式。
 * @returns {ProcessedClip[]} 一个简化的片段数组，可以直接用于模板渲染。
 *
 * 工作原理：
 * 1. 它们都依赖于 `videoEditorStore.timeline`。当 `timeline` 数据更新时，它们会自动重新计算。
 * 2. `?.` (可选链操作符) 用于安全地访问深层嵌套的属性，如果中间某个属性（如 `VideoTracks`）不存在，表达式会返回 `undefined` 而不是报错。
 * 3. **核心逻辑**: 它们将原始 `timeline` 数据中的 `TimelineIn` 和 `TimelineOut` (片段在整个时间轴上的绝对开始和结束时间)
 *    转换为 `start` 和 `duration`，这种格式更适合 CSS 定位。
 */
const videoClips = computed<ProcessedClip[]>(() => {
    const videoTrack = videoEditorStore.timeline?.VideoTracks?.[0]?.VideoTrackClips;
    if (!videoTrack) return [];
    return videoTrack.map((clip: TimelineClip) => ({ // 明确指定 clip 类型
        name: clip.FileName || clip.MediaId,
        start: clip.TimelineIn,
        duration: clip.TimelineOut - clip.TimelineIn,
    }));
});

const audioClips = computed<ProcessedClip[]>((() => {
    const audioTrack = videoEditorStore.timeline?.AudioTracks?.[0]?.AudioTrackClips;
    if (!audioTrack) return [];
    return audioTrack.map((clip: TimelineClip) => ({ // 明确指定 clip 类型
        name: clip.FileName || clip.MediaId,
        start: clip.TimelineIn,
        duration: clip.TimelineOut - clip.TimelineIn,
    }));
}) as () => ProcessedClip[]); // 显式类型断言以避免 Vue 3.3+ 的类型推断问题

const subtitleClips = computed<ProcessedClip[]>(() => {
    const subtitleTrack = videoEditorStore.timeline?.SubtitleTracks?.[0]?.SubtitleTrackClips;
    if (!subtitleTrack) return [];
    return subtitleTrack.map((clip: TimelineClip) => ({ // 明确指定 clip 类型
        name: clip.Content || '字幕',
        start: clip.TimelineIn,
        duration: clip.TimelineOut - clip.TimelineIn,
    }));
});


/**
 * @computed playheadLeft
 * @description 计算播放头（playhead）的CSS `left` 属性值。
 * @returns {number} 播放头距离左侧的像素值。
 *
 * 工作原理：
 * 1. 它依赖于 `videoEditorStore.currentTime`。当播放时间变化时，该值自动更新。
 * 2. 公式非常简单：`left = 当前时间(秒) * 每秒对应的像素值`。
 *    这确保了播放头的位置与当前时间精确同步。
 */
const playheadLeft = computed<number>(() => {
    return videoEditorStore.currentTime * PIXELS_PER_SECOND;
});


// =======================================================================
// 方法 (Methods)
// =======================================================================

/**
 * @method handleTimelineInteraction
 * @description 处理用户在时间轴标尺区域的点击和拖拽事件，以实现跳转（seek）功能。
 * @param {MouseEvent} event - 鼠标 mousedown 事件对象。
 *
 * 工作原理：
 * 1. 在 `mousedown` 事件触发时，如果视频正在播放，则立即发送 `pause` 事件将其暂停。
 * 2. 立即根据点击位置计算时间并触发 `seek`。
 * 3. 在 `window` 上动态添加 `mousemove` 和 `mouseup` 事件监听器以处理拖拽。
 * 4. 当鼠标移动时 (`mousemove`)，实时计算新的时间点并再次触发 `seek`。
 * 5. 当鼠标松开时 (`mouseup`)，移除监听器，结束拖拽。视频将保持暂停状态，需要用户手动播放。
 * 6. 在整个过程中，会阻止默认事件（如文本选择），并确保计算出的时间被限制在 `[0, duration]` 范围内。
 */
const handleTimelineInteraction = (event: MouseEvent) => {
    // 如果正在拖拽片段，则不触发时间轴背景的交互
    if (isDragging.value) return;

    if (!scrollAreaRef.value) return;

    // 暂停视频（如果正在播放），直接调用 store 的 action
    if (videoEditorStore.isVideoPlaying) {
        videoEditorStore.handlePause();
    }

    event.preventDefault();

    const seekToEventTime = (e: MouseEvent) => {
        const rect = scrollAreaRef.value!.getBoundingClientRect();
        const scrollLeft = scrollAreaRef.value!.scrollLeft;
        const offsetX = e.clientX - rect.left + scrollLeft;
        let timeInSeconds = offsetX / PIXELS_PER_SECOND;

        timeInSeconds = Math.max(0, timeInSeconds);
        // 总时长从 store 获取
        if (videoEditorStore.videoDurationSeconds > 0) {
            timeInSeconds = Math.min(timeInSeconds, videoEditorStore.videoDurationSeconds);
        }

        // 直接调用 store 的 seek action
        videoEditorStore.handleSeek(timeInSeconds);
    };

    seekToEventTime(event);

    const handleMouseMove = (e: MouseEvent) => {
        e.preventDefault();
        seekToEventTime(e);
    };

    const handleMouseUp = () => {
        window.removeEventListener('mousemove', handleMouseMove);
        window.removeEventListener('mouseup', handleMouseUp);
    };

    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('mouseup', handleMouseUp);
};

/**
 * @method startClipDrag
 * @description 处理片段的 mousedown 事件，启动点击或拖拽的逻辑。
 * @param {MouseEvent} event - 鼠标事件对象。
 * @param {ProcessedClip} clip - 被点击的片段的简化数据。
 * @param {'video' | 'audio' | 'subtitle'} type - 片段类型。
 * @param {number} index - 片段在轨道中的索引。
 */
const startClipDrag = (event: MouseEvent, clip: ProcessedClip, type: 'video' | 'audio' | 'subtitle', index: number) => {
    // 阻止默认的浏览器拖拽行为（如拖拽图片）和事件冒泡到时间轴背景
    event.preventDefault();
    event.stopPropagation();

    isDragging.value = true; // 鼠标已按下
    isActualDragging.value = false; // 初始认为不是实际拖拽
    initialMouseX = event.clientX;
    currentDraggedClipElement = event.currentTarget as HTMLElement;
    initialClipLeft = currentDraggedClipElement.offsetLeft;
    originalClipDuration = clip.duration;
    draggedClipData = { type, index };

    // 在window上添加mousemove和mouseup监听器
    window.addEventListener('mousemove', handleClipDrag);
    window.addEventListener('mouseup', endClipDrag);
};

/**
 * @method handleClipDrag
 * @description 处理片段的 mousemove 事件，在拖拽过程中实时更新片段的视觉位置。
 *              包含吸附逻辑。
 * @param {MouseEvent} event - 鼠标事件对象。
 */
const handleClipDrag = (event: MouseEvent) => {
    if (!isDragging.value || !currentDraggedClipElement || !scrollAreaRef.value || !draggedClipData) return;

    const deltaX = event.clientX - initialMouseX;

    // 只有当鼠标移动超过一个阈值时，才判定为真正的"拖拽"
    if (!isActualDragging.value && Math.abs(deltaX) > DRAG_THRESHOLD_PX) {
        isActualDragging.value = true; // 确定为实际拖拽
        // 只有在确定为拖拽时才添加拖拽样式和暂停视频
        currentDraggedClipElement.classList.add('is-dragging');
        if (videoEditorStore.isVideoPlaying) {
            videoEditorStore.handlePause();
        }
    }

    // 只有在确定为实际拖拽后，才进行位置更新和吸附计算
    if (isActualDragging.value) {
        let newVisualLeft = initialClipLeft + deltaX;

        // 限制拖拽范围，确保片段不会拖到0秒之前
        newVisualLeft = Math.max(0, newVisualLeft);

        // ===============================================================
        // 吸附逻辑 (Snapping Logic)
        // ===============================================================
        const snapPoints: number[] = []; // 存储所有潜在的吸附点 (像素位置)

        // 收集所有片段的边界作为吸附点
        const allTimelineClips: ({ type: 'video' | 'audio' | 'subtitle', index: number } & ProcessedClip)[] = [];

        if (videoEditorStore.timeline?.VideoTracks?.[0]?.VideoTrackClips) {
            videoEditorStore.timeline.VideoTracks[0].VideoTrackClips.forEach((c: TimelineClip, i: number) => {
                allTimelineClips.push({
                    type: 'video',
                    index: i,
                    name: c.FileName || c.MediaId,
                    start: c.TimelineIn,
                    duration: c.TimelineOut - c.TimelineIn,
                });
            });
        }
        if (videoEditorStore.timeline?.AudioTracks?.[0]?.AudioTrackClips) {
            videoEditorStore.timeline.AudioTracks[0].AudioTrackClips.forEach((c: TimelineClip, i: number) => {
                allTimelineClips.push({
                    type: 'audio',
                    index: i,
                    name: c.FileName || c.MediaId,
                    start: c.TimelineIn,
                    duration: c.TimelineOut - c.TimelineIn,
                });
            });
        }
        if (videoEditorStore.timeline?.SubtitleTracks?.[0]?.SubtitleTrackClips) {
            videoEditorStore.timeline.SubtitleTracks[0].SubtitleTrackClips.forEach((c: TimelineClip, i: number) => {
                allTimelineClips.push({
                    type: 'subtitle',
                    index: i,
                    name: c.Content || '字幕',
                    start: c.TimelineIn,
                    duration: c.TimelineOut - c.TimelineIn,
                });
            });
        }

        allTimelineClips.forEach(clip => {
            // 排除当前被拖拽的片段自身
            if (clip.type === draggedClipData!.type && clip.index === draggedClipData!.index) {
                return;
            }

            // 将片段的开始和结束时间转换为像素位置，作为吸附点
            const clipStartPx = clip.start * PIXELS_PER_SECOND;
            const clipEndPx = (clip.start + clip.duration) * PIXELS_PER_SECOND;

            snapPoints.push(clipStartPx); // 其他片段的起点
            snapPoints.push(clipEndPx);   // 其他片段的终点
        });

        // 添加时间轴的0点作为吸附点
        snapPoints.push(0);

        // 获取当前拖拽片段的实时边界（基于未吸附的 newVisualLeft）
        const draggedClipLeftPx = newVisualLeft;
        const draggedClipRightPx = newVisualLeft + originalClipDuration * PIXELS_PER_SECOND;

        // 遍历吸附点，检查是否需要吸附
        for (const snapPoint of snapPoints) {
            // 检查拖拽片段的左边界是否接近吸附点
            if (Math.abs(draggedClipLeftPx - snapPoint) < SNAPPING_THRESHOLD_PX) {
                newVisualLeft = snapPoint; // 吸附到吸附点
                break; // 找到一个吸附点就停止
            }
            // 检查拖拽片段的右边界是否接近吸附点
            else if (Math.abs(draggedClipRightPx - snapPoint) < SNAPPING_THRESHOLD_PX) {
                newVisualLeft = snapPoint - (originalClipDuration * PIXELS_PER_SECOND); // 吸附到吸附点
                break;
            }
        }
        // ===============================================================

        currentDraggedClipElement.style.left = `${newVisualLeft}px`;
    }
};

/**
 * @method endClipDrag
 * @description 处理片段的 mouseup 事件，拖拽结束时更新片段的实际数据或执行选中。
 * @param {MouseEvent} event - 鼠标事件对象。
 */
const endClipDrag = (event: MouseEvent) => {
    if (!isDragging.value || !currentDraggedClipElement || !draggedClipData || !scrollAreaRef.value) return;

    // 如果是实际拖拽行为，则更新数据并移除拖拽样式
    if (isActualDragging.value) {
        currentDraggedClipElement.classList.remove('is-dragging');

        // 获取片段的最终视觉left位置 (可能已经包含了吸附后的位置)
        const finalVisualLeftPx = parseFloat(currentDraggedClipElement.style.left || '0');

        // 将像素位置转换回时间（秒）
        let newStartTimeInSeconds = finalVisualLeftPx / PIXELS_PER_SECOND;

        // 确保新的开始时间不小于0 (左侧边界保留)
        newStartTimeInSeconds = Math.max(0, newStartTimeInSeconds);

        // 调用 Pinia Store 的 action 更新时间轴数据
        videoEditorStore.updateClipTime(draggedClipData.type, draggedClipData.index, newStartTimeInSeconds);
    } else {
        // 如果不是实际拖拽，则视为点击，触发片段选中
        selectClip(draggedClipData.type, draggedClipData.index);
    }

    // 重置所有拖拽相关状态
    isDragging.value = false;
    isActualDragging.value = false;
    currentDraggedClipElement = null;
    draggedClipData = null;
    originalClipDuration = 0;

    // 移除window上的事件监听器
    window.removeEventListener('mousemove', handleClipDrag);
    window.removeEventListener('mouseup', endClipDrag);
};


/**
 * @method handleScroll
 * @description 监听时间轴的滚动事件。目前为空，但保留以备将来扩展。
 * 例如，如果需要实现一个迷你地图（minimap）或者同步其他UI元素的滚动，就可以在这里添加逻辑。
 */
const handleScroll = (event: Event) => {
    // 如果需要同步其他东西的滚动，可以在这里做
}

/**
 * @method selectClip
 * @description 当用户点击一个片段时调用。
 * @param {string} type - 片段类型 ('video', 'audio', 'subtitle').
 * @param {number} index - 片段在数组中的索引。
 *
 * 工作原理：
 * 1. 调用 `videoEditorStore.setSelectedClip` 更新 store 中的选中片段信息。
 */
const selectClip = (type: 'video' | 'audio' | 'subtitle', index: number) => {
    // 移除 isDragging.value 的判断，因为现在通过 isActualDragging 来区分点击和拖拽
    videoEditorStore.setSelectedClip({ type, index }); // 调用 store 的 action
}

/**
 * @method isSelected
 * @description 检查一个片段当前是否被选中。
 * @returns {boolean}
 * 这个函数在模板的 `:class` 绑定中使用，用于动态添加 'selected' CSS 类。
 */
const isSelected = (type: 'video' | 'audio' | 'subtitle', index: number) => {
    return videoEditorStore.selectedClip.type === type && videoEditorStore.selectedClip.index === index; // 检查 store 的 selectedClip
}

/**
 * @method getClipStyle
 * @description 为每个片段动态计算其 CSS 样式。
 * @param {object} clip - 经过计算属性处理后的片段对象，包含 `start` 和 `duration`。
 * @returns {{left: string, width: string}} 一个包含 `left` 和 `width` 属性的样式对象。
 *
 * 工作原理：
 * - `left`: 由片段的开始时间 `clip.start` 决定。`left = 开始时间 * 每秒像素`
 * - `width`: 由片段的持续时间 `clip.duration` 决定。`width = 持续时间 * 每秒像素`
 * 这两个属性共同决定了片段在时间轴轨道上的精确位置和长度。
 */
const getClipStyle = (clip: ProcessedClip) => {
    return {
        left: `${clip.start * PIXELS_PER_SECOND}px`,
        width: `${clip.duration * PIXELS_PER_SECOND}px`
    }
}

// =======================================================================
// 辅助函数 (Utility Functions)
// =======================================================================
</script>

<style lang="scss" scoped>
.timeline-component {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #1e1e1e;
    color: #e0e0e0;
    overflow: hidden;
}

.timeline-header {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px 12px;
    background-color: #252526;
    border-bottom: 1px solid #333;

    .controls {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    button {
        background: none;
        border: none;
        color: #ccc;
        font-size: 20px;
        padding: 4px;
        border-radius: 4px;
        cursor: pointer;
        display: inline-flex;
        justify-content: center;
        align-items: center;

        &:hover {
            background-color: #3e3e42;
            color: #fff;
        }
    }

    .time-display {
        font-family: monospace;
        font-size: 12px;
        color: #ccc;
        margin-left: 12px;
    }
}

.timeline-body {
    flex-grow: 1;
    display: flex;
    overflow: hidden;
}

.track-headers {
    flex-shrink: 0;
    width: 120px;
    background-color: #333435;
    border-right: 1px solid #444;
    z-index: 4;

    .header-placeholder {
        height: 30px;
        /* 与标尺高度相同 */
        border-bottom: 1px solid #444;
    }

    .track-header {
        height: 60px;
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 0 10px;
        font-size: 12px;
        font-weight: 500;
        user-select: none;
        border-bottom: 1px solid #444;

        .track-icon {
            font-size: 16px;
        }
    }
}

.timeline-scroll-area {
    flex-grow: 1;
    overflow-x: auto;
    overflow-y: hidden;
    position: relative;

    .ruler {
        position: sticky;
        top: 0;
        height: 30px;
        background-color: #252526;
        z-index: 3;
        display: flex;
        align-items: flex-end;
        width: fit-content;
        border-bottom: 1px solid #333;
        cursor: pointer;

        .ruler-mark {
            flex-shrink: 0;
            width: 120px;
            border-left: 1px solid #444;
            padding-bottom: 4px;
            padding-left: 4px;
            font-size: 10px;
            color: #888;
        }
    }

    .tracks {
        position: relative;
        width: fit-content;
        min-width: 100%;
    }

    .track {
        height: 60px;
        border-bottom: 1px solid #444;
        background-color: #2a2d2e;
        position: relative;
    }

    .clip {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        height: 48px;
        background-color: #4a90e2;
        border-radius: 4px;
        box-sizing: border-box;
        border: 1px solid #1e1e1e;
        border-left-width: 2px;
        border-right-width: 2px;
        display: flex;
        align-items: center;
        padding: 0 10px;
        font-size: 12px;
        color: #fff;
        cursor: grab; // **修改**: 默认鼠标样式为可抓取
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);

        &.selected {
            border: 2px solid #ffc107;
            z-index: 2;
        }

        &.is-dragging { // **新增**: 拖拽时的样式
            z-index: 10; // 确保在最顶层
            cursor: grabbing; // 拖拽时鼠标样式
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
            opacity: 0.9;
        }

        &.audio-clip {
            background-color: #8e44ad;
        }

        &.subtitle-clip {
            background-color: #16a085;
        }
    }

    .playhead {
        position: absolute;
        top: 0;
        bottom: 0;
        width: 2px;
        background-color: #e53935;
        z-index: 5;
        /* 最高层 */
        pointer-events: none;

        .playhead-top {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 8px 6px 0 6px;
            border-color: #e53935 transparent transparent transparent;
        }
    }
}
</style>