<!--
  TimelinePlayer.vue
  ====================
  这是整个视频编辑应用的核心播放引擎。它是一个无UI的后台组件，专门负责视频的加载和播放控制。
  它基于阿里云播放器SDK (Aliplayer) 构建，并设计了两种操作模式：

  1.  **单一播放器模式 (Single Player Mode)**:
      - 当接收到一个独立的 `source` URL prop 时激活。
      - 用于预览单个视频素材，例如在素材库中点击一个视频时。
      - 在这种模式下，它会创建一个标准的、带全功能控制条的 Aliplayer 实例。

  2.  **时间线播放器模式 (Timeline Player Mode)**:
      - 当接收到一个 `timeline` 对象 prop 时激活。
      - 这是编辑页面的主要工作模式。
      - 它会解析 `timeline` 对象中的所有视频片段（clips）。
      - 为 **每一个** 片段创建一个独立的、无控制条的 Aliplayer 实例。
      - 通过一个自定义的主时钟 (`masterTime`) 和 `requestAnimationFrame` 循环，来精确地协调和控制这些播放器实例的播放、暂停、静音和显示/隐藏，从而模拟出一个连续的、多片段的视频流。

  主要职责:
  - 动态加载 Aliplayer 的 JS 脚本。
  - 根据传入的 props (`source` 或 `timeline`) 自动切换工作模式。
  - 创建、管理和销毁 Aliplayer 实例。
  - 在时间线模式下，获取所有视频片段的播放URL。
  - 实现一个自定义的播放循环（tick-tock 机制），用于同步主时钟和各个视频片段的播放状态。
  - 通过 `emits` 将播放器的核心状态（如 isPlaying, currentTime, duration）报告给父组件，驱动 `Timeline.vue` 等UI组件的更新。
-->
<template>
  <div class="timeline-player-wrapper">
    <!--
      播放器容器 (Player Container)
      - `ref="playerContainerRef"`: 获取此 `div` 的 DOM 引用。
      - 所有动态创建的 Aliplayer 实例都会被作为子元素添加到这个容器中。
      - 在时间线模式下，多个播放器实例会通过绝对定位堆叠在这里，通过控制 `visibility` 来决定哪个是当前可见的。
    -->
    <div ref="playerContainerRef" class="player-container">
      <!-- Aliplayer 实例将被动态创建并添加到这里 -->
    </div>
    <!--
      加载遮罩层 (Loading Overlay)
      - `v-if="isLoading"`: 当正在加载脚本或初始化播放器时显示，提供用户反馈。
    -->
    <div v-if="isLoading" class="loading-overlay">
      <p>正在初始化播放器引擎...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onUnmounted, watch } from 'vue';
import { loadScript } from '../../utils/loadScript';
import type { Timeline, TimelineClip } from '../../types/videoEdit';

// Aliplayer SDK 的 CDN 地址。
const ALI_PLAYER_SCRIPT_URL = 'https://g.alicdn.com/de/prismplayer/2.15.2/aliplayer-min.js';

// =======================================================================
// 类型定义 (Type Definitions)
// =======================================================================

/**
 * @type {'single' | 'timeline' | 'idle'} PlayerMode
 * 定义播放器的三种可能状态：
 * - `single`: 单一视频预览模式。
 * - `timeline`: 时间线编辑播放模式。
 * - `idle`: 空闲状态，没有任何播放器实例。
 */
type PlayerMode = 'single' | 'timeline' | 'idle';

/**
 * @interface ProcessedClip
 * @description 这是对从 `timeline` prop 接收到的原始片段（TimelineClip）进行处理和增强后的内部数据结构。
 *
 * @property {string} mediaId - 媒体资源的唯一标识符。
 * @property {number} timelineIn - 该片段在 **整个时间线** 上的开始时间（单位：毫秒）。
 * @property {number} timelineOut - 该片段在 **整个时间线** 上的结束时间（单位：毫秒）。
 * @property {number} internalIn - 该片段需要从 **源视频** 的哪个时间点开始播放（单位：秒）。这对应于阿里云剪辑的 `In` 属性。
 * @property {string} [sourceUrl] - 从服务器获取到的该片段的实际播放URL。
 * @property {any} [player] - 指向与此片段关联的 Aliplayer 实例的引用。
 * @property {string} playerId - 为每个 Aliplayer 实例生成的唯一 DOM `id`。
 */
interface ProcessedClip {
  mediaId: string;
  timelineIn: number;
  timelineOut: number;
  internalIn: number;
  sourceUrl?: string;
  player?: any;
  playerId: string;
}

// =======================================================================
// Emits: 定义组件向父组件发送的事件
// =======================================================================
const emit = defineEmits<{
  (e: 'update:isPlaying', value: boolean): void; // 更新播放状态
  (e: 'update:currentTime', value: number): void; // 更新当前播放时间（秒）
  (e: 'update:duration', value: { formatted: string, seconds: number }): void; // 更新总时长
  (e: 'active-video-element-changed', value: HTMLVideoElement | null): void; // 当活动 <video> 元素改变时触发
}>();

// =======================================================================
// Props: 定义组件从父组件接收的数据
// =======================================================================
const props = defineProps<{
  timeline: Timeline | null; // 时间线数据对象，用于时间线模式
  source?: string | null;     // 单个视频的URL，用于单一播放器模式
}>();

// =======================================================================
// 响应式状态和内部变量 (Reactive State & Internal Variables)
// =======================================================================
const playerContainerRef = ref<HTMLDivElement | null>(null); // 播放器容器的DOM引用
let animationFrameId: number | null = null; // `requestAnimationFrame` 的ID，用于取消动画帧
const isLoading = ref(false); // 是否显示加载状态
const clips = ref<ProcessedClip[]>([]); // 存储所有处理过的片段（时间线模式）
const isPlaying = ref(false); // 内部维护的播放状态
const masterTime = ref(0); // **核心**: 主时钟，以毫秒为单位，驱动整个时间线的播放进度
let lastFrameTimestamp = 0; // 上一帧的时间戳，用于计算时间增量
const totalDuration = ref(0); // 视频总时长（毫秒）
let scriptLoaded = false; // 标记 Aliplayer 脚本是否已加载
let lastActiveClipId: string | null = null; // 记录上一个活动的片段ID，用于优化
let singlePlayer: any = null; // 存放单一播放器模式下的实例
const currentPlayerMode = ref<PlayerMode>('idle'); // 当前播放器模式

// =======================================================================
// 监听器和生命周期钩子 (Watchers & Lifecycle Hooks)
// =======================================================================

/**
 * @watch props.source, props.timeline
 * @description 这是组件的入口和模式切换器。
 * 它会深度监听 `source` 和 `timeline` 这两个 prop 的变化。
 * 当任何一个发生变化时，它会决定进入哪种模式，并调用相应的初始化函数。
 * 优先级：`source` > `timeline`。如果 `source` 有值，则总是进入单一播放器模式。
 */
watch(
  () => ({ source: props.source, timeline: props.timeline }),
  ({ source, timeline }) => {
    // 优先处理 source，如果 source 存在，则总是进入单播放器模式
    if (source) {
      initializeSinglePlayer(source);
    } else if (timeline) {
      initializeTimelinePlayer(timeline);
    } else {
      // 如果两者都为空，则销毁所有播放器
      destroyAllPlayers();
    }
  },
  { deep: true, immediate: true } // `immediate: true` 确保组件加载时立即执行一次
);

/**
 * @hook onUnmounted
 * @description Vue 组件销毁时调用的生命周期钩子。
 * 在这里，我们必须清理所有资源，防止内存泄漏：
 * 1. 取消 `requestAnimationFrame` 循环。
 * 2. 调用 `destroyAllPlayers` 销毁所有 Aliplayer 实例。
 */
onUnmounted(() => {
  if (animationFrameId) cancelAnimationFrame(animationFrameId);
  destroyAllPlayers();
});

/**
 * @method destroyAllPlayers
 * @description 清理函数，负责销毁所有存在的播放器实例和相关资源。
 * 这是一个非常重要的函数，用于在模式切换或组件卸载时重置状态。
 */
function destroyAllPlayers() {
  // 1. 遍历 clips 数组，调用每个播放器实例的 `dispose` 方法
  clips.value.forEach(clip => clip.player?.dispose());
  clips.value = []; // 清空数组

  // 2. 销毁单一播放器实例
  singlePlayer?.dispose();
  singlePlayer = null;

  // 3. 清空播放器容器的 DOM 内容
  if (playerContainerRef.value) {
    playerContainerRef.value.innerHTML = '';
  }

  // 4. 重置状态
  currentPlayerMode.value = 'idle';
  emit('active-video-element-changed', null); // 通知父组件已没有活动的 video 元素
  console.log("所有播放器实例已销毁。");
}

/**
 * @method ensureScriptLoaded
 * @description 确保 Aliplayer 的 JS 脚本只被加载一次。
 * 使用一个 `scriptLoaded` 标志位来防止重复加载。
 */
async function ensureScriptLoaded() {
  if (scriptLoaded) return;
  try {
    isLoading.value = true;
    await loadScript(ALI_PLAYER_SCRIPT_URL);
    scriptLoaded = true;
    console.log("Aliplayer 脚本加载成功。");
  } catch (error) {
    console.error("Aliplayer 脚本加载失败:", error);
  } finally {
    isLoading.value = false;
  }
}

// =======================================================================
// 模式一：单一播放器 (Single Player Mode)
// =======================================================================

/**
 * @method initializeSinglePlayer
 * @description 初始化单一视频预览播放器。
 * @param {string} sourceUrl - 要播放的视频的 URL。
 */
async function initializeSinglePlayer(sourceUrl: string) {
  // 1. 先彻底清理环境，销毁任何可能存在的旧实例
  destroyAllPlayers();
  currentPlayerMode.value = 'single';

  // 2. 确保核心脚本已加载
  await ensureScriptLoaded();
  if (!playerContainerRef.value) return;

  try {
    isLoading.value = true;
    // 3. 创建一个新的 div 作为播放器挂载点
    const playerId = `single-player-${Date.now()}`;
    const playerEl = document.createElement('div');
    playerEl.id = playerId;
    playerContainerRef.value.appendChild(playerEl);

    // 4. 实例化 Aliplayer
    // @ts-ignore
    singlePlayer = new Aliplayer({
      id: playerId,
      source: sourceUrl,
      width: '100%',
      height: '100%',
      autoplay: false,
      isLive: false,
      // 显示标准的、悬浮时出现的控制条
      controlBarVisibility: 'hover',
      useH5Prism: true, // 推荐使用 H5 模式
      // 配置控制条布局
      skinLayout: [
        { name: "bigPlayButton", align: "cc" },
        { name: "H5Loading", align: "cc" },
        {
          name: "controlBar", align: "blabs", x: 0, y: 0,
          children: [
            { name: "progress", align: "blabs", x: 0, y: 44 },
            { name: "playButton", align: "tl", x: 15, y: 12 },
            { name: "timeDisplay", align: "tl", x: 10, y: 7 },
            { name: "fullScreenButton", align: "tr", x: 10, y: 12 },
            { name: "volume", align: "tr", x: 15, y: 10 }
          ]
        }
      ]
    });

    // 辅助函数，用于获取播放器内部的 <video> 元素
    const getPlayerVideoElement = () => playerContainerRef.value?.querySelector('video') ?? null;

    // 5. 绑定事件监听，并将播放器的状态通过 emits 报告给父组件
    singlePlayer.on('play', () => {
      emit('update:isPlaying', true);
      emit('active-video-element-changed', getPlayerVideoElement());
    });
    singlePlayer.on('pause', () => {
      emit('update:isPlaying', false);
      emit('active-video-element-changed', null);
    });
    singlePlayer.on('ended', () => {
      emit('update:isPlaying', false);
      emit('active-video-element-changed', null);
    });
    singlePlayer.on('timeupdate', (e: any) => emit('update:currentTime', e.paramData.currentTime));
    singlePlayer.on('loadedmetadata', () => {
      const duration = singlePlayer.getDuration();
      emit('update:duration', {
        formatted: formatTime(duration),
        seconds: duration
      });
    });
    singlePlayer.on('error', (e: any) => console.error('单一播放器发生错误:', e));

  } catch (error) {
    console.error("初始化单一播放器时发生错误:", error);
  } finally {
    isLoading.value = false;
  }
}

// =======================================================================
// 模式二：时间线播放器 (Timeline Player Mode)
// =======================================================================

/**
 * @method initializeTimelinePlayer
 * @description 初始化时间线播放模式，这是最复杂的部分。
 * @param {Timeline} timeline - 包含所有轨道和片段信息的时间线对象。
 */
async function initializeTimelinePlayer(timeline: Timeline) {
  // 1. 清理环境并设置模式
  destroyAllPlayers();
  currentPlayerMode.value = 'timeline';

  await ensureScriptLoaded();

  try {
    isLoading.value = true;
    // 2. 从 timeline 对象中提取出视频轨道的所有片段
    const rawClips = timeline.VideoTracks?.[0]?.VideoTrackClips || [];
    if (rawClips.length === 0) {
      isLoading.value = false;
      return;
    };

    // 3. 将原始片段数据转换为内部使用的 `ProcessedClip` 格式
    clips.value = rawClips.map((clip: TimelineClip & { FileUrl?: string }, index: number) => ({
      mediaId: clip.MediaId,
      timelineIn: clip.TimelineIn * 1000,   // 转换为毫秒
      timelineOut: clip.TimelineOut * 1000, // 转换为毫秒
      internalIn: clip.In,                  // 保留秒单位，用于 seek
      sourceUrl: clip.FileUrl,              // 直接使用传入的 FileUrl
      playerId: `player-${index}-${Date.now()}` // 生成唯一ID
    }));

    // 4. 计算总时长，并通知父组件
    totalDuration.value = Math.max(...clips.value.map(c => c.timelineOut));
    emit('update:duration', {
      formatted: formatTime(totalDuration.value / 1000),
      seconds: totalDuration.value / 1000
    });

    // 5. 不再需要 loadMediaAssets，直接创建播放器实例
    await createTimelinePlayerInstances();

  } catch (error) {
    console.error("初始化时间线播放器时发生错误:", error);
  } finally {
    isLoading.value = false;
  }
}

/**
 * @method createTimelinePlayerInstances
 * @description 遍历 `clips` 数组，为每个有`sourceUrl`的片段创建一个 Aliplayer 实例。
 */
async function createTimelinePlayerInstances() {
  if (!playerContainerRef.value) return;

  for (const clip of clips.value) {
    if (!clip.sourceUrl) continue;

    // 1. 创建播放器挂载点 div
    const playerEl = document.createElement('div');
    playerEl.id = clip.playerId;
    // 核心样式: 通过绝对定位和 visibility:hidden，让所有播放器堆叠在一起，但默认都不可见。
    playerEl.style.cssText = 'width:100%;height:100%;position:absolute;top:0;left:0;visibility:hidden;';
    playerContainerRef.value.appendChild(playerEl);

    // 2. 实例化 Aliplayer
    // @ts-ignore
    clip.player = new Aliplayer({
      id: clip.playerId,
      source: clip.sourceUrl,
      width: '100%',
      height: '100%',
      autoplay: false, // 禁止自动播放，由我们的主时钟控制
      isLive: false,
      controlBarVisibility: 'never', // **重要**: 隐藏所有默认控制条
      preload: true, // 允许预加载，以加快播放启动速度
      skinLayout: [ // 空的 skinLayout 进一步确保没有UI元素
        { name: "H5Loading", align: "cc" }
      ]
    });
  }
  console.log("所有 Aliplayer 实例创建完毕。");
}

// =======================================================================
// 时间线播放核心控制逻辑 (Timeline Playback Core Logic)
// =======================================================================
// 以下函数共同构成了时间线模式下的自定义播放引擎。

/**
 * @method renderLoop
 * @description 播放循环函数，是时间线播放的"心跳"。
 * @param {DOMHighResTimeStamp} timestamp -由 `requestAnimationFrame` 提供的高精度时间戳。
 *
 * 工作原理：
 * 1. 只有在 `isPlaying.value` 为 true 时，这个循环才会持续执行。
 * 2. `deltaTime = timestamp - lastFrameTimestamp`: 计算自上一帧以来经过的时间（毫秒）。
 * 3. `masterTime.value += deltaTime`: 将经过的时间累加到主时钟上，推动时间前进。
 * 4. `lastFrameTimestamp = timestamp`: 保存当前帧的时间戳，用于下一帧的计算。
 * 5. 当主时钟超过总时长，就暂停播放。
 * 6. 在每一帧，调用 `updatePlayerState` 来更新所有播放器的状态。
 * 7. `requestAnimationFrame(renderLoop)`: 请求浏览器在下一次重绘之前再次调用 `renderLoop`，形成一个高效的、与浏览器刷新率同步的循环。
 */
function renderLoop(timestamp: number) {
  if (!isPlaying.value) return;

  if (lastFrameTimestamp !== 0) {
    const deltaTime = timestamp - lastFrameTimestamp;
    masterTime.value += deltaTime;
  }
  lastFrameTimestamp = timestamp;

  if (masterTime.value >= totalDuration.value) {
    pause(); // 播放到末尾时自动暂停
    masterTime.value = totalDuration.value; // 将时间锁定在总时长，防止超出
  }

  // 根据当前的主时钟时间，更新所有播放器的状态
  updatePlayerState(masterTime.value);
  // 请求下一帧
  animationFrameId = requestAnimationFrame(renderLoop);
}

/**
 * @method updatePlayerState
 * @description 在每个渲染帧或用户 seek 时被调用，是决策中心。
 * @param {number} timeMs - 当前的主时钟时间（毫秒）。
 *
 * 工作原理：
 * 1. `emit('update:currentTime', ...)`: 首先，将当前主时钟（转换为秒）报告给父组件，用于更新UI（如 `Timeline.vue` 的播放头位置）。
 * 2. **找出活动片段**: `clips.value.find(...)` 遍历所有片段，找出当前主时钟 `timeMs` 所在的那个片段。这个片段就是"活动片段"。
 * 3. **管理 video 元素**: 如果活动片段发生了变化，就通过 `emit('active-video-element-changed', ...)` 通知父组件，父组件可能会用它来实现音频可视化等功能。
 * 4. **遍历并控制所有片段**:
 *    - **对于活动片段 (clip === activeClip)**:
 *      a. 将其容器 `visibility` 设置为 `visible`，让它显示出来。
 *      b. 计算 `internalTime`: 这是播放头在 **源视频** 内的时间。公式为 `((主时钟 - 片段在时间轴的开始时间) / 1000) + 片段在源视频的裁切入点`。
 *      c. **关键 seek 逻辑**: 为了确保播放器在正确的时间点，需要调用 `seek()`。但为了避免性能问题，设置了一个阈值 `0.08` 秒。只有当播放器当前时间和我们计算出的 `internalTime` 差距较大时，才执行 seek。
 *      d. 如果当前是播放状态，就调用 `clip.player.play()`。
 *    - **对于非活动片段**:
 *      a. 将其容器 `visibility` 设置为 `hidden`，隐藏起来。
 *      b. 调用 `clip.player.pause()`，确保它们都处于暂停状态，节省资源。
 */
function updatePlayerState(timeMs: number) {
  // 1. 发送当前时间给父组件
  emit('update:currentTime', timeMs / 1000);

  // 2. 找到当前时间点对应的活动片段
  const activeClip = clips.value.find(c => timeMs >= c.timelineIn && timeMs < c.timelineOut);

  // 3. 如果活动片段切换了，通知父组件
  if (activeClip?.playerId !== lastActiveClipId) {
    const newVideoElement = activeClip ? document.querySelector<HTMLVideoElement>(`#${activeClip.playerId} video`) : null;
    emit('active-video-element-changed', newVideoElement);
    lastActiveClipId = activeClip?.playerId || null;
  }

  // 4. 遍历所有片段，更新它们各自的状态
  for (const clip of clips.value) {
    if (!clip.player) continue;

    const playerEl = document.getElementById(clip.playerId);
    if (!playerEl) continue;

    if (clip === activeClip) {
      // 这是活动片段
      playerEl.style.visibility = 'visible';

      // 计算需要播放的源文件内部时间点
      const internalTime = ((timeMs - clip.timelineIn) / 1000) + clip.internalIn;

      // 如果播放器当前时间与目标时间偏差较大，则进行seek
      if (Math.abs(clip.player.getCurrentTime() - internalTime) > 0.08) {
        clip.player.seek(internalTime);
      }

      // 如果是播放状态，确保此片段在播放
      if (isPlaying.value) {
        clip.player.play();
      }
    } else {
      // 这是非活动片段
      playerEl.style.visibility = 'hidden';
      // 确保非活动片段都处于暂停状态
      if (clip.player.getStatus() !== 'paused') {
        clip.player.pause();
      }
    }
  }
}

// =======================================================================
// 公开的控制方法 (Exposed Control Methods)
// =======================================================================
// 这些方法通过 `defineExpose` 暴露给父组件，父组件可以通过 ref 调用它们，
// 就像调用一个普通对象的方法一样。例如 `playerRef.value.play()`。

/**
 * @method play
 * @description 公开的播放方法。
 */
function play() {
  // 在单一播放器模式下，直接调用实例的 play 方法
  if (currentPlayerMode.value === 'single' && singlePlayer) {
    singlePlayer.play();
    return;
  }

  // 在时间线模式下
  if (isPlaying.value || currentPlayerMode.value !== 'timeline') return;
  isPlaying.value = true;
  emit('update:isPlaying', true);

  // 如果播放完毕，从头开始
  if (masterTime.value >= totalDuration.value) {
    masterTime.value = 0;
  }

  lastFrameTimestamp = performance.now(); // 重置上一帧的时间戳，开始新的计时
  animationFrameId = requestAnimationFrame(renderLoop); // 启动渲染循环
}

/**
 * @method pause
 * @description 公开的暂停方法。
 */
function pause() {
  console.log('TimelinePlayer: pause method called'); // <-- 添加此行
  if (currentPlayerMode.value === 'single' && singlePlayer) {
    singlePlayer.pause();
    return;
  }

  if (!isPlaying.value || currentPlayerMode.value !== 'timeline') return;
  isPlaying.value = false;
  emit('update:isPlaying', false);

  // 停止渲染循环
  if (animationFrameId) cancelAnimationFrame(animationFrameId);
  animationFrameId = null;
  lastFrameTimestamp = 0; // 重置时间戳

  // 循环确保所有片段的播放器实例都已暂停
  clips.value.forEach(clip => {
    if (clip.player && clip.player.getStatus() !== 'paused') {
      clip.player.pause();
    }
  });
}

/**
 * @method seek
 * @description 公开的跳转方法。
 * @param {number} timeInSeconds - 要跳转到的目标时间（秒）。
 */
function seek(timeInSeconds: number) {
  if (currentPlayerMode.value === 'single' && singlePlayer) {
    singlePlayer.seek(timeInSeconds);
    // 在单一播放器模式下，如果跳转，也应该暂停
    singlePlayer.pause(); // <--- 添加这行
    return;
  }

  if (currentPlayerMode.value !== 'timeline') return;

  // 将目标时间转换为毫秒并更新主时钟
  const targetTimeMs = Math.max(0, Math.min(timeInSeconds * 1000, totalDuration.value));
  masterTime.value = targetTimeMs;

  // 核心改动：在时间轴跳转后，主动调用组件自身的 pause 方法，
  // 以确保播放器暂停，并停止渲染循环。这符合视频编辑器的常见行为。
  // 即使在跳转前没有播放，调用 pause() 也不会有副作用。
  pause(); // <--- 添加这行

  // 调用 updatePlayerState 来立即将所有播放器实例同步到新的时间点
  // 无论当前是否在播放，seek 操作都需要立即生效
  updatePlayerState(targetTimeMs);
}

// =======================================================================
// 辅助函数 (Utility Functions)
// =======================================================================
function formatTime(seconds: number): string {
  if (isNaN(seconds) || seconds < 0) return '00:00';
  const min = Math.floor(seconds / 60);
  const sec = Math.floor(seconds % 60);
  return `${String(min).padStart(2, '0')}:${String(sec).padStart(2, '0')}`;
}

// =a=====================================================================
// 暴露组件API (Expose Component API)
// =======================================================================
// 使用 `defineExpose` 将内部方法暴露给父组件，
// 这样父组件就可以通过 ref 引用来直接调用这些方法，实现对播放器的控制。
defineExpose({
  play,
  pause,
  seek
});
</script>

<style scoped>
.timeline-player-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #000;
  overflow: hidden;
}

.player-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2em;
}
</style>