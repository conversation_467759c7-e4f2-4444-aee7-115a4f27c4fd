/**
 * 模板数据调试工具 - 清理版本
 * 提供核心测试功能，用于调试模板数据解析、同步和保存流程
 */

import { useVideoEditorStore } from '../useVideoEditor';

/**
 * 测试修复后的解析逻辑并自动更新store
 */
function testFixedParsing() {
  const videoEditorStore = useVideoEditorStore();
  
  console.group('🔧 测试修复后的解析逻辑');
  
  try {
    if (!videoEditorStore.timeline) {
      console.error('❌ Timeline数据不存在');
      return;
    }

    // 创建一个模拟的template对象来测试解析
    const mockTemplate = {
      Config: JSON.stringify(videoEditorStore.timeline)
    };

    // 测试解析
    const materials = videoEditorStore.parseTemplateConfigForMaterials(mockTemplate as any);
    console.log(`✅ 成功解析出 ${materials.length} 个模板素材:`, materials);

    // 自动更新store
    videoEditorStore.setTemplateMaterials(materials);
    console.log('✅ 已自动更新templateMaterials到store');
    
  } catch (error) {
    console.error('❌ 测试解析逻辑时出错:', error);
  }
  
  console.groupEnd();
}

/**
 * 测试同步逻辑问题
 */
function testSyncLogicIssue() {
  const videoEditorStore = useVideoEditorStore();
  console.group('🔍 测试同步逻辑');
  
  const materialsCount = videoEditorStore.templateMaterials.length;
  console.log(`素材数量: ${materialsCount}`);
  
  const needsSync = videoEditorStore.syncMaterialsWithTimeline();
  console.log(`同步检查: ${needsSync ? '❌ 需要同步' : '✅ 同步正常'}`);
  
  console.groupEnd();
}

/**
 * 测试完整的保存流程
 */
function testSaveFlow() {
  const videoEditorStore = useVideoEditorStore();
  console.group('💾 测试保存流程');
  
  try {
    // 1. 数据验证
    const validation = videoEditorStore.validateDataIntegrity();
    console.log('数据验证:', validation);
    
    // 2. 构建请求
    const updateRequest = videoEditorStore.buildUpdateTemplateRequest();
    console.log('保存请求:', {
      hasTemplateId: !!updateRequest.TemplateId,
      hasConfig: !!updateRequest.Config,
      configLength: updateRequest.Config?.length || 0
    });
    
    console.log('✅ 保存流程测试完成');
    
  } catch (error) {
    console.error('❌ 保存流程测试失败:', error);
  }
  
  console.groupEnd();
}

/**
 * 完整测试修复后的系统
 */
function testCompleteFixedSystem() {
  const videoEditorStore = useVideoEditorStore();
  console.group('🎯 完整系统测试');
  
  try {
    console.log('1. 重新解析素材...');
    testFixedParsing();
    
    console.log('2. 测试同步状态...');
    testSyncLogicIssue();
    
    console.log('3. 模拟用户修改素材...');
    if (videoEditorStore.templateMaterials.length > 0) {
      // 修改第一个素材的可替换状态
      videoEditorStore.updateTemplateMaterial(0, 'replace', true);
      videoEditorStore.updateTemplateMaterial(0, 'remark', '用户修改的备注');
      console.log('✅ 已修改第一个素材');
    }
    
    console.log('4. 测试保存流程...');
    testSaveFlow();
    
    console.log('✅ 完整系统测试完成');
    
  } catch (error) {
    console.error('❌ 系统测试失败:', error);
  }
  
  console.groupEnd();
}

// 开发环境自动启用
if (import.meta.env.DEV) {
  // 将所有测试函数绑定到 window 对象，方便在控制台调用
  (window as any).testFixedParsing = testFixedParsing;
  (window as any).testSyncLogicIssue = testSyncLogicIssue;
  (window as any).testSaveFlow = testSaveFlow;
  (window as any).testCompleteFixedSystem = testCompleteFixedSystem;
  
  console.log('🔧 调试工具已加载，可在控制台使用以下函数:');
  console.log('- testFixedParsing() - 测试解析逻辑');
  console.log('- testSyncLogicIssue() - 测试同步逻辑');
  console.log('- testSaveFlow() - 测试保存流程');
  console.log('- testCompleteFixedSystem() - 完整系统测试');
}

export {
  testFixedParsing,
  testSyncLogicIssue,
  testSaveFlow,
  testCompleteFixedSystem
};
