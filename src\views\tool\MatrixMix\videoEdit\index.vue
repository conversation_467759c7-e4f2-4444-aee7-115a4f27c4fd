<script setup lang="ts" name="VideoEdit">
// 从 Vue 框架中导入所需的 Composition API 函数：
// ref: 用于创建响应式引用，常用于包装基本类型数据，使得数据变化能触发视图更新。
// onMounted: 生命周期钩子，在组件挂载到 DOM 后执行，常用于初始化操作、数据请求或事件监听。
// onUnmounted: 生命周期钩子，在组件从 DOM 卸载后执行，常用于清理工作，如移除事件监听器或清除定时器，以防止内存泄漏。
// computed: 用于创建计算属性，其值会根据依赖的响应式数据自动更新，并且具有缓存机制。
// watch: 用于侦听一个或多个响应式数据源（如 ref、reactive 或 computed），并在数据源变化时执行回调函数，适用于执行副作用操作。
import { ref, onMounted, onUnmounted, computed, watch } from 'vue';
// 从 Vue Router 导入路由相关的钩子和函数：
// useRoute: 获取当前路由的信息对象，包含路径、参数、查询参数等。
// useRouter: 获取路由器实例，用于编程式导航，例如 router.push()、router.back()。
import { useRoute, useRouter } from 'vue-router';
// 导入自定义的 Vue 组件，这些组件构成了视频编辑界面的不同部分：
import TimelineComponent from './components/Timeline.vue'; // 时间线组件，用于展示和操作视频片段。
import LeftPanel from './components/LeftPanel.vue'; // 左侧面板组件，可能包含素材库、特效、文本编辑等。
import TimelinePlayer from './components/TimelinePlayer.vue'; // 视频播放器组件，用于预览视频。
import HeaderToolbar from './components/HeaderToolbar.vue'; // 顶部工具栏组件，包含项目标题、返回按钮、保存、导出等操作。
import Levelmeter from './components/Levelmeter.vue'; // 音量电平计组件，用于实时显示音频的音量电平。
import SaveAsTemplateDialog from './components/SaveAsTemplateDialog.vue'; // 另存为模板对话框组件，用于将当前项目保存为可复用的模板。
// 导入 API 调用函数，用于与后端交互，获取项目、媒体、模板数据及触发视频生成：
import { getEditingProject } from '../api/videoEdit';
import { getTemplate } from '../api/template';
import { produceVideo } from '@/api/videoEdit/template';
import { convertTemplateToTimeline, enrichTimelineWithMediaDetails } from '../utils/timeUtils';
import type { VideoEditProject, Timeline } from '../types/videoEdit'; // 视频编辑项目、时间线的类型定义。
import type { TemplateInfo } from '../types/template';
import { ElMessage } from 'element-plus';
import { useVideoEditorStore } from './useVideoEditor';

// 开发环境调试工具
if (import.meta.env.DEV) {
  import('./debug/templateDataDebug').then(({ testCompleteFixedSystem }) => {
    (window as any).debugTemplateData = testCompleteFixedSystem;
    console.log('🔧 调试工具已加载，可在控制台使用: debugTemplateData()');
  });
}

// --- Pinia Store ---
// 通过调用 useVideoEditorStore()，我们可以访问在 useVideoEditor.ts 中定义的响应式状态和方法。这是一个全局状态管理模式
const videoEditorStore = useVideoEditorStore();

// 定义编辑页面来源的类型。用于根据不同的入口（云剪辑项目列表或模板工厂列表）
type EditSource = 'cloud-editing' | 'template-factory';

// 实例化 useRouter 和 useRoute 钩子，以便在组件中访问路由信息。
const route = useRoute(); // route 对象包含了当前路由的路径、参数、查询参数等信息。
const router = useRouter(); // router 实例用于执行导航操作，例如 router.push()、router.back()。

// 调用 router.back() 会使浏览器导航到历史记录中的上一个页面。
const goBack = () => router.back();

// 定义一个临时的 ref 来接收 TimelinePlayer 的实例。

const timelinePlayerRef = ref(null);

// 定义响应式引用，用于存储项目详情、当前活跃视频元素等数据。
// projectDetails: 存储当前编辑项目的详细信息，可能包含项目ID、标题、状态等，可为空。
const projectDetails = ref<Partial<VideoEditProject & TemplateInfo> | null>(null);
// activeVideoForMeter: 存储当前活跃的 HTMLVideoElement 实例，用于音量电平计的输入，可为空。
// Levelmeter 组件会监听这个 ref，以获取实时音频数据并显示音量。
const activeVideoForMeter = ref<HTMLVideoElement | null>(null);

/**
 * @description 记录编辑页的来源，以区分不同的UI和逻辑。
 * 'cloud-editing': 从云剪辑项目列表进入。
 * 'template-factory': 从模板工厂列表进入。
 * 默认为'cloud-editing'。这个变量会影响后续数据加载和UI显示逻辑。
 */
const editSource = ref<EditSource>('cloud-editing');

// timelineObject: 存储经过处理的时间线数据，包含视频片段、音频等信息，类型为 Timeline 或为空。
// const timelineObject = ref<Timeline | null>(null); // 这行将被移除
// videoSource: 存储当前播放的视频URL，可能是项目封面或生成的视频URL，初始化为空字符串。
const videoSource = ref('');
// producedMediaId: 存储已生成的媒体ID，如果项目已有成品，则会赋值，可为空。
const producedMediaId = ref<string | null>(null);

// showSaveAsTemplateDialog: 控制"另存为模板"对话框的显示与隐藏，布尔类型。
const showSaveAsTemplateDialog = ref(false);

// showLeftPanel: 控制左侧面板的显示与隐藏，默认为 true (显示)
const showLeftPanel = ref(true);

// toggleLeftPanel: 切换左侧面板的显示与隐藏状态
const toggleLeftPanel = () => {
  showLeftPanel.value = !showLeftPanel.value;
};

// --- 页面缩放禁用逻辑 ---
// 阻止浏览器默认的缩放行为（Ctrl + 滚轮）。
const handleWheel = (event: WheelEvent) => {
  // 当Ctrl键被按下时，阻止鼠标滚轮的默认行为（缩放）。
  if (event.ctrlKey) {
    event.preventDefault(); // 阻止事件的默认行为。
  }
};

// 阻止浏览器默认的缩放快捷键行为（Ctrl + '+' / '-' / '0'）。
const handleKeyDown = (event: KeyboardEvent) => {
  // 当Ctrl键和+、-、0键被按下时，阻止默认的缩放行为。
  if (event.ctrlKey && (event.key === '+' || event.key === '-' || event.key === '0')) {
    event.preventDefault(); // 阻止事件的默认行为。
  }
};

// 组件挂载生命周期钩子。
// 在组件被挂载到 DOM 后执行的逻辑，通常用于初始化、事件监听等。
onMounted(() => {
  // --- 将 Player 实例链接到 Pinia Store ---
  // 这是关键一步，确保 store 可以控制播放器。
  // watch 侦听 timelinePlayerRef 的变化。
  // immediate: true 确保在组件挂载时立即执行一次回调，即使 timelinePlayerRef 初始就是 null。
  // 当 timelinePlayerRef 被 TimelinePlayer 组件的实例更新后，这个 watch 会触发。
  // videoEditorStore.setPlayerRef(newRef) 将 TimelinePlayer 实例设置到 Pinia Store 中，
  // 使得其他组件或业务逻辑可以通过 store 来调用播放器的方法（如播放、暂停、跳转）。
  watch(timelinePlayerRef, (newRef) => {
    videoEditorStore.setPlayerRef(newRef);
  }, { immediate: true });

  // 根据路由查询参数 'from' 来确定页面来源。
  // route.query 是一个响应式对象，包含了 URL 中的查询参数。
  if (route.query.from === 'template-factory') {
    editSource.value = 'template-factory'; // 如果来源是模板工厂，则更新 editSource。
    // **设置编辑来源到数据中心Store**
    videoEditorStore.setEditSource('template-factory');
  } else {
    // **设置编辑来源到数据中心Store**
    videoEditorStore.setEditSource('cloud-editing');
  }

  // 从路由参数中获取项目或模板ID。
  // route.params 是一个响应式对象，包含了 URL 中的动态路径参数。
  const id = route.params.projectId as string; // ID可以是projectId或templateId，这里统一取名为projectId。
  if (id) {
    // 如果ID存在，则加载项目或模板详情。
    loadProjectOrTemplate(id);
  } else {
    // 如果ID不存在，显示错误消息并返回上一页。
    ElMessage.error('未找到项目或模板ID，无法加载详情。');
    goBack();
  }

  // 组件挂载时，添加事件监听器，用于阻止页面缩放。
  // window.addEventListener('wheel', handleWheel, { passive: false });: 监听鼠标滚轮事件。
  // passive: false 表示事件监听器会阻止事件的默认行为，这对于 preventDefault() 是必需的。
  window.addEventListener('wheel', handleWheel, { passive: false });
  // window.addEventListener('keydown', handleKeyDown);: 监听键盘按下事件。
  window.addEventListener('keydown', handleKeyDown);
});

// 组件卸载生命周期钩子。用于清理事件监听器、定时器等，防止内存泄漏。
onUnmounted(() => {
  // 组件卸载时，移除之前添加的事件监听器，避免影响其他页面或造成内存泄漏。
  window.removeEventListener('wheel', handleWheel);
  window.removeEventListener('keydown', handleKeyDown);
});

/**
 * @description 根据页面来源（editSource），加载云剪辑项目或模板的详情。
 * 这个函数是一个异步函数，因为它需要等待 API 调用的结果。
 * 负责从后端获取项目或模板数据，并进行初步处理。
 * @param id - 项目ID或模板ID
 */
async function loadProjectOrTemplate(id: string) {
  loading.value = true; // 设置加载状态为 true，通常用于显示加载指示器。
  try {
    let rawTimeline: Timeline | null = null; // 用于存储原始的时间线数据，可能来自项目或模板。

    if (editSource.value === 'template-factory') {
      // 场景一：加载模板
      // 调用 API 获取模板详情（包含关联素材信息）
      const response = await getTemplate(id, { relatedMediaidFlag: '1' });
      const template = response.data.Template; // 从响应中提取模板数据。

      // **重要：将完整的模板详情数据缓存到 Store 中，供素材加载时复用**
      videoEditorStore.setTemplateDetailData(template);

      // 适配模板数据到 projectDetails，用于 UI 展示。
      projectDetails.value = {
        ProjectId: template.TemplateId, // 模板ID作为项目的唯一标识。
        Title: template.Name, // 模板名称作为项目标题。
        Status: template.Status, // 模板状态。
      };
      // **新增：将模板ID和名称保存到 Pinia Store**
      videoEditorStore.setEditingTemplateInfo(template.TemplateId, template.Name);

      videoEditorStore.setEditingProjectId('', template.TemplateId);

      if (template.Config) {
        // convertTemplateToTimeline 函数负责解析模板配置（Config），并生成一个 Timeline 对象。
        rawTimeline = convertTemplateToTimeline(template);
        // **新增：解析模板可变素材**
        const parsedMaterials = videoEditorStore.parseTemplateConfigForMaterials(template);
        videoEditorStore.setTemplateMaterials(parsedMaterials);
        console.log('解析到的模板可变素材:', parsedMaterials);
      } else {
        // 如果模板没有有效的配置信息，则发出警告。
        ElMessage.warning("此模板没有有效的配置信息。");
      }
    } else {
      // 场景二：加载云剪辑项目
      // 调用 API 获取云剪辑项目详情。
      const response = await getEditingProject(id);
      projectDetails.value = response.data.Project; // 从响应中提取项目数据。
      const timelineJson = projectDetails.value?.Timeline; // 获取项目的时间线 JSON 字符串。

      // **新增：如果不是模板工厂来源，清空 store 中的模板信息**
      videoEditorStore.setEditingTemplateInfo('', ''); // 清空

      // **设置真正的工程ID到数据中心Store - 云剪辑场景**
      // 必须使用详情接口返回的真实ProjectId，不能用路由参数
      if (!projectDetails.value.ProjectId) {
        console.error('⚠️ 工程详情接口未返回ProjectId，这可能是后端问题');
        ElMessage.error('工程详情接口异常，请联系管理员');
        return;
      }
      videoEditorStore.setEditingProjectId(projectDetails.value.ProjectId, '');
      if (projectDetails.value?.CoverURL) {
        // 如果项目已有生成的封面URL（即成品），则直接播放成品视频。
        videoSource.value = projectDetails.value.CoverURL; // 设置视频源为封面URL。
        producedMediaId.value = projectDetails.value?.ProduceMediaId || null; // 设置已生成的媒体ID。
        console.log('检测到已生成的 CoverURL，将直接播放:', videoSource.value);
        // 如果是直接播放成品，则无需处理时间线，因为时间线是用于编辑的草稿数据。
      } else if (timelineJson) {
        // 如果没有封面URL，但有时间线JSON数据，则解析时间线数据。
        rawTimeline = JSON.parse(timelineJson); // 解析 JSON 字符串为 Timeline 对象。
      }
    }

    // 对转换或解析后的时间线进行"丰富化"处理，获取文件名和URL。
    // enrichTimelineWithMediaDetails 函数会根据时间线中的媒体ID，调用 getMediaInfo API
    // 获取媒体的详细信息（如文件路径、URL等），并将其添加到时间线对象中。
    // 这使得播放器和时间线组件能够正确加载和显示媒体资源。
    if (rawTimeline) {
      videoEditorStore.setTimeline(await enrichTimelineWithMediaDetails(rawTimeline)); // 直接设置到 store
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : "加载详情失败，请检查网络或ID是否正确。";
    console.error("加载项目或模板详情失败:", error);
  } finally {
    loading.value = false; // 无论成功或失败，都将加载状态设置为 false，结束加载指示。
  }
}

// 处理活跃视频元素变化的函数。
// 当 TimelinePlayer 内部的视频元素发生变化时（例如，从一个片段切换到另一个片段的视频），
// 这个函数会被调用，并将当前的 HTMLVideoElement 实例传递给 activeVideoForMeter。
// activeVideoForMeter 会被 Levelmeter 组件用来获取音频数据，从而显示实时音量电平。
function handleActiveVideoChange(videoEl: HTMLVideoElement | null) {
  activeVideoForMeter.value = videoEl;
}

// 处理元素添加到时间线的函数。
// 当左侧面板有元素（如素材、特效）被拖拽或点击添加到时间轴时，会触发此函数。
// TODO:目前只是简单地打印日志，未来会在此处添加更新时间线数据的实际逻辑。
const handleAddElement = (element: any) => {
  console.log("Element added to timeline:", element);
};

// 定义响应式引用 loading，用于控制加载状态。
// 当数据加载或视频生成等耗时操作进行时，可以设置为 true，用于显示加载动画。
const loading = ref(false);

// 处理视频生成（Produce）操作的函数。
// 这是一个异步函数，用于触发后端生成最终视频文件。
const handleProduce = async () => {
  // 检查项目ID是否存在，如果不存在则无法生成视频。
  if (!projectDetails.value?.ProjectId) {
    ElMessage.error('项目ID不存在，无法生成视频。');
    return;
  }

  loading.value = true; // 设置加载状态为 true，显示加载动画。
  try {
    // 调用 produceVideo API，向后端发送视频生成请求。
    const res = await produceVideo(projectDetails.value.ProjectId, {});
    console.log("Production started:", res); // 打印生成请求的响应。
    ElMessage.success('视频生成请求已发送，请稍后查看状态。'); // 提示用户请求已发送。
  } catch (error) {
    // 捕获生成过程中可能发生的错误。
    console.error("Production failed:", error); // 打印错误信息。
    ElMessage.error('视频生成失败。'); // 向用户显示错误消息。
  } finally {
    loading.value = false; // 无论成功或失败，都将加载状态设置为 false，结束加载指示。
  }
};

// 处理"另存为模板"操作的函数。
// 触发此函数时，会显示一个对话框，允许用户将当前编辑的时间线保存为一个新的模板。
const handleSaveAsTemplate = () => {
  // 检查时间线数据是否存在，如果没有时间线数据，则无法生成模板。
  if (!videoEditorStore.timeline) { // 修改：直接检查 store 的 timeline
    ElMessage.warning("没有可用于生成模板的时间线数据。");
    return;
  }
  // 显示"另存为模板"对话框。
  showSaveAsTemplateDialog.value = true;
};

// 处理保存操作的函数。
// TODO: 目前只是一个占位符，需要在此处添加调用 API 保存项目的实际逻辑。
const handleSave = () => {
  console.log("触发保存操作");
  ElMessage.info("保存功能待实现");
};

// 处理导出命令的函数。
// 这个函数根据传入的命令字符串，执行不同的导出操作，例如导出片段、合成导出或导出完整视频。
const handleExportCommand = (command: string) => {
  switch (command) {
    case 'export-clips':
      // TODO: 添加各片段独立导出的逻辑。
      console.log("触发各片段独立导出");
      ElMessage.info("各片段独立导出功能待实现");
      break;
    case 'export-composition':
      // TODO: 添加片段合成导出的逻辑。
      console.log("触发片段合成导出");
      ElMessage.info("片段合成导出功能待实现");
      break;
    case 'export-video':
      // TODO: 添加导出完整视频的逻辑。
      console.log("触发导出视频");
      ElMessage.info("导出视频功能待实现");
      break;
  }
};

</script>

<template>
  <div class="vid-edit-wrapper">
    <!-- 顶部工具栏组件：`HeaderToolbar` 用于显示项目标题、提供返回功能以及保存、导出等操作。 -->
    <!-- `:title` prop 绑定项目标题，如果 `projectDetails.Title` 为空，则显示默认文本 "智能媒体生产"。 -->
    <!-- `:edit-source` prop 传递编辑来源（'cloud-editing' 或 'template-factory'），用于工具栏内部根据来源调整显示内容或功能。 -->
    <!-- `@go-back` 事件监听：当点击返回按钮时，触发父组件的 `goBack` 方法，实现页面导航。 -->
    <!-- `@save-as-template` 事件监听：当点击"另存为模板"按钮时，触发 `handleSaveAsTemplate` 方法，打开模板保存对话框。 -->
    <!-- `@save` 事件监听：当点击"保存"按钮时，触发 `handleSave` 方法，执行项目保存逻辑。 -->
    <!-- `@export` 事件监听：当点击导出相关菜单项时，触发 `handleExportCommand` 方法，根据传入的命令执行不同的导出操作。 -->
    <!-- `@save-template` 事件监听：当点击"保存模板"按钮时，触发 `videoEditorStore.handleSaveTemplate` 方法，执行保存模板逻辑。 -->
    <HeaderToolbar :title="projectDetails?.Title || '智能媒体生产'" :edit-source="editSource" @go-back="goBack"
      @save-as-template="handleSaveAsTemplate" @save="handleSave" @export="handleExportCommand" />

    <!-- 页面主要内容区域包裹器：`vid-edit-view-wrap` 包含除顶部工具栏和底部时间轴之外的所有主要内容。 -->
    <div class="vid-edit-view-wrap">
      <!-- 页面中部内容行：`vid-edit-top-content-row` 包含左侧面板、中心播放器区域和右侧面板。 -->
      <div class="vid-edit-top-content-row">
          <!-- 左侧面板组件：`LeftPanel` 用于展示素材库、内容编辑区、模板变量等。 -->
          <div :class="['left-panel-container', { 'is-collapsed': !showLeftPanel }]">
            <LeftPanel @add-element="handleAddElement" :materials="videoEditorStore.templateMaterials"
              :edit-source="editSource" :is-collapsed="!showLeftPanel" @toggle-collapse="toggleLeftPanel" />
          </div>
          <!-- 中心面板：`vid-edit-center-panel` 包含视频播放器和播放控制条。 -->
          <div class="vid-edit-center-panel">
            <!-- 视频播放器区域的包裹器：`vid-edit-video-player-wrapper` 用于承载 `TimelinePlayer` 组件。 -->
            <!-- 采用 Flex 布局使其内容居中对齐，并占据可用空间，设置背景颜色、圆角和最小高度。 -->
            <div class="vid-edit-video-player-wrapper">
              <!-- 核心播放器组件：`TimelinePlayer` 负责视频的加载、播放、暂停、跳转等纯逻辑操作，不包含任何可见的UI。 -->
              <!-- `ref="timelinePlayerRef"` 将组件实例的引用绑定到 `timelinePlayerRef` 这个 Vue ref 上。 -->
              <!-- 通过这个 ref，父组件 `index.vue` 及其 Pinia Store 可以调用 `TimelinePlayer` 内部暴露的方法（如 `play()`、`pause()`、`seek()`）。 -->
              <!-- `:source` prop 绑定单一视频播放模式下的视频源 URL。 -->
              <!-- `:timeline` prop 绑定时间线对象，用于时间线模式下的多片段播放。 -->
              <!-- `@update:is-playing` 事件监听：当播放状态变化时（播放/暂停），触发 `videoEditorStore.updateIsPlaying` 更新 Pinia Store 中的 `isPlaying` 状态。 -->
              <!-- `@update:current-time` 事件监听：当当前播放时间变化时，触发 `videoEditorStore.updateCurrentTime` 更新 Pinia Store 中的 `currentTime` 状态。 -->
              <!-- `@update:duration` 事件监听：当视频总时长变化时，触发 `videoEditorStore.updateDuration` 更新 Pinia Store 中的 `videoDurationSeconds` 状态。 -->
              <!-- `@active-video-element-changed` 事件监听：当活动视频元素变化时（例如切换播放片段），触发 `handleActiveVideoChange` 函数， -->
              <!-- 用于将当前播放的 `<video>` 元素传递给音频电平表组件，以便其能够分析音频流。 -->
              <TimelinePlayer ref="timelinePlayerRef" :source="videoSource" :timeline="videoEditorStore.timeline"
                @update:is-playing="videoEditorStore.updateIsPlaying"
                @update:current-time="videoEditorStore.updateCurrentTime"
                @update:duration="videoEditorStore.updateDuration"
                @active-video-element-changed="handleActiveVideoChange" />
            </div>
            <!-- 播放器下方的控制条区域：`vid-edit-player-controls` 显示时间信息和操作按钮。 -->
            <div class="vid-edit-player-controls">
              <!-- 显示当前播放时间 / 视频总时长：格式化后的时间从 Pinia Store 的 computed 属性 `currentTimeFormatted` 和 `videoDurationFormatted` 获取。 -->
              <span>{{ videoEditorStore.currentTimeFormatted }} / {{ videoEditorStore.videoDurationFormatted }}</span>
              <!-- 控制按钮区域：`vid-edit-control-buttons` 包含视频生成等操作按钮。 -->
              <!-- `el-button` 是 Element Plus 的按钮组件。 -->
              <!-- `type="primary"` 设置按钮样式为主要按钮。 -->
              <!-- `@click="handleProduce"` 绑定点击事件，触发 `handleProduce` 函数开始视频生成。 -->
              <!-- `:loading="loading"` 根据 `loading` 响应式变量的状态显示加载动画，在生成过程中禁用按钮以防止重复提交。 -->
              <el-button type="primary" @click="handleProduce" :loading="loading">生成视频</el-button>
            </div>
          </div>

          <!-- 右侧面板：`vid-edit-right-panel` 目前包含音频电平表，未来可以扩展其他功能，如属性面板、效果设置等。 -->
          <!-- 采用 Flex 布局使其内容垂直排列，设置固定宽度，并阻止收缩（`flex-shrink: 0;`）。 -->
          <div class="vid-edit-right-panel">
            <!-- 音频电平表组件：`Levelmeter` 用于实时显示音频的音量电平。 -->
            <!-- `:video-element` prop 绑定当前活动的 `<video>` 元素，电平表通过它来分析音频数据。 -->
            <!-- `:is-playing` prop 绑定视频的播放状态（由 Pinia Store 的 `isVideoPlaying` 提供），电平表根据此状态决定是否显示实时数据。 -->
          </div>
          <Levelmeter :video-element="activeVideoForMeter" :is-playing="videoEditorStore.isVideoPlaying" />
        </div>

      <!-- 页面底部的时间轴行：`vid-edit-bottom-timeline-row` 包含 `TimelineComponent`。 -->
      <!-- 设置固定高度，阻止收缩，设置背景颜色和溢出处理。 -->
      <div class="vid-edit-bottom-timeline-row">
        <!-- 时间轴组件：`TimelineComponent` 用于显示视频轨道、音频轨道、片段、播放头等，并处理时间轴的交互。 -->
        <!-- `:timeline` prop 绑定经过 `enrichTimelineWithMediaDetails` 处理后的时间线对象。 -->
        <!-- `timelineObject || {}` 提供一个空对象作为备用，防止 `timelineObject` 为 null 时导致报错。 -->
        <TimelineComponent :timeline="videoEditorStore.timeline" />
      </div>
    </div>

    <!-- "另存为模板"对话框组件：`SaveAsTemplateDialog` 用于将当前编辑项目保存为可复用的模板。 -->
    <!-- `v-model:visible="showSaveAsTemplateDialog"` 实现双向绑定，控制对话框的显示/隐藏状态。 -->
    <!-- `:timeline` prop 绑定当前的时间线数据，对话框内部会使用这些数据来生成模板配置。 -->
    <!-- `:preview-media-id` prop 绑定已生成的媒体 ID，用于模板的预览功能。 -->
    <SaveAsTemplateDialog v-model:visible="showSaveAsTemplateDialog" :timeline="videoEditorStore.timeline"
      :preview-media-id="producedMediaId" />

    <!-- 全局加载遮罩：当 `loading` 变量为 `true` 时显示，用于阻止用户操作并提供视觉反馈。 -->
    <!-- `v-loading="loading"`：Element Plus 的指令，根据 `loading` 的布尔值显示或隐藏加载动画和遮罩。 -->
    <!-- `element-loading-text="正在加载..."`：加载时显示的文本。 -->
    <!-- `element-loading-background="rgba(0, 0, 0, 0.7)"`：遮罩的背景颜色和透明度。 -->
    <div v-loading="loading" element-loading-text="正在加载..." element-loading-background="rgba(0, 0, 0, 0.7)"
      class="loading-overlay"></div>
  </div>
</template>

<style lang="scss" scoped>
/* 视频编辑页面的根容器样式：`vid-edit-wrapper` 定义了整个页面的基本布局和外观。 */
.vid-edit-wrapper {
  background-color: #1a1e22;
  color: #e0e0e0;
  display: flex;
  flex-direction: column;
  /* 子元素垂直排列。 */
  height: 100vh;
  overflow: hidden;
  /* 防止整个页面溢出 */
}

/* 主要内容区域包裹器样式：`vid-edit-view-wrap` 包含除了顶部工具栏和底部"另存为模板"对话框之外的所有主要内容。 */
.vid-edit-view-wrap {
  display: flex;
  flex-direction: column;
  /* 子元素垂直排列。 */
  flex: 1;
  /* 占据父容器的剩余可用空间，使其可伸缩。 */
  padding: 10px;
  overflow: hidden;
  /* 隐藏超出容器的内容，防止布局溢出。 */
  min-height: 0;
  /* 允许子元素正确收缩 */
  height: 50vh;
  /* 设置确定的高度，为顶部工具栏预留空间 */
}

/* 页面顶部内容行样式：`vid-edit-top-content-row` 包含左侧面板、中心播放器区域和右侧面板。 */
.vid-edit-top-content-row {
  display: flex;
  flex: 1;
  /* 占据父容器的剩余可用空间。 */
  min-height: 0;
  /* 允许子元素正确收缩 */
  overflow: hidden;
  /* 隐藏超出容器的内容。 */
}

.left-panel-container {
  transition: width 0.3s ease;
  /* 添加过渡动画 */
  height: 50vh;

  &.is-collapsed {
    width: 80px;
    /* 折叠后的宽度，与 LeftPanel 内部的折叠宽度一致 */
  }
}

/* 中心面板样式：`vid-edit-center-panel` 包含视频播放器和播放控制条。 */
.vid-edit-center-panel {
  flex-grow: 1;
  /* 占据父容器的剩余可用空间。 */
  display: flex;
  /* 启用 Flex 布局。 */
  flex-direction: column;
  /* 子元素垂直排列。 */
  min-width: 0;
  /* 确保在 Flex 容器中内容可以正确收缩。 */

  /* 视频播放器区域包裹器样式：`vid-edit-video-player-wrapper` 用于承载 `TimelinePlayer` 组件。 */
  .vid-edit-video-player-wrapper {
    flex-grow: 1;
    background-color: #1a1e22;
    border-radius: 8px;
    display: flex;
    align-items: center;
    /* 子元素垂直居中对齐。 */
    justify-content: center;
    /* 子元素水平居中对齐。 */
    min-height: 0;
  }

  /* 播放器控制条样式：`vid-edit-player-controls` 位于播放器下方，包含时间显示和操作按钮。 */
  .vid-edit-player-controls {
    flex-shrink: 0;
    /* 不收缩，保持固定大小。 */
    background-color: #24292e;
    padding: 8px 16px;
    display: flex;
    align-items: center;
    /* 子元素垂直居中对齐。 */
    justify-content: space-between;
    /* 子元素两端对齐，中间留白。 */
    color: #a8b2c2;
  }
}

/* 右侧面板样式：`vid-edit-right-panel` 目前为空，但定义了其基本布局。 */
.vid-edit-right-panel {
  flex-shrink: 0;
  width: 150px;
  display: flex;
  flex-direction: column;
}

/* 页面底部时间轴行样式：`vid-edit-bottom-timeline-row` 包含 `TimelineComponent`。 */
.vid-edit-bottom-timeline-row {
  height: 40vh;
  flex-shrink: 0;
  /* 不收缩，保持固定大小。 */
  background-color: #24292e;
}
</style>