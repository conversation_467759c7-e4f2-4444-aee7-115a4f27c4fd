import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { Timeline, SelectedClip, TimelineClip } from '../types/videoEdit'; // 引入新类型
import type { Material, TemplateInfo } from '../types/template'; // 引入 Material 和 TemplateInfo 类型
import { ElMessage } from 'element-plus'; // **确保导入 ElMessage，如果之前没有的话**
import { updateTemplate } from '../api/template'; // 导入 updateTemplate API
import type { UpdateTemplateRequest } from '../types/template';

// **新增**: 导入时间轴操作工具类
import {
  cutClipAtTime,
  deleteClipAtIndex,
  updateClipTimeAtIndex,
  deepCopyTimeline,
  type TrackType,
  type TimelineOperationResult
} from '../utils/timelineUtils';

// **新增**: 导入模板管理工具类
import {
  parseTemplateConfigForMaterials,
  convertTimelineToTemplateConfig,
  buildClipsParamFromMaterials,
  syncMaterialsWithTimeline,
  buildUpdateTemplateRequest,
  validateDataIntegrity,
  syncSingleMaterialToTimeline,
  refreshMaterialsFromTimeline
} from '../utils/templateUtils';

// **新增**: 导入播放器控制工具类
import {
  createPlayerController,
  createPlayerInteractionController,
  PlayerStateManager,
  type TimelinePlayerInstance
} from '../utils/playerControlUtils';

// **新增**: 导入通用选择器工具类
import { selectBySource, selectId, createSelector, type EditSource } from '../utils/editSourceSelector';

// **新增**: 导入工程素材加载器
import { loadProjectMaterials as loadProjectMaterialsUtil } from '../utils/projectMaterialsLoader';

// **新增**: 导入通用工具函数
import { deepClone } from '@/utils/index';

/**
 * @name useVideoEditorStore
 * @description 视频编辑器的 Pinia Store，管理播放器状态、时间轴数据和模板素材。
 */
export const useVideoEditorStore = defineStore('videoEditor', () => {
  // =======================================================================
  // State - 核心响应式状态
  // =======================================================================

  const timelinePlayerRef = ref<TimelinePlayerInstance | null>(null);
  const isVideoPlaying = ref(false);
  const currentTime = ref(0);
  const lastCurrentTime = ref(0);
  const videoDurationSeconds = ref(0);
  const timeline = ref<Timeline | null>(null);
  const selectedClip = ref<SelectedClip>({ type: null, index: null });
  const templateMaterials = ref<Material[]>([]);
  const editingTemplateId = ref<string>('');
  const editingTemplateName = ref<string>('');
  const templateDetailData = ref<any>(null); // 存储完整的模板详情数据

  // **新增**: 编辑来源状态
  const editSource = ref<EditSource>('cloud-editing'); // 默认为云剪辑模式

  // **新增**: 工程管理相关状态
  const editingProjectId = ref<string>(''); // 当前编辑的工程ID（云剪辑或模板工厂）
  const projectMaterials = ref<any[]>([]); // 工程关联的媒资素材
  const projectMaterialsLoading = ref<boolean>(false); // 工程素材加载状态

  // 播放器控制器实例
  const playerController = createPlayerController();
  const playerInteractionController = createPlayerInteractionController(playerController);

  // =======================================================================
  // Getters - 计算属性
  // =======================================================================

  const videoDurationFormatted = computed(() =>
    PlayerStateManager.formatVideoDuration(videoDurationSeconds.value)
  );

  const currentTimeFormatted = computed(() =>
    PlayerStateManager.formatCurrentTime(
      currentTime.value,
      lastCurrentTime.value,
      isVideoPlaying.value
    )
  );

  // =======================================================================
  // Actions - 方法定义
  // =======================================================================

  /**
   * @action handlePause
   * @description 暴露给外部的暂停方法。用于在时间轴交互时暂停视频。
   */
  function handlePause() {
    _pause();
  }

  /**
   * @action setPlayerRef
   * @description 设置 TimelinePlayer 组件实例的引用。
   * @param {TimelinePlayerInstance | null} player - TimelinePlayer 组件实例。
   */
  function setPlayerRef(player: TimelinePlayerInstance | null) {
    timelinePlayerRef.value = player;
  }

  // --- 内部控制方法 ---
  /**
   * @internal _play - 内部播放方法
   */
  function _play() {
    lastCurrentTime.value = currentTime.value;
    timelinePlayerRef.value?.play();
  }

  /**
   * @internal _pause - 内部暂停方法
   */
  function _pause() {
    timelinePlayerRef.value?.pause();
  }

  // --- 公开的控制方法 ---
  /**
   * @action togglePlayPause - 切换播放/暂停状态
   */
  function togglePlayPause() {
    if (isVideoPlaying.value) {
      _pause();
    } else {
      _play();
    }
  }

  /**
   * @action handleReset - 重置到视频开头
   */
  function handleReset() {
    timelinePlayerRef.value?.seek(0);
  }

  /**
   * @action handleSeek - 跳转到指定时间
   * @param {number} time - 跳转时间（秒）
   */
  function handleSeek(time: number) {
    timelinePlayerRef.value?.seek(time);
  }

  // --- 状态更新器 ---

  /**
   * @action updateIsPlaying - 更新播放状态
   */
  function updateIsPlaying(value: boolean) {
    isVideoPlaying.value = value;
  }

  /**
   * @action updateCurrentTime - 更新当前播放时间
   */
  function updateCurrentTime(value: number) {
    currentTime.value = value;
  }

  /**
   * @action updateDuration - 更新视频总时长
   */
  function updateDuration(data: { seconds: number }) {
    videoDurationSeconds.value = data.seconds;
  }

  /**
  * @action setTimeline - 设置时间轴数据
  */
  function setTimeline(data: Timeline | null) {
    timeline.value = data;
  }

  /**
  * @action setSelectedClip - 设置选中的片段
  */
  function setSelectedClip(payload: SelectedClip) {
    if (selectedClip.value.type === payload.type && selectedClip.value.index === payload.index) {
      selectedClip.value = { type: null, index: null };
    } else {
      selectedClip.value = payload;
    }
  }

  /**
  * @action handleCut - 处理视频片段切割
  */
  function handleCut() {
    if (!timeline.value || selectedClip.value.type === null || selectedClip.value.index === null) {
      console.warn('无法切割：时间轴数据未加载或未选中任何片段。');
      return;
    }

    if (isVideoPlaying.value) {
      _pause();
    }

    const { type, index } = selectedClip.value;
    const cutTime = currentTime.value;

    const result = cutClipAtTime(timeline.value, type as TrackType, index, cutTime);

    if (result.success) {
      timeline.value = result.timeline!;
      setSelectedClip({ type: null, index: null });

      // **新增**: 同步模板素材数据
      try {
        const refreshedMaterials = refreshMaterialsFromTimeline(timeline.value);
        templateMaterials.value = refreshedMaterials;
        console.log('✂️ 切割操作完成，已同步模板素材数据');
      } catch (error) {
        console.error('同步模板素材失败:', error);
        // 同步失败不影响主要功能，仅记录错误
      }
    } else {
      console.warn(`切割失败：${result.error || '未知错误'}`);
    }
  }

  /**
   * @action handleDeleteClip - 处理视频片段删除
   */
  function handleDeleteClip() {
    if (!timeline.value || selectedClip.value.type === null || selectedClip.value.index === null) {
      console.warn('无法删除：时间轴数据未加载或未选中任何片段。');
      return;
    }

    if (isVideoPlaying.value) {
      _pause();
    }

    const { type, index } = selectedClip.value;
    const result = deleteClipAtIndex(timeline.value, type as TrackType, index);

    if (result.success) {
      timeline.value = result.timeline!;
      setSelectedClip({ type: null, index: null });

      // **新增**: 同步模板素材数据
      try {
        const refreshedMaterials = refreshMaterialsFromTimeline(timeline.value);
        templateMaterials.value = refreshedMaterials;
        console.log('🗑️ 删除操作完成，已同步模板素材数据');
      } catch (error) {
        console.error('同步模板素材失败:', error);
        // 同步失败不影响主要功能，仅记录错误
      }
    } else {
      console.warn(`删除失败：${result.error || '未知错误'}`);
    }
  }

  /**
   * @action updateClipTime - 更新片段时间
   */
  function updateClipTime(type: 'video' | 'audio' | 'subtitle', index: number, newStartTime: number) {
    if (!timeline.value) {
      console.warn('无法更新片段时间：时间轴数据未加载。');
      return;
    }

    const result = updateClipTimeAtIndex(timeline.value, type as TrackType, index, newStartTime);

    if (result.success) {
      timeline.value = result.timeline!;
      setSelectedClip({ type: null, index: null });
    } else {
      console.warn(`更新片段时间失败：${result.error || '未知错误'}`);
    }
  }

  // --- 模板相关方法 ---

  /**
   * @action convertTimelineToTemplateConfig - 转换时间轴为模板配置
   */
  function convertTimelineToTemplateConfigAction(): string {
    if (!timeline.value) {
      throw new Error('时间线数据不存在，无法转换为模板配置');
    }
    return convertTimelineToTemplateConfig(timeline.value, templateMaterials.value);
  }

  /**
   * @action buildClipsParamFromMaterials - 构建ClipsParam对象
   */
  function buildClipsParamFromMaterialsAction(): string {
    if (!timeline.value) {
      throw new Error('时间线数据不存在，无法构建ClipsParam');
    }
    return buildClipsParamFromMaterials(templateMaterials.value, timeline.value);
  }

  /**
   * @action buildUpdateTemplateRequest - 构建更新模板请求
   */
  function buildUpdateTemplateRequestAction(): UpdateTemplateRequest {
    if (!timeline.value) {
      throw new Error('时间线数据不存在，无法构建更新请求');
    }
    return buildUpdateTemplateRequest(
      editingTemplateId.value,
      editingTemplateName.value,
      timeline.value,
      templateMaterials.value
    );
  }

  /**
   * @action handleSaveTemplate - 保存模板
   */
  async function handleSaveTemplate() {
    try {
      // 数据完整性验证
      const validationResult = validateDataIntegrityAction();
      if (validationResult.issues.length > 0) {
        console.warn('数据完整性验证发现问题:', validationResult.issues);
        if (validationResult.issues.includes('时间线数据不存在') ||
          validationResult.issues.includes('编辑模板ID不存在')) {
          ElMessage.error('关键数据缺失，无法保存模板');
          return;
        }
      }

      if (!editingTemplateId.value) {
        ElMessage.error('模板ID不存在，无法保存模板');
        return;
      }

      if (!timeline.value) {
        ElMessage.error('时间线数据不存在，无法保存模板');
        return;
      }

      if (templateMaterials.value.length === 0) {
        ElMessage.warning('没有可变素材数据，将保存当前时间线为固定模板');
      }

      // 数据同步检查
      if (syncMaterialsWithTimelineAction()) {
        ElMessage.warning('检测到数据不同步，已自动调整');
      }

      const updateRequest = buildUpdateTemplateRequestAction();

      const response = await updateTemplate(updateRequest);
      ElMessage.success('模板保存成功！');

    } catch (error) {
      console.error('保存模板失败:', error);
      const errorMessage = error instanceof Error
        ? `保存模板失败: ${error.message}`
        : '保存模板失败，请稍后重试';
      ElMessage.error(errorMessage);
    }
  }

  /**
   * @action updateTemplateMaterial - 更新模板素材属性
   */
  function updateTemplateMaterial(index: number, key: keyof Material, value: any) {
    if (!templateMaterials.value || templateMaterials.value.length <= index) {
      console.warn(`无法更新模板素材：索引 ${index} 超出范围。`);
      return;
    }

    // 使用深拷贝更新 templateMaterials，避免直接修改原始数据
    const newMaterials = deepClone(templateMaterials.value) as Material[];
    const oldMaterial = newMaterials[index];
    newMaterials[index] = { ...oldMaterial, [key]: value };

    // 特殊处理：当更新 replace 状态时，同时更新 action 字段
    if (key === 'replace') {
      newMaterials[index].action = value ? '高级设置' : '';
      console.log(`🔄 同时更新素材 ${index} 的 action 字段为: "${newMaterials[index].action}"`);
    }

    templateMaterials.value = newMaterials;

    // **新增**：同步到 timeline 数据
    if (timeline.value) {
      const updatedMaterial = newMaterials[index];
      const success = syncSingleMaterialToTimeline(timeline.value, updatedMaterial);

      if (success) {
        console.log(`✅ 已同步素材 "${updatedMaterial.name}" 的 ${String(key)} 更改到时间轴`);

        // 根据不同的更新类型给出用户提示
        if (key === 'replace') {
          ElMessage.success(`素材 "${updatedMaterial.name}" 的可替换状态已更新为: ${value ? '可替换' : '不可替换'}`);
        } else if (key === 'remark') {
          ElMessage.success(`素材 "${updatedMaterial.name}" 的备注已更新`);
        } else if (key === 'advancedSettings') {
          ElMessage.success(`素材 "${updatedMaterial.name}" 的高级设置已更新`);
        }
      } else {
        console.warn(`⚠️ 未能在时间轴中找到素材 ${updatedMaterial.idNum} 对应的片段`);

        // 给用户友好的提示
        if (key === 'replace' || key === 'remark' || key === 'advancedSettings') {
          ElMessage.warning(`素材 "${updatedMaterial.name}" 的设置已保存到前端，保存模板时将生效`);
        }
      }
    } else {
      console.warn('时间轴数据不存在，素材修改仅保存在前端');

      // 即使没有时间轴数据，也要给用户反馈
      if (key === 'replace') {
        ElMessage.info(`素材 "${oldMaterial.name}" 的可替换状态已更新，保存模板时将生效`);
      }
    }
  }

  /**
   * @action setTemplateMaterials - 设置模板素材数据
   */
  function setTemplateMaterials(materials: Material[]) {
    templateMaterials.value = materials;
  }

  /**
   * @action setEditingTemplateInfo - 设置编辑模板信息
   */
  function setEditingTemplateInfo(templateId: string, templateName: string) {
    editingTemplateId.value = templateId;
    editingTemplateName.value = templateName;
  }

  /**
   * @action setTemplateDetailData - 设置完整的模板详情数据
   */
  function setTemplateDetailData(templateData: any) {
    templateDetailData.value = templateData;
    console.log('📦 已缓存模板详情数据，避免重复API调用');
  }

  /**
   * @action getTemplateDetailData - 获取缓存的模板详情数据
   */
  function getTemplateDetailData() {
    return templateDetailData.value;
  }

  /**
   * @action parseTemplateConfigForMaterials - 解析模板配置中的可变素材
   */
  function parseTemplateConfigForMaterialsAction(template: TemplateInfo): Material[] {
    try {
      return parseTemplateConfigForMaterials(template);
    } catch (error) {
      console.error('解析模板配置失败:', error);
      ElMessage.error('解析模板配置失败');
      return [];
    }
  }

  /**
   * @action syncMaterialsWithTimeline - 同步模板素材与时间线数据
   */
  function syncMaterialsWithTimelineAction(): boolean {
    if (!timeline.value) {
      console.warn('时间线数据不存在，无法同步');
      return false;
    }
    return syncMaterialsWithTimeline(templateMaterials.value, timeline.value);
  }

  /**
   * @action validateDataIntegrity - 验证数据完整性
   */
  function validateDataIntegrityAction() {
    return validateDataIntegrity(
      timeline.value,
      templateMaterials.value,
      editingTemplateId.value,
      editingTemplateName.value
    );
  }

  // --- 工程相关方法 ---
  /**
   * @action setEditSource - 设置编辑来源
   */
  function setEditSource(source: EditSource) {
    editSource.value = source;
    console.log('📝 设置编辑来源:', source);
  }

  /**
   * @action setEditingProjectId - 设置当前编辑的工程ID
   * @param projectId - 从详情接口返回的实际工程ID (Project.ProjectId)
   * @param templateId - 从详情接口返回的实际模板ID (Project.TemplateId)
   */
  function setEditingProjectId(projectId: string, templateId: string = '') {
    // 使用选择器根据编辑来源选择正确的ID
    const selector = createSelector(editSource.value);
    const currentId = selector.selectId(projectId, templateId);
    editingProjectId.value = currentId;

    const idType = selector.selectText('云剪辑工程ID', '模板工厂模板ID');
    console.log(`📋 设置${idType}:`, currentId);
    console.log(`📋 注意：此ID来自详情接口返回，不是路由参数`);
  }

  /**
   * @action getCurrentProjectInfo - 获取当前工程信息
   */
  function getCurrentProjectInfo() {
    const selector = createSelector(editSource.value);
    return selector.select(
      {
        type: '云剪辑工程',
        id: editingProjectId.value,
        idField: 'ProjectId'
      },
      {
        type: '模板工厂模板',
        id: editingTemplateId.value,
        idField: 'TemplateId'
      }
    );
  }

  /**
   * @action loadProjectMaterials - 加载工程关联素材
   * @param templateData - 可选的模板数据，避免重复API调用
   */
  async function loadProjectMaterials(templateData?: any) {
    console.log(`🔍 当前编辑来源:`, editSource.value);
    console.log(`🔍 工程ID:`, editingProjectId.value);
    console.log(`🔍 模板ID:`, editingTemplateId.value);
    console.log(`🔍 是否有传入模板数据:`, !!templateData);

    projectMaterialsLoading.value = true;

    try {
      // 对于模板工厂模式，优先使用缓存的模板详情数据
      let effectiveTemplateData = templateData;
      if (!effectiveTemplateData && editSource.value === 'template-factory') {
        effectiveTemplateData = getTemplateDetailData();
        console.log(`🔍 使用缓存的模板详情数据:`, !!effectiveTemplateData);
      }

      const result = await loadProjectMaterialsUtil(
        editSource.value,
        editingProjectId.value,
        editingTemplateId.value,
        effectiveTemplateData
      );

      projectMaterials.value = result.materials;

      if (result.success) {
        const selector = createSelector(editSource.value);
        const loadingText = selector.selectText('云剪辑工程素材', '模板工厂素材');
        console.log(`✅ ${loadingText}加载完成，素材数量:`, result.materials.length);
      } else {
        console.error(`❌ 素材加载失败:`, result.message);
        ElMessage.error(`素材加载失败: ${result.message}`);
      }
    } catch (error) {
      console.error(`❌ 素材加载异常:`, error);
      projectMaterials.value = [];
      ElMessage.error('素材加载失败');
    } finally {
      projectMaterialsLoading.value = false;
    }
  }

  /**
   * @action filterProjectMaterialsByType - 根据类型过滤工程素材
   */
  function filterProjectMaterialsByType(mediaType: 'video' | 'audio' | 'image') {
    return projectMaterials.value.filter(media => {
      const type = media.MediaBasicInfo?.MediaType?.toLowerCase();
      return type === mediaType;
    });
  }

  // =======================================================================
  // 返回对象 - 暴露给组件的状态和方法
  // =======================================================================

  return {
    // State
    timelinePlayerRef,
    isVideoPlaying,
    currentTime,
    videoDurationSeconds,
    timeline,
    selectedClip,
    templateMaterials,
    editingTemplateId,
    editingTemplateName,
    editingProjectId,
    projectMaterials,
    projectMaterialsLoading,
    editSource,

    // Getters
    videoDurationFormatted,
    currentTimeFormatted,

    // Actions
    setPlayerRef,
    togglePlayPause,
    handleReset,
    handleSeek,
    updateIsPlaying,
    updateCurrentTime,
    updateDuration,
    handlePause,
    setTimeline,
    setSelectedClip,
    handleCut,
    handleDeleteClip,
    updateClipTime,
    handleSaveTemplate,
    updateTemplateMaterial,
    setTemplateMaterials,
    setEditingTemplateInfo,
    parseTemplateConfigForMaterials: parseTemplateConfigForMaterialsAction,
    convertTimelineToTemplateConfig: convertTimelineToTemplateConfigAction,
    buildClipsParamFromMaterials: buildClipsParamFromMaterialsAction,
    buildUpdateTemplateRequest: buildUpdateTemplateRequestAction,
    validateDataIntegrity: validateDataIntegrityAction,
    syncMaterialsWithTimeline: syncMaterialsWithTimelineAction,

    // 工程相关方法
    setEditSource,
    setEditingProjectId,
    getCurrentProjectInfo,
    loadProjectMaterials,
    filterProjectMaterialsByType,
    setTemplateDetailData,
    getTemplateDetailData,
  };
});