{"include": ["src/**/*", "src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"], "exclude": ["node_modules", "dist", "**/*.js"], "compilerOptions": {"types": ["vite/client"], "target": "ES2020", "experimentalDecorators": true, "noImplicitAny": true, "importHelpers": true, "strict": true, "forceConsistentCasingInFileNames": true, "downlevelIteration": true, "outDir": "dist/compiled", "allowSyntheticDefaultImports": true, "allowJs": true, "jsx": "preserve", "moduleResolution": "node", "module": "ES2022", "lib": ["ES2020", "dom"], "declaration": true, "baseUrl": ".", "paths": {"@": ["src"], "@/*": ["src/*"], "@lib": ["lib"], "@lib/*": ["lib/*"], "vue": ["node_modules/vue"], "tslib": ["node_modules/tslib/tslib.d.ts"]}}}